# **PyRon System Architecture Documentation Index**

This document serves as the main index for all architecture documentation for the PyRon trading system. The system consists of three main repositories working together to provide a comprehensive automated trading platform on the Solana blockchain.

## **Documentation Structure**

### **System-Wide Documentation**
- **[System Overview](./system-overview.md)** - Complete system architecture and design principles
- **[System Architecture Diagrams](./system-architecture-diagrams.md)** - Visual representations of all system components
- **[Security Architecture](./security.md)** - Security considerations and implementations

### **Repository-Specific Documentation**
- **[PyRon WebApp Architecture](./pyron-webapp-architecture.md)** - Frontend React application
- **[PyRon MVP Architecture](./pyron-mvp-architecture.md)** - Backend API service
- **[PyRon Webhook Architecture](./pyron-webhook-architecture.md)** - Webhook processing service

### **Reference Documentation**
- **[Privy.io Architecture Diagrams](./privy-architecture-diagrams.md)** - Visual architecture diagrams for Privy.io
- **[Privy.io Architecture](./privy-architecture.md)** - Wallet infrastructure reference (below)

## **Quick Start Guide**

### **For Developers**
1. Read the [System Overview](./system-overview.md) for high-level understanding
2. Review [System Architecture Diagrams](./system-architecture-diagrams.md) for visual context
3. Dive into specific repository documentation based on your focus area
4. Check [Security Architecture](./security.md) for security considerations

### **For Architects**
1. Start with [System Overview](./system-overview.md) for complete system understanding
2. Review all repository-specific architectures for detailed component design
3. Examine [System Architecture Diagrams](./system-architecture-diagrams.md) for integration patterns
4. Consider scalability and security implications from respective documents

### **For Operations**
1. Focus on deployment sections in [System Overview](./system-overview.md)
2. Review monitoring and observability sections in each repository documentation
3. Understand error handling and recovery procedures
4. Check security requirements and configurations

## **System Components Summary**

### **PyRon WebApp** (Frontend)
- **Technology**: React 18 + TypeScript + Vite
- **Purpose**: User interface for trading operations
- **Key Features**: Wallet integration, trading charts, AI chat, portfolio tracking

### **PyRon MVP** (Backend API)
- **Technology**: Node.js + Express.js + MongoDB
- **Purpose**: Core business logic and data management
- **Key Features**: Authentication, agent management, trading operations, chat system

### **PyRon Webhook** (Signal Processing)
- **Technology**: Node.js + Express.js + Redis + BullMQ
- **Purpose**: Asynchronous processing of trading signals
- **Key Features**: High-throughput webhooks, queue processing, automated trading

---

# **Privy.io Conceptual Architecture Reference**

*The following section contains reference architecture for Privy.io wallet infrastructure, which may be relevant for understanding wallet integration patterns.*

# **Privy.io Conceptual Architecture Diagram Description**

This document outlines the conceptual architecture of Privy.io, a platform providing wallet infrastructure and user onboarding solutions for Web3 applications. It's designed to be a reference for creating a visual architecture diagram.

## **I. Core Principles & Goals:**

* **Simplified User Onboarding:** Abstract away crypto complexities for both new and experienced users.  
* **Developer-Friendly:** Easy-to-integrate SDKs and APIs.  
* **Security First:** Non-custodial wallets, key sharding, secure execution environments (TEEs), and robust security practices.  
* **Flexibility & Composability:** Support for various login methods, wallet types, and blockchain networks.  
* **Scalability:** Infrastructure designed to handle a large number of users and transactions.

## **II. Key Actors & Entities:**

1. **End-Users:**  
   * **New Users:** Individuals new to Web3, typically requiring an embedded, self-custodial wallet created and managed via Privy.  
   * **Existing Crypto Users:** Individuals who already have external wallets (e.g., MetaMask, Phantom) and wish to connect them to the dApp.  
2. **Developer Applications (dApps):**  
   * Web or mobile applications built by developers that integrate Privy to manage user authentication, wallets, and blockchain interactions.  
3. **Privy Platform:**  
   * The suite of backend services, APIs, and SDKs provided by Privy.io.  
4. **Blockchain Networks:**  
   * Various public blockchains (EVM-compatible, Solana, Bitcoin, etc.) where transactions are executed and state is recorded.

## **III. Main Architectural Components & Flows:**

Here's a breakdown of the major components and how they interact:

**(A) Client-Side (Integrated into Developer's dApp)**

1. **Privy SDKs (Software Development Kits):**  
   * **Description:** Libraries for various platforms (React, React Native, Expo, Swift, iOS, etc.) that developers integrate into their applications.  
   * **Responsibilities:**  
     * Render UI components for login, wallet selection, transaction confirmation.  
     * Handle user input for authentication (email, SMS, social, passkey, wallet signature).  
     * Securely communicate with the Privy Backend Platform.  
     * Manage local wallet state (for connected external wallets) or interface with the embedded wallet's secure environment.  
     * Initiate transaction signing requests.  
   * **Sub-components/Features:**  
     * **Authentication UI:** Modals/flows for login (email, SMS, social, passkey).  
     * **Wallet Connector UI:** Interface for users to connect external wallets.  
     * **Embedded Wallet UI:** Interaction points for the embedded wallet.

**(B) Privy Backend Platform**

2. **API Gateway / Load Balancers:**  
   * **Description:** Entry point for all requests from Privy SDKs and developer backends.  
   * **Responsibilities:**  
     * Request routing, authentication, rate limiting, load distribution.  
3. **User Authentication Service:**  
   * **Description:** Manages user identity and authentication processes.  
   * **Responsibilities:**  
     * Verifies credentials for various login methods (email OTP, SMS OTP, OAuth for social logins, WebAuthn for passkeys, SIWE/SWS for wallet logins).  
     * Creates and manages Privy User Accounts, linking various authentication methods to a single user identity.  
     * Issues JWT access tokens to authenticated users for session management.  
   * **Interacts with:** Privy SDKs, Identity Providers (e.g., Google, Apple), SMS/Email gateways.  
4. **Embedded Wallet Management Service:**  
   * **Description:** Core service responsible for the lifecycle and security of Privy-managed embedded wallets. This is central to Privy's non-custodial approach.  
   * **Key Security Mechanisms:**  
     * **Shamir's Secret Sharing (SSS):** Private keys (or their entropy) are generated and immediately sharded into multiple pieces.  
       * Device/Enclave Share: Stored on the user's device (within a sandboxed iframe's local storage for web) or within a TEE for server-controlled wallets. Encrypted.  
       * Auth Share: Stored by Privy's backend, encrypted and accessible only upon successful user authentication.  
       * (Optional) Recovery Share: Potentially another share stored by Privy or a designated recovery service, used for account recovery mechanisms.  
     * **Secure Execution Environments (SEEs):**  
       * Client-Side SEE (Browser iFrame): For user-operated embedded wallets, key shares are temporarily reconstructed and cryptographic operations (like signing) occur within an isolated iframe on the user's device. Privy backend *never* sees the full private key.  
       * Server-Side SEE (Trusted Execution Environments \- TEEs): For server-controlled wallets or advanced use cases. Key shares are reconstructed, and operations are performed within hardware-secured enclaves.  
   * **Responsibilities:**  
     * Orchestrating the generation of new embedded wallets.  
     * Managing the storage and retrieval of encrypted key shares (specifically the Auth Share).  
     * Coordinating with SEEs for key reconstruction and signing operations.  
     * Enforcing wallet policies.  
5. **External Wallet Connector Service:**  
   * **Description:** Manages connections to users' existing external wallets (e.g., MetaMask, Phantom).  
   * **Responsibilities:**  
     * Handles the wallet connection handshake (e.g., via WalletConnect or browser extension providers).  
     * Stores metadata about connected external wallets (e.g., public address, chain).  
     * Facilitates signing requests by relaying them to the user's external wallet software (via the SDK).  
6. **Transaction Orchestration & Signing Service:**  
   * **Description:** Manages the process of constructing, signing, and broadcasting transactions.  
   * **Responsibilities:**  
     * Receives transaction requests from the dApp (via SDK).  
     * **For Embedded Wallets:**  
       * Authenticates the request.  
       * Retrieves the encrypted Auth Share.  
       * Instructs the appropriate SEE (client-side iframe or server-side TEE) to request the Device Share, reconstruct the key, sign the transaction, and return the signed transaction.  
     * **For External Wallets:** Relays the signing request to the user's external wallet via the SDK.  
     * Broadcasts the signed transaction to the relevant blockchain network.  
     * Tracks transaction status.  
7. **Policy Engine:**  
   * **Description:** Allows developers to define and enforce rules on wallet operations.  
   * **Responsibilities:**  
     * Stores policy configurations (e.g., spending limits, whitelisted addresses/contracts, MFA requirements, multi-signature quorum rules).  
     * Evaluates transaction requests against these policies before allowing signing.  
     * Can trigger MFA prompts or require multiple approvals based on policy.  
8. **Session Signers / Server Wallet API:**  
   * **Description:** Enables authorized server-side actions on behalf of a user or for application-owned wallets.  
   * **Responsibilities:**  
     * Manages "session keys" or permissions that allow a developer's backend to initiate transactions through Privy without direct user interaction for each action (after initial user consent).  
     * Provides APIs for developers to manage and use server-controlled TEE-backed wallets (fleet management).  
     * Authenticates requests from developer backends using API keys/secrets.  
9. **Blockchain Interaction Service (Node Interface):**  
   * **Description:** Handles communication with various blockchain networks.  
   * **Responsibilities:**  
     * Broadcasting signed transactions.  
     * Querying blockchain data (e.g., account balances, transaction status, contract state).  
     * Manages connections to blockchain nodes (either self-hosted or via providers like Infura, Alchemy).  
10. **Indexing Service & Webhooks:**  
    * **Description:** Monitors blockchain events and notifies applications.  
    * **Responsibilities:**  
      * Listens for on-chain events relevant to user wallets (e.g., deposits, specific contract interactions).  
      * Provides a webhook system for dApps to subscribe to these events.  
11. **Developer Console & Management API:**  
    * **Description:** Web interface and API for developers to manage their Privy integration.  
    * **Responsibilities:**  
      * App configuration (API keys, callback URLs, UI customization).  
      * User management overview.  
      * Analytics and reporting.  
      * Policy configuration.

**(C) External Systems & Dependencies**

12. **Identity Providers (IdPs):**  
    * Google, Apple, X (Twitter), Discord, GitHub, etc., for social logins.  
    * SMS/Email Gateway services for OTP delivery.  
13. **Blockchain Networks:**  
    * Ethereum, Polygon, Solana, Base, Arbitrum, Bitcoin, etc.  
    * Consensus mechanisms, smart contract execution, distributed ledger.  
14. **Secure Hardware (for TEEs):**  
    * CPU-level secure enclaves (e.g., Intel SGX, AMD SEV) used by Privy's server-side infrastructure.

## **IV. Key Interaction Flows (Examples):**

1. **New User Onboarding (Embedded Wallet):**  
   1. User interacts with dApp UI (via Privy SDK) to sign up (e.g., with email).  
   2. SDK sends auth request to Privy Authentication Service.  
   3. Auth Service verifies email (sends OTP), creates Privy User Account, issues JWT.  
   4. SDK requests embedded wallet creation from Embedded Wallet Management Service.  
   5. Wallet Service orchestrates key generation within the client-side SEE (iframe). Key is sharded.  
      * Device Share stored locally in iframe.  
      * Encrypted Auth Share sent to and stored by Wallet Service.  
   6. Wallet address is returned to dApp.  
2. **Transaction with Embedded Wallet:**  
   1. User initiates an action in the dApp that requires a transaction.  
   2. dApp (via SDK) prepares transaction details and requests signing from Privy.  
   3. SDK communicates with Transaction Orchestration Service.  
   4. Service authenticates user (JWT) and checks policies.  
   5. Orchestration Service instructs client-side SEE (iframe) via SDK:  
      * Retrieve Device Share.  
      * Request encrypted Auth Share from Wallet Service.  
      * Decrypt Auth Share (using a key derived from user's session/authentication).  
      * Reconstruct private key *within the iframe*.  
      * Sign transaction *within the iframe*.  
      * Return signed transaction.  
   6. SDK sends signed transaction to Orchestration Service, which broadcasts it to the blockchain.  
3. **Connect External Wallet:**  
   1. User chooses "Connect Wallet" in dApp UI (Privy SDK).  
   2. SDK presents options (MetaMask, WalletConnect, etc.).  
   3. User selects wallet; SDK initiates connection request with the chosen wallet provider.  
   4. User approves connection in their external wallet.  
   5. External wallet provides public address and signing capabilities to the SDK.  
   6. SDK registers the connected wallet with Privy's External Wallet Connector Service for the user's account.  
4. **Transaction with External Wallet:**  
   1. User initiates an action.  
   2. dApp (via SDK) prepares transaction and requests signing.  
   3. SDK forwards the signing request to the connected external wallet software.  
   4. User confirms and signs the transaction in their external wallet.  
   5. Signed transaction is returned to the SDK.  
   6. SDK sends signed transaction to Privy's Transaction Orchestration Service (or directly to blockchain if configured).

## **V. Security Considerations (Highlighted):**

* **Non-Custodial Nature:** Privy is designed so that neither Privy nor the dApp developer has direct access to users' complete private keys for embedded wallets.  
* **Key Sharding (SSS):** Distributes key components, requiring multiple shares for reconstruction, significantly reducing single points of failure.  
* **Secure Execution Environments:** Operations with reconstructed keys happen in isolated environments (browser iframe or TEEs).  
* **Defense in Depth:** Multiple layers of security including authentication, encryption, policy enforcement, audits, and monitoring.  
* **End-to-End Encryption:** Sensitive data is encrypted in transit and at rest.  
* **Regular Audits & Bug Bounties:** Continuous security validation.  
* **SOC 2 Compliance:** Adherence to industry-standard security and operational practices.

This description provides a foundational understanding of Privy.io's architecture. A visual diagram would typically use boxes for components and arrows to show data flow and interaction pathways.