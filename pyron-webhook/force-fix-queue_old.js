// force-fix-queue.js
const { Queue } = require('bullmq');
const IORedis = require('ioredis');

// Create Redis connection
const connection = new IORedis(process.env.REDIS_URL || "redis://127.0.0.1:6379", {
  maxRetriesPerRequest: null,
  enableReadyCheck: false,
  connectTimeout: 10000,
  commandTimeout: 10000,
  retryStrategy: (times) => {
    return Math.min(times * 200, 3000);
  }
});

// Create queue instance
const alertQueue = new Queue("alertQueue", { connection });

async function forceFixQueue() {
  try {
    console.log("=== Queue Status Before Fix ===");
    const countsBefore = await alertQueue.getJobCounts();
    console.table(countsBefore);

    // Pause the queue to prevent new jobs from being processed
    console.log("\nPausing queue...");
    await alertQueue.pause();
    console.log("Queue paused.");

    // Get the stuck job ID
    const stuckJobId = "20626"; // This is the ID from your output
    console.log(`\nForce removing stuck job ${stuckJobId}...`);

    // Direct Redis commands to remove the job from active set
    await connection.lrem(`bull:alertQueue:active`, 1, stuckJobId);
    console.log(`Removed job ${stuckJobId} from active set.`);

    // Remove job data
    await connection.del(`bull:alertQueue:${stuckJobId}`);
    console.log(`Removed job ${stuckJobId} data.`);

    // Check for any locks related to the job
    console.log("\n=== Checking for Redis Locks ===");
    const locks = await connection.keys("lock:agent:*");
    if (locks.length === 0) {
      console.log("No agent locks found.");
    } else {
      console.log(`Found ${locks.length} agent locks. Removing them...`);
      
      for (const lockKey of locks) {
        console.log(`Removing lock: ${lockKey}`);
        await connection.del(lockKey);
      }
      
      console.log("All agent locks removed.");
    }

    // Resume the queue
    console.log("\nResuming queue...");
    await alertQueue.resume();
    console.log("Queue resumed.");

    // Get queue status after fix
    console.log("\n=== Queue Status After Fix ===");
    const countsAfter = await alertQueue.getJobCounts();
    console.table(countsAfter);

    // Close connections
    await connection.quit();
    console.log("\nQueue fix complete.");
    
    console.log("\n=== Next Steps ===");
    console.log("1. Restart your worker with increased concurrency");
    console.log("2. Monitor the queue to ensure jobs are being processed");
    console.log("3. Consider implementing a job timeout to prevent stuck jobs in the future");
    console.log("\nTo increase worker concurrency, run:");
    console.log("node update-worker-concurrency.js 5");
    console.log("Then restart your application.");
  } catch (error) {
    console.error("Error fixing queue:", error);
    
    // Try to resume the queue if there was an error
    try {
      await alertQueue.resume();
      console.log("Queue resumed after error.");
    } catch (resumeError) {
      console.error("Error resuming queue:", resumeError);
    }
    
    await connection.quit();
    process.exit(1);
  }
}

// Run the fix
forceFixQueue();
