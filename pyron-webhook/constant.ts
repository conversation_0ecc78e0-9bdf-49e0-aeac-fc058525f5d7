import { PublicKey } from "@solana/web3.js";

/**
 * Common token addresses used across the toolkit
 */
export const TOKENS = {
  USDC: {address: new PublicKey("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), decimals: 6},
  USDT: {address: new PublicKey("Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"), decimals: 6 },
  USDS: {address: new PublicKey("USDSwr9ApdHk5bvJKMjzff41FfuX8bSxdKcR81vTwcA"), decimals: 6},
  SOL: {address: new PublicKey("So11111111111111111111111111111111111111112"), decimals: 9},
} as const;

export const PERP_MARKETS={
  SOL: {minAmount: 0.01, marketId: 0 ,minTick: 0.0001},
  ETH: {minAmount: 0.001, marketId: 2 ,minTick: 0.0001},
  BTC: {minAmount: 0.0001, marketId: 1 ,minTick: 0.0001},
  SUI: {minAmount: 1, marketId: 9 ,minTick: 0.0001},
  JTO: {minAmount: 2, marketId: 20 ,minTick: 0.0001},
  RAY: {minAmount: 1, marketId: 56 ,minTick: 0.0001},
  KMNO: {minAmount: 50, marketId: 28 ,minTick: 0.0001},
  IO: {minAmount: 1, marketId: 32,minTick: 0.001},
  RENDER: {minAmount: 2, marketId: 12,minTick: 0.0001},
  JUP: {minAmount: 5, marketId: 24,minTick: 0.0001},
  DOGE: {minAmount: 50, marketId: 7,minTick: 0.00001},
  TAO: {minAmount: 0.01, marketId: 26,minTick: 0.01},
  AI16Z: {minAmount: 5, marketId: 63,minTick: 0.0001},
  GOAT: {minAmount: 10, marketId: 53,minTick: 0.0001},
  PAXG: {minAmount: 0.001, marketId: 73,minTick: 0.001},
} as const;
/**
 * Default configuration options
 * @property {number} SLIPPAGE_BPS - Default slippage tolerance in basis points (300 = 3%)
 * @property {number} TOKEN_DECIMALS - Default number of decimals for new tokens
 */
export const DEFAULT_OPTIONS = {
  SLIPPAGE_BPS: 300,
  TOKEN_DECIMALS: 9,
} as const;

/**
 * Jupiter API URL
 */
export const JUP_API = "https://quote-api.jup.ag/v6";

/**
 * LULO (fka Flexlend) API URL
 */
export const LULO_API = "https://api.flexlend.fi";
export const USDC_MARKET_INDEX = 0;
