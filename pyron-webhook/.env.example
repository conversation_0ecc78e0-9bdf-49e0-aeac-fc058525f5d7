# Pyron Webhook Service Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
PORT=3000
NODE_ENV=production

# =============================================================================
# MONGODB CONFIGURATION (Required)
# =============================================================================
MONGO_HOST=localhost
MONGO_USER=your_mongodb_username
MONGO_PASSWORD=your_mongodb_password
MONGO_DB=pyron-production
MONGO_PORT=27017

# =============================================================================
# REDIS CONFIGURATION (Required for queue processing)
# =============================================================================
REDIS_URL=redis://127.0.0.1:6379
WORKER_CONCURRENCY=5

# =============================================================================
# SOLANA BLOCKCHAIN CONFIGURATION (Required for production)
# =============================================================================
# Your Solana wallet private key in base58 format
ADMIN_KEY=your_solana_private_key_base58

# Solana RPC endpoint (use a reliable provider like Helius, QuickNode, etc.)
RPC_URL=https://your-solana-rpc-endpoint

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# CORS - Allowed origins for cross-origin requests
ALLOWED_ORIGINS=https://yourdomain.com,http://localhost:3000

# IP Filtering - Comma-separated list of allowed IP addresses
# For TradingView webhooks, use these official TradingView IPs:
ALLOWED_IPS=*************,************,*************,***********


# =============================================================================
# DEVELOPMENT NOTES
# =============================================================================
# 1. Never commit this file with real credentials
# 2. For development, you can use Docker for MongoDB and Redis:
#    docker run -d -p 27017:27017 --name mongo -e MONGO_INITDB_ROOT_USERNAME=admin -e MONGO_INITDB_ROOT_PASSWORD=password mongo:latest
#    docker run -d -p 6379:6379 --name redis redis:latest
# 3. For testing, see .env.test for test-specific configuration
# 4. IP filtering is optional - if ALLOWED_IPS is not set, all IPs are allowed
