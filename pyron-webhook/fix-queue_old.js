// fix-queue.js
const { Queue } = require('bullmq');
const IORedis = require('ioredis');

// Create Redis connection
const connection = new IORedis(process.env.REDIS_URL || "redis://127.0.0.1:6379", {
  maxRetriesPerRequest: null,
  enableReadyCheck: false,
  connectTimeout: 10000,
  commandTimeout: 10000,
  retryStrategy: (times) => {
    return Math.min(times * 200, 3000);
  }
});

// Create queue instance
const alertQueue = new Queue("alertQueue", { connection });

async function fixQueue() {
  try {
    console.log("=== Queue Status Before Fix ===");
    const countsBefore = await alertQueue.getJobCounts();
    console.table(countsBefore);

    // Get active jobs
    console.log("\n=== Active Jobs ===");
    const activeJobs = await alertQueue.getActive();
    
    if (activeJobs.length === 0) {
      console.log("No active jobs found.");
    } else {
      console.log(`Found ${activeJobs.length} active jobs.`);
      
      for (const job of activeJobs) {
        console.log(`\nJob ID: ${job.id}`);
        console.log(`Name: ${job.name}`);
        console.log(`Timestamp: ${new Date(job.timestamp).toISOString()}`);
        console.log(`Attempts Made: ${job.attemptsMade}`);
        console.log(`Processing Time: ${Date.now() - job.timestamp}ms`);
        
        // Check if job has been processing for more than 5 minutes
        const processingTime = Date.now() - job.timestamp;
        if (processingTime > 5 * 60 * 1000) {
          console.log(`Job ${job.id} has been processing for ${processingTime/1000} seconds. Moving to failed state.`);
          
          // Move the job to the failed state
          await job.moveToFailed(new Error('Manually failed due to being stuck'), 'manual-intervention');
          console.log(`Job ${job.id} moved to failed state.`);
        }
      }
    }

    // Check for locks in Redis
    console.log("\n=== Redis Locks ===");
    const locks = await connection.keys("lock:agent:*");
    if (locks.length === 0) {
      console.log("No agent locks found.");
    } else {
      console.log(`Found ${locks.length} agent locks. Removing them...`);
      
      for (const lockKey of locks) {
        console.log(`Removing lock: ${lockKey}`);
        await connection.del(lockKey);
      }
      
      console.log("All agent locks removed.");
    }

    // Get queue status after fix
    console.log("\n=== Queue Status After Fix ===");
    const countsAfter = await alertQueue.getJobCounts();
    console.table(countsAfter);

    // Close connections
    await connection.quit();
    console.log("\nQueue fix complete.");
    
    console.log("\n=== Next Steps ===");
    console.log("1. Restart your worker with increased concurrency");
    console.log("2. Monitor the queue to ensure jobs are being processed");
    console.log("3. Consider implementing a job timeout to prevent stuck jobs in the future");
  } catch (error) {
    console.error("Error fixing queue:", error);
    await connection.quit();
    process.exit(1);
  }
}

// Run the fix
fixQueue();
