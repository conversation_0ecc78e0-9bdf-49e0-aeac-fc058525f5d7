// monitor-redis.js
const Redis = require('ioredis');
const fs = require('fs');

// Create Redis client
const redis = new Redis({
  host: process.env.REDIS_HOST || '127.0.0.1',
  port: process.env.REDIS_PORT || 6379,
  maxRetriesPerRequest: null,
  enableReadyCheck: false,
  connectTimeout: 10000,
  commandTimeout: 10000,
  retryStrategy: (times) => {
    return Math.min(times * 200, 3000);
  }
});

// Log file
const logFile = 'redis-monitor.log';

// Clear log file
fs.writeFileSync(logFile, '');

// Log function
const log = (message) => {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} - ${message}\n`;
  console.log(logMessage.trim());
  fs.appendFileSync(logFile, logMessage);
};

// Monitor Redis info
const monitorRedis = async () => {
  try {
    const info = await redis.info();
    const clients = await redis.info('clients');
    const memory = await redis.info('memory');
    const stats = await redis.info('stats');
    
    // Parse info sections
    const parseSection = (section) => {
      const lines = section.split('\n');
      const result = {};
      
      for (const line of lines) {
        if (line && !line.startsWith('#')) {
          const parts = line.split(':');
          if (parts.length === 2) {
            result[parts[0]] = parts[1].trim();
          }
        }
      }
      
      return result;
    };
    
    const clientsInfo = parseSection(clients);
    const memoryInfo = parseSection(memory);
    const statsInfo = parseSection(stats);
    
    // Log key metrics
    log('=== Redis Health Check ===');
    log(`Connected clients: ${clientsInfo.connected_clients}`);
    log(`Blocked clients: ${clientsInfo.blocked_clients}`);
    log(`Used memory: ${memoryInfo.used_memory_human}`);
    log(`Memory fragmentation ratio: ${memoryInfo.mem_fragmentation_ratio}`);
    log(`Total commands processed: ${statsInfo.total_commands_processed}`);
    log(`Keyspace hits: ${statsInfo.keyspace_hits}`);
    log(`Keyspace misses: ${statsInfo.keyspace_misses}`);
    log(`Rejected connections: ${statsInfo.rejected_connections}`);
    
    // Check for any locks in the system
    const locks = await redis.keys('lock:*');
    if (locks.length > 0) {
      log(`Active locks: ${locks.length}`);
      for (const lock of locks) {
        const ttl = await redis.ttl(lock);
        log(`  - ${lock} (TTL: ${ttl}s)`);
      }
    } else {
      log('No active locks found');
    }
    
    // Check for any BullMQ jobs
    const bullKeys = await redis.keys('bull:*');
    if (bullKeys.length > 0) {
      log(`BullMQ keys: ${bullKeys.length}`);
      
      // Group keys by queue
      const queueGroups = {};
      for (const key of bullKeys) {
        const parts = key.split(':');
        if (parts.length >= 2) {
          const queueName = parts[1];
          if (!queueGroups[queueName]) {
            queueGroups[queueName] = [];
          }
          queueGroups[queueName].push(key);
        }
      }
      
      // Log queue information
      for (const [queue, keys] of Object.entries(queueGroups)) {
        log(`  - Queue "${queue}": ${keys.length} keys`);
      }
    } else {
      log('No BullMQ keys found');
    }
    
  } catch (error) {
    log(`Error monitoring Redis: ${error.message}`);
  }
};

// Run monitor every 5 seconds
log('Starting Redis monitoring...');
monitorRedis();
const interval = setInterval(monitorRedis, 5000);

// Handle process termination
process.on('SIGINT', async () => {
  clearInterval(interval);
  log('Stopping Redis monitoring...');
  await redis.quit();
  process.exit(0);
});

// Keep process running
process.stdin.resume();

log('Redis monitor running. Press Ctrl+C to stop.');
