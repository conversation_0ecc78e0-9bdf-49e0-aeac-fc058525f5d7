// empty-queue.js
// <PERSON>ript to completely empty all jobs from the BullMQ queue
const { Queue } = require('bullmq');
const IORedis = require('ioredis');

// Create Redis connection with robust settings
const connection = new IORedis(process.env.REDIS_URL || "redis://127.0.0.1:6379", {
  maxRetriesPerRequest: null,
  enableReadyCheck: false,
  connectTimeout: 10000,      // 10 seconds
  commandTimeout: 10000,      // 10 seconds
  retryStrategy: (times) => {
    // Exponential backoff with a maximum of 3 seconds
    return Math.min(times * 200, 3000);
  }
});

// Create queue instance
const alertQueue = new Queue("alertQueue", { connection });

async function emptyQueue() {
  try {
    console.log("=== Queue Status Before Emptying ===");
    const countsBefore = await alertQueue.getJobCounts();
    console.table(countsBefore);
    
    // Calculate total jobs
    const totalJobs = Object.values(countsBefore).reduce((sum, count) => sum + count, 0);
    console.log(`Total jobs in queue: ${totalJobs}`);

    if (totalJobs === 0) {
      console.log("Queue is already empty. Nothing to do.");
      await connection.quit();
      return;
    }

    // Pause the queue to prevent new jobs from being processed during cleanup
    console.log("\nPausing queue...");
    await alertQueue.pause();
    console.log("Queue paused successfully.");

    // Get all jobs from different states
    console.log("\nFetching all jobs from queue...");
    const waitingJobs = await alertQueue.getWaiting();
    console.log(`Found ${waitingJobs.length} waiting jobs`);
    
    const activeJobs = await alertQueue.getActive();
    console.log(`Found ${activeJobs.length} active jobs`);
    
    const delayedJobs = await alertQueue.getDelayed();
    console.log(`Found ${delayedJobs.length} delayed jobs`);
    
    const completedJobs = await alertQueue.getCompleted();
    console.log(`Found ${completedJobs.length} completed jobs`);
    
    const failedJobs = await alertQueue.getFailed();
    console.log(`Found ${failedJobs.length} failed jobs`);

    // Combine all jobs
    const allJobs = [
      ...waitingJobs,
      ...activeJobs,
      ...delayedJobs,
      ...completedJobs,
      ...failedJobs
    ];

    console.log(`\nRemoving ${allJobs.length} jobs individually...`);
    
    // Remove all jobs one by one
    let removedCount = 0;
    let errorCount = 0;
    
    for (const job of allJobs) {
      try {
        await job.remove();
        removedCount++;
        
        // Show progress every 100 jobs
        if (removedCount % 100 === 0 || removedCount === allJobs.length) {
          console.log(`Removed ${removedCount}/${allJobs.length} jobs...`);
        }
      } catch (err) {
        errorCount++;
        console.log(`Error removing job ${job?.id}: ${err.message}`);
      }
    }
    
    console.log(`\nRemoved ${removedCount} jobs with ${errorCount} errors.`);

    // Check for any remaining jobs
    const countsAfterRemoval = await alertQueue.getJobCounts();
    const remainingJobs = Object.values(countsAfterRemoval).reduce((sum, count) => sum + count, 0);
    
    if (remainingJobs > 0) {
      console.log(`\nThere are still ${remainingJobs} jobs in the queue. Using obliterate to force clean...`);
    } else {
      console.log("\nAll jobs removed successfully. Using obliterate to clean queue metadata...");
    }

    // Obliterate the queue with force option to remove any remaining jobs and metadata
    console.log("\nObliterating queue...");
    await alertQueue.obliterate({ force: true });
    console.log("Queue obliterated successfully.");

    // Check for any locks in Redis that might be related to jobs
    console.log("\n=== Checking for Redis Locks ===");
    const locks = await connection.keys("lock:agent:*");
    if (locks.length === 0) {
      console.log("No agent locks found.");
    } else {
      console.log(`Found ${locks.length} agent locks. Removing them...`);
      
      for (const lockKey of locks) {
        console.log(`Removing lock: ${lockKey}`);
        await connection.del(lockKey);
      }
      
      console.log("All agent locks removed.");
    }

    // Resume the queue so it can accept new jobs
    console.log("\nResuming queue...");
    await alertQueue.resume();
    console.log("Queue resumed successfully.");

    // Final check
    console.log("\n=== Queue Status After Emptying ===");
    const countsAfter = await alertQueue.getJobCounts();
    console.table(countsAfter);
    
    const finalTotal = Object.values(countsAfter).reduce((sum, count) => sum + count, 0);
    if (finalTotal === 0) {
      console.log("\n✅ Queue is now completely empty!");
    } else {
      console.log(`\n⚠️ There are still ${finalTotal} jobs in the queue. You may need to restart the worker or run this script again.`);
    }

  } catch (error) {
    console.error("\n❌ Error emptying queue:", error);
  } finally {
    // Always close the Redis connection
    console.log("\nClosing Redis connection...");
    await connection.quit();
    console.log("Done!");
  }
}

// Run the empty queue function
console.log("Starting queue emptying process...");
emptyQueue().catch(console.error);
