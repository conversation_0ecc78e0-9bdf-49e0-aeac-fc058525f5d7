# Pyron Webhook Service

A robust webhook service built with Express.js and MongoDB for handling webhook events and processing them asynchronously.

## Features

- Express.js based webhook endpoint
- MongoDB integration for data persistence
- Redis-based queue system for asynchronous processing
- IP filtering middleware for security
- CORS enabled for secure cross-origin requests
- Environment-based configuration
- TypeScript support
- Comprehensive test suite (unit and integration tests)
- Solana blockchain integration via Drift protocol

## Prerequisites

- Node.js (v18 or higher recommended)
- MongoDB instance (local or remote)
- Redis instance (for queue processing)
- npm or yarn package manager

## Environment Variables

Create a `.env` file in the root directory with the following variables:

### Required Variables

```env
# Server Configuration
PORT=3000

# MongoDB Configuration (Required)
MONGO_USER=your_mongodb_username
MONGO_PASSWORD=your_mongodb_password
MONGO_HOST=your_mongodb_host
MONGO_DB=your_database_name
MONGO_PORT=27017

# Solana Configuration (Required for production)
ADMIN_KEY=your_solana_private_key_base58
RPC_URL=https://your-solana-rpc-endpoint

# Redis Configuration (Required for queue processing)
REDIS_URL=redis://127.0.0.1:6379
WORKER_CONCURRENCY=5
```

### Security Variables

```env
# CORS Configuration
ALLOWED_ORIGINS=https://yourdomain.com,http://localhost:3000

# IP Filtering (Optional - if not set, all IPs are allowed)
# For TradingView webhooks, use the official TradingView IPs:
ALLOWED_IPS=*************,************,*************,***********

```

### Optional Variables

```env

# Development/Testing
NODE_ENV=production
```

### TradingView Webhook IPs

For production use with TradingView webhooks, configure these official TradingView IP addresses:
- `*************`
- `************`
- `*************`
- `***********`

**Security Note**: The IP filtering middleware uses the `ALLOWED_IPS` environment variable. If this variable is not set, the service will accept requests from any IP address (fail-open approach for development convenience).

## Installation

1. Clone the repository:
```bash
git clone https://gitlab.com/pyron2/pyron-webhook.git
cd pyron-webhook
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your configuration
nano .env
```

4. Set up development services (MongoDB and Redis):
```bash
# Using Docker (recommended for development)
docker run -d -p 27017:27017 --name mongo \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=password \
  mongo:latest

docker run -d -p 6379:6379 --name redis redis:latest
```

## Project Structure

```
src/
├── controller/     # Request handlers and business logic
├── databaseModels/ # MongoDB schema definitions
├── queue/         # Queue processing logic
├── router/        # API route definitions
└── utils/         # Utility functions and helpers
```

## API Endpoints

### Webhook Endpoint
- `POST /webhook/:agentId` - Endpoint for receiving webhook events for a specific agent

**Request Format:**
```json
{
  "action": "confirmationMinute",
  "signalName": "buy",
  "ticker": "SOL-PERP"
}
```

**Security Features:**
- IP filtering based on `ALLOWED_IPS` environment variable
- Request validation middleware
- Signal threshold filtering to reduce queue load

## Development

### Running the Application

**Development mode:**
```bash
npm run start
```

**Running tests:**
```bash
# Run all tests
npm test

# Run only unit tests (fast)
npm run test:unit

# Run only integration tests (requires MongoDB + Redis)
npm run test:integration
```

### Development Setup

For detailed testing setup and development guidelines, see [TEST_README.md](./TEST_README.md).

## Production Deployment

### Environment Setup

1. Ensure all required environment variables are set
2. Use production-grade MongoDB and Redis instances
3. Configure proper IP filtering for security
4. Set up monitoring and logging

### Starting the Server

```bash
npm run start
```

The server will start on the port specified in the `PORT` environment variable (default: 3000).

### Security Considerations

- **IP Filtering**: Configure `ALLOWED_IPS` with TradingView's official IP addresses
- **CORS**: Set `ALLOWED_ORIGINS` to your frontend domains
- **Database Security**: Use strong credentials and network isolation

## Logging

The application uses Winston for structured logging. Logs are written to:
- Console (development)
- `logs/app.log` (production)
- `logs/agents/` (agent-specific logs)

Log levels: `error`, `warn`, `info`, `debug`
