import express, { Request, Response } from "express";
import { alertQueue } from "../queue/queue";
import { logInfo, logError } from "../utils/logger";
import webhookFilterMiddleware from "../middleware/webhookFilterMiddleware";
import ipFilterMiddleware from "../middleware/ipFilterMiddleware";

const router = express.Router();

// POST /webhook/:agentId
// Apply IP filter middleware first, then webhook filter middleware before the main handler
router.post("/:agentId", ipFilterMiddleware, webhookFilterMiddleware, (req: Request, res: Response) => {
  const agentId = req.params.agentId;
  const alertData = req.body;
  const { action, signalName, ticker } = alertData;

  logInfo(`Received webhook for agentId=${agentId}`, {
    action,
    signalName,
    ticker
  });

  // Handle the webhook asynchronously
  (async () => {
    try {
      // Add the webhook request to the queue (filtering is now handled by middleware)
      await alertQueue.add('processAlert', { agentId, alertData });

      // Respond immediately to acknowledge receipt
      logInfo(`Webhook for agentId=${agentId} added to the queue`);
      res.status(200).send("Webhook added to the queue for processing");
    } catch (error) {
      logError(`Error processing webhook for agentId=${agentId}:`, error);
      res.status(500).send("Error processing webhook");
    }
  })();
});

export default router;
