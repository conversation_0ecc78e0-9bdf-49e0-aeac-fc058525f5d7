// controllers/webhook.controller.ts
import { Request, Response } from "express";
import { alertQueue } from "../queue/queue" // The queue we set up
import dotenv from "dotenv";
import { logInfo, logError } from "../utils/logger";

dotenv.config();

/**
 * Handle webhook requests
 * Note: This function assumes filtering has already been applied by the webhookFilterMiddleware
 */
export async function handleWebhook(req: Request, res: Response) {
  try {
    const agentId = req.params.agentId;       // e.g. from URL path
    const alertData = req.body;
    const { action, signalName, ticker } = alertData;

    logInfo(`Handling webhook for agentId=${agentId}`, {
      action,
      signalName,
      ticker
    });

    // Push the job to Redis queue if it wasn't filtered
    await alertQueue.add("processAlert", {
      agentId,
      alertData,
    });

    // Immediately respond with success
    logInfo(`Alert for agentId=${agentId} queued successfully`);
    return res.status(200).send("Alert queued");
  } catch (error) {
    logError("Error processing webhook:", error);
    return res.status(500).send("Internal Server Error");
  }
}
