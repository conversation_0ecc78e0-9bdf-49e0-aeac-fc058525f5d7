import { Connection, PublicKey } from "@solana/web3.js";
import { Wallet, DriftClient, DRIFT_PROGRAM_ID } from "@drift-labs/sdk";
import { logInfo, logError } from "../../utils/logger";

/**
 * Creates a Drift client with the provided connection, keypair, and authority
 * @param connection The Solana connection
 * @param keypair The wallet keypair
 * @param authority The authority public key
 * @returns A DriftClient instance or null if creation fails
 */
export async function createClient(connection: Connection, keypair: any, authority: PublicKey) {
  try {
    const wallet = new Wallet(keypair);
    // Log only essential information to reduce memory usage
    logInfo("Creating drift client with wallet", wallet.publicKey.toBase58());

    // Create the drift client with optimized configuration
    const driftClient = new DriftClient({
      connection,
      wallet,
      env: 'mainnet-beta',
      programID: new PublicKey(DRIFT_PROGRAM_ID),
      opts: {
        commitment: 'confirmed',
      },
      authority: authority,
      includeDelegates: true,
    });

    logInfo("Drift client created successfully");
    return driftClient;
  } catch (error: any) {
    // Log detailed error information for debugging
    logError("Error creating drift client:", error?.message);

    // Return null instead of undefined for more explicit error handling
    return null;
  }
}