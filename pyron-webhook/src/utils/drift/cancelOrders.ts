import { Connection, PublicKey } from "@solana/web3.js";
import { createClient } from "./getClient";
import { logAgentInfo, logAgentWarn, logAgentError } from "../../utils/logger";

export async function cancelOrders(agent: any, marketIndex: number, connection: Connection, signer: any, authority: PublicKey, side: string) {
    const agentId = agent.botId || agent.chatId;
    let driftClient = null;

    try {
        driftClient = await createClient(connection, signer, authority);
        if (!driftClient) {
            logAgentWarn(agentId, "Drift client is not initialized");
            return;
        }

        await driftClient.subscribe();

        // fetch agent's driftuser
        const user = driftClient.getUser(agent.number, new PublicKey(agent.pubkey))
        if (!user) {
            logAgentWarn(agentId, "Cannot fetch user");
            return;
        }

        // Gather open orders
        const placedOrders = user.getOpenOrders();

        // Filter orders efficiently to reduce memory usage
        const openOrdersShort = placedOrders.filter(
            (order) => order.direction === "short" &&
                      order.status === "open" &&
                      order.marketType === "perp" &&
                      order.marketIndex === marketIndex
        );

        const openOrdersLong = placedOrders.filter(
            (order) => order.direction === "long" &&
                      order.status === "open" &&
                      order.marketType === "perp" &&
                      order.marketIndex === marketIndex
        );

        // Determine which orders to cancel
        let openOrders = [];
        if (side === "short") {
            openOrders = openOrdersShort;
            logAgentInfo(agentId, `Canceling ${openOrdersShort.length} short orders for market ${marketIndex}`);
        } else if (side === "long") {
            openOrders = openOrdersLong;
            logAgentInfo(agentId, `Canceling ${openOrdersLong.length} long orders for market ${marketIndex}`);
        } else {
            openOrders = [...openOrdersShort, ...openOrdersLong];
            logAgentInfo(agentId, `Canceling ${openOrders.length} orders (${openOrdersShort.length} short, ${openOrdersLong.length} long) for market ${marketIndex}`);
        }

        // Cancel all open orders
        for (const order of openOrders) {
            // Log only essential order information
            logAgentInfo(agentId, `Canceling order ${order.orderId}`, {
                direction: order.direction,
                marketIndex: order.marketIndex
            });
            await driftClient.cancelOrder(order.orderId, {}, agent.number);
        }

        logAgentInfo(agentId, `Finished canceling orders for market ${marketIndex}`);
        return;
    } catch (error) {
        logAgentError(agentId, "Error canceling orders:", error);
        throw error; // Re-throw to be handled by the caller
    } finally {
        // Ensure drift client is always unsubscribed to prevent memory leaks
        if (driftClient) {
            try {
                await driftClient.unsubscribe();
                logAgentInfo(agentId, "Drift client unsubscribed after order cancellation");
            } catch (unsubError) {
                logAgentError(agentId, "Error unsubscribing drift client:", unsubError);
            }
        }
    }
}