import axios from 'axios';
import { logInfo, logError, logWarn } from "../../utils/logger";
// Helper function to fetch the current price from the market's order book
export async function getCurrentPrice(marketName: string): Promise<number> {
  const url = `https://dlob.drift.trade/l2?marketName=${marketName}-PERP&depth=10&includeOracle=true&includeVamm=true`;
  logInfo(`Fetching current price for ${marketName} from ${url}`);

  try {
    // Use timeout to prevent hanging requests
    const response = await axios.get(url, { timeout: 10000 });
    const { bids, asks } = response.data;

    if (!bids?.length || !asks?.length) {
      logWarn(`No bids or asks found for ${marketName}`);
      throw new Error(`No bids or asks found for ${marketName}`);
    }

    // Extract only what we need to reduce memory usage
    const lastBidPrice = parseFloat(bids[0].price);
    const lastAskPrice = parseFloat(asks[0].price);

    // Calculate the mean price between the last bid and last ask
    const price = (lastBidPrice + lastAskPrice) / 2 / 10**6;
    logInfo(`Current price for ${marketName}: ${price}`);

    return price;
  } catch (error) {
    logError(`Failed to fetch market price for ${marketName}:`, error);
    throw new Error(`Failed to fetch market price for ${marketName}`);
  }
}

