import { Transaction } from "@solana/web3.js";
import { Connection } from "@solana/web3.js";
import base58 from "bs58";
import { logInfo, logError, logWarn } from "../utils/logger";

export async function getPriorityFee(transaction:Transaction) {
    const heliusURL = process.env.RPC_URL;
    if (!heliusURL) {
        throw new Error("RPC_URL is not set");
    }

    try {
        const response = await fetch(heliusURL, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                jsonrpc: "2.0",
                id: "1",
                method: "getPriorityFeeEstimate",
                params: [
                    {
                        transaction: base58.encode(transaction.serialize()), // Pass the serialized transaction in Base58
                        options: {
                            // "recommended": true,
                            "priorityLevel": "High"
                        },
                    },
                ],
            }),
        });

        const result = await response.json();
        logInfo("Priority fee estimation result", result);

        if (!result.result || !result.result.priorityFeeEstimate) {
            logWarn("Invalid priority fee result", result);
            return 1000000; // Default value if estimation fails
        }

        return result.result.priorityFeeEstimate;
    } catch (error) {
        logError("Error estimating priority fee", error);
        return 1000000; // Default value if estimation fails
    }
}
