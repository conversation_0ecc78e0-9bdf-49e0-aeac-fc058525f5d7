import winston from 'winston';
import fs from 'fs';
import path from 'path';

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create agents logs directory if it doesn't exist
const agentsLogsDir = path.join(logsDir, 'agents');
if (!fs.existsSync(agentsLogsDir)) {
  fs.mkdirSync(agentsLogsDir, { recursive: true });
}

// Common log format for all loggers
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.printf(({ timestamp, level, message }) => {
    return `${timestamp} [${level.toUpperCase()}]: ${message}`;
  })
);

// Create a default logger for general application logs
const defaultLogger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({
      filename: path.join(logsDir, 'app.log'),
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
    }),
  ],
});

// Cache for agent-specific loggers with a maximum size to prevent memory leaks
const MAX_LOGGER_CACHE_SIZE = 100; // Maximum number of loggers to keep in cache
const agentLoggers: Record<string, winston.Logger> = {};
const agentLoggerTimestamps: Record<string, number> = {}; // Track last usage time

/**
 * Get or create a logger for a specific agent
 * @param agentId The agent's ID or name
 * @returns A winston logger instance for the agent
 */
export function getAgentLogger(agentId: string): winston.Logger {
  // Update the timestamp for this logger
  agentLoggerTimestamps[agentId] = Date.now();

  if (!agentLoggers[agentId]) {
    // Check if we need to clean up the cache
    const loggerCount = Object.keys(agentLoggers).length;
    if (loggerCount >= MAX_LOGGER_CACHE_SIZE) {
      // Remove the oldest logger
      const oldestAgentId = Object.keys(agentLoggerTimestamps).reduce((oldest, current) => {
        return agentLoggerTimestamps[current] < agentLoggerTimestamps[oldest] ? current : oldest;
      }, Object.keys(agentLoggerTimestamps)[0]);

      // Close the logger to free resources
      if (agentLoggers[oldestAgentId]) {
        agentLoggers[oldestAgentId].close();
        delete agentLoggers[oldestAgentId];
        delete agentLoggerTimestamps[oldestAgentId];
      }
    }

    // Create a new logger for this agent
    agentLoggers[agentId] = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: logFormat,
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.printf(({ timestamp, level, message }) => {
              return `${timestamp} [${level.toUpperCase()}] [Agent: ${agentId}]: ${message}`;
            })
          ),
        }),
        new winston.transports.File({
          filename: path.join(agentsLogsDir, `${agentId}.log`),
          maxsize: 5 * 1024 * 1024, // 5MB
          maxFiles: 3,
        }),
      ],
    });
  }

  return agentLoggers[agentId];
}

/**
 * Log a message for a specific agent
 * @param agentId The agent's ID or name
 * @param level The log level
 * @param message The message to log
 * @param meta Additional metadata to log
 */
export function logAgent(
  agentId: string,
  level: 'info' | 'warn' | 'error' | 'debug',
  message: string,
  meta?: any
) {
  const logger = getAgentLogger(agentId);

  if (meta) {
    logger[level](`${message} ${JSON.stringify(meta)}`);
  } else {
    logger[level](message);
  }
}

/**
 * Log an info message for a specific agent
 * @param agentId The agent's ID or name
 * @param message The message to log
 * @param meta Additional metadata to log
 */
export function logAgentInfo(agentId: string, message: string, meta?: any) {
  logAgent(agentId, 'info', message, meta);
}

/**
 * Log a warning message for a specific agent
 * @param agentId The agent's ID or name
 * @param message The message to log
 * @param meta Additional metadata to log
 */
export function logAgentWarn(agentId: string, message: string, meta?: any) {
  logAgent(agentId, 'warn', message, meta);
}

/**
 * Log an error message for a specific agent
 * @param agentId The agent's ID or name
 * @param message The message to log
 * @param meta Additional metadata to log
 */
export function logAgentError(agentId: string, message: string, meta?: any) {
  logAgent(agentId, 'error', message, meta);
}

/**
 * Log a debug message for a specific agent
 * @param agentId The agent's ID or name
 * @param message The message to log
 * @param meta Additional metadata to log
 */
export function logAgentDebug(agentId: string, message: string, meta?: any) {
  logAgent(agentId, 'debug', message, meta);
}

/**
 * Log a message to the default application logger
 * @param level The log level
 * @param message The message to log
 * @param meta Additional metadata to log
 */
export function log(
  level: 'info' | 'warn' | 'error' | 'debug',
  message: string,
  meta?: any
) {
  if (meta) {
    defaultLogger[level](`${message} ${JSON.stringify(meta)}`);
  } else {
    defaultLogger[level](message);
  }
}

/**
 * Log an info message to the default application logger
 * @param message The message to log
 * @param meta Additional metadata to log
 */
export function logInfo(message: string, meta?: any) {
  log('info', message, meta);
}

/**
 * Log a warning message to the default application logger
 * @param message The message to log
 * @param meta Additional metadata to log
 */
export function logWarn(message: string, meta?: any) {
  log('warn', message, meta);
}

/**
 * Log an error message to the default application logger
 * @param message The message to log
 * @param meta Additional metadata to log
 */
export function logError(message: string, meta?: any) {
  log('error', message, meta);
}

/**
 * Log a debug message to the default application logger
 * @param message The message to log
 * @param meta Additional metadata to log
 */
export function logDebug(message: string, meta?: any) {
  log('debug', message, meta);
}

/**
 * Clean up all loggers to prevent memory leaks
 * Call this when shutting down the application
 */
export function cleanupLoggers() {
  // Close the default logger
  defaultLogger.close();

  // Close all agent loggers
  Object.values(agentLoggers).forEach(logger => {
    logger.close();
  });

  // Clear the caches
  Object.keys(agentLoggers).forEach(key => {
    delete agentLoggers[key];
    delete agentLoggerTimestamps[key];
  });
}

export default {
  getAgentLogger,
  logAgent,
  logAgentInfo,
  logAgentWarn,
  logAgentError,
  logAgentDebug,
  log,
  logInfo,
  logWarn,
  logError,
  logDebug,
  defaultLogger,
  cleanupLoggers,
};
