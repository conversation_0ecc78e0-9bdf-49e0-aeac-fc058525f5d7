// src/queue/worker.ts
import { Worker, Job } from "bullmq";
import IORedis from "ioredis";
import Redlock from "redlock";
import { alertQueue } from "./queue";
import { Connection } from "@solana/web3.js";
import Agent from "../databaseModels/agent";
import { createKeypairFromSecretKey } from "../utils/createKeypairFromSecretKey";
import { processAlert } from "../utils/processAlert";
import mongoose from "mongoose";
import { logAgentError, logAgentInfo, logAgentWarn, logInfo } from "../utils/logger";

logInfo("Starting worker.ts");

/**
 * Creates and returns a worker for processing webhook alerts
 * This function is used both in production and tests
 */
export function createWorker(options: {
  concurrency?: number;
  redisUrl?: string;
  isTest?: boolean;
} = {}) {
  const {
    concurrency = process.env.NODE_ENV === 'test' ? 3 : (parseInt(process.env.WORKER_CONCURRENCY || '5') || 5),
    redisUrl = process.env.REDIS_URL || "redis://127.0.0.1:6379",
    isTest = process.env.NODE_ENV === 'test'
  } = options;

  logInfo(`Creating worker with concurrency ${concurrency} (${isTest ? 'test' : 'production'} mode)`);

  // Only require these environment variables in production
  if (!isTest) {
    if (!process.env.ADMIN_KEY || !process.env.RPC_URL) {
      throw new Error("ADMIN_KEY and RPC_URL must be set in the env variables");
    }
  }

  const wallet = isTest
    ? { publicKey: { toBase58: () => '7v91N7iZ9mNicL8WfG6cgSCKyRXydQjLh6UYBWwm6y1M' } }
    : createKeypairFromSecretKey(process.env.ADMIN_KEY || '');

  const connectionRpc = new Connection(process.env.RPC_URL || "", {
    commitment: "confirmed",
    confirmTransactionInitialTimeout: 7000,
  });

  // Re-use the same Redis connection for both BullMQ and Redlock
  const connection = new IORedis(redisUrl, {
    maxRetriesPerRequest: null,
    enableReadyCheck: false,
    connectTimeout: 10000,    // Increase connection timeout
    disconnectTimeout: 5000,  // Increase disconnect timeout
    retryStrategy: (times) => {
      // Exponential backoff with a maximum of 2 seconds
      return Math.min(times * 100, 2000);
    }
  });

  // Create a Redlock instance with better retry settings
  const redlock = new Redlock([connection], {
    // Use different settings for test vs production
    retryCount: isTest ? 50 : 10,         // More retries in test, fewer in production
    retryDelay: isTest ? 800 : 400,       // Longer delay in test
    retryJitter: isTest ? 800 : 400,      // More jitter in test
    automaticExtensionThreshold: 2000,    // Time before lock extension
    driftFactor: 0.2,                     // Allow for clock drift
  });

  const worker = new Worker(
    alertQueue.name,
    async (job: Job) => {
      const { agentId, alertData } = job.data;
      // Reduce logging to essential information only
      logAgentInfo(agentId, `Processing job ${job.id} for agent ${agentId} at ${new Date().toISOString()}`);

      // Log only key alert data properties instead of the entire object
      const { action, signalName, ticker } = alertData;
      logAgentInfo(agentId, `Alert data: action=${action}, signal=${signalName}, ticker=${ticker}`);

      // For test environments, log more details to help with debugging
      if (isTest) {
        console.log(`Processing job ${job.id} for agent ${agentId} with data:`, JSON.stringify(alertData));
      }

      const lockKey = `lock:agent:${agentId}`;
      const lockTtl = isTest ? 120_000 : 60_000; // 2 min for test, 1 min for production
      let lock: any = null; // Using any type to avoid TypeScript issues with Redlock
      let lockAttempts = 0;
      const maxLockAttempts = isTest ? 10 : 5; // More attempts in test

      try {
        logAgentInfo(agentId, `Starting job processing`);

        // Create a timeout promise to prevent jobs from running too long
        const timeoutMs = isTest ? 60_000 : 300_000; // 1 minute for test, 5 minutes for production

        // Use Promise.race to implement a timeout for the entire job processing
        await Promise.race([
          // Main job processing logic
          (async () => {
            // Try to acquire the lock with multiple attempts if needed
            while (!lock && lockAttempts < maxLockAttempts) {
              try {
                lockAttempts++;

                // Use an exponential backoff strategy
                const backoffDelay = Math.min(Math.pow(2, lockAttempts) * 100, 3000);
                const jitter = Math.floor(Math.random() * 500);
                const totalDelay = backoffDelay + jitter;

                logAgentInfo(agentId, `Attempt ${lockAttempts} to acquire lock with delay ${totalDelay}ms`);

                // Wait before trying to acquire the lock to reduce contention
                if (lockAttempts > 1) {
                  await new Promise(resolve => setTimeout(resolve, totalDelay));
                }

                // Try to acquire the lock
                lock = await redlock.acquire([lockKey], lockTtl);
                logAgentInfo(agentId, `Lock acquired on attempt ${lockAttempts}`);
                break;
              } catch (lockErr) {
                if (lockAttempts >= maxLockAttempts) {
                  logAgentError(agentId, `Failed to acquire lock after ${maxLockAttempts} attempts`, lockErr);

                  // Log more details about the error
                  if (typeof lockErr === 'object' && lockErr !== null && 'name' in lockErr && lockErr.name === 'ExecutionError') {
                    const execError = lockErr as any; // Type assertion for the error
                    logAgentError(agentId, `Redlock execution error details`, {
                      message: execError.message || 'Unknown error',
                      attempts: execError.attempts?.length || 0,
                      name: execError.name
                    });
                  }

                  throw lockErr;
                }
                logAgentInfo(agentId, `Lock acquisition attempt ${lockAttempts} failed, retrying...`);
              }
            }

            // Continue with the rest of the job processing
            // Check MongoDB connection and process the alert
            // This code will be executed after the lock is acquired

            // Check MongoDB connection status
            const readyState = mongoose.connection.readyState as number;
            if (readyState !== 1) {
              logAgentInfo(agentId, `MongoDB connection is not ready (state: ${readyState}). Attempting to reconnect...`);

              // Wait for MongoDB connection to be established
              for (let i = 0; i < 5; i++) {
                const currentState = mongoose.connection.readyState as number;
                if (currentState === 1) {
                  logAgentInfo(agentId, "MongoDB connection is now ready");
                  break;
                }

                logAgentInfo(agentId, `Waiting for MongoDB connection (attempt ${i+1}/5)...`);
                await new Promise(resolve => setTimeout(resolve, 500));
              }

              // If still not connected, throw an error
              const finalState = mongoose.connection.readyState as number;
              if (finalState !== 1) {
                throw new Error(`MongoDB connection is not ready (state: ${finalState})`);
              }
            }

            // Now try to find the agent
            const agent = await Agent.findOne({ botId: agentId.toString() });
            if (!agent) {
              logAgentWarn(agentId, "Agent not found");
              return;
            }

            logAgentInfo(agentId, `Processing alert with signals state: ${JSON.stringify(agent.signals)}`);

            // For test environments, log more details to help with debugging
            if (isTest) {
              console.log(`Agent state before processing:`, JSON.stringify(agent.signals));
            }

            // Process the alert with the agent document
            await processAlert(alertData, wallet, agent, connectionRpc);

            // The agent document is already updated by processAlert and saved within the function
            logAgentInfo(agentId, `Updated signals state: ${JSON.stringify(agent.signals)}`);

            // For test environments, log more details to help with debugging
            if (isTest) {
              console.log(`Agent state after processing:`, JSON.stringify(agent.signals));
            }

            logAgentInfo(agentId, `Finished job for agentId=${agentId}`);
          })(),

          // Timeout promise
          new Promise((_, reject) => {
            setTimeout(() => {
              reject(new Error(`Job ${job.id} timed out after ${timeoutMs}ms`));
            }, timeoutMs);
          })
        ]);



      } catch (err: any) {
        // Provide more detailed error information
        if (err.name === 'ExecutionError' && err.message.includes('quorum')) {
          logAgentError(agentId, `Redlock quorum error for job=${job.id}`, {
            message: err.message,
            attempts: err.attempts?.length || 0
          });
        } else {
          logAgentError(agentId, `Error processing job=${job.id}`, err);
        }

        // In test environment, we want to fail the job to see the error
        if (isTest) {
          throw err;
        }
      } finally {
        if (lock) {
          try {
            await lock.release();
            logAgentInfo(agentId, `Lock released`);
          } catch (releaseErr) {
            logAgentError(agentId, "Error releasing lock", releaseErr);
          }
        }
      }
    },
    {
      connection,
      concurrency,
      lockDuration: isTest ? 180_000 : 120_000, // 3 minutes for testing, 2 minutes for production
      stalledInterval: 30000,   // Check for stalled jobs more frequently (30 seconds)
      maxStalledCount: 2        // Consider jobs stalled after fewer checks
    }
  );

  worker.on("completed", (job) => {
    // Log only essential information to reduce memory usage
    logAgentInfo(job?.data.agentId, `Job completed: ${job?.id}`);
  });

  worker.on("failed", (job, err) => {
    // Log only essential error information
    const errorMessage = err?.message || 'Unknown error';
    logAgentError(job?.data.agentId, `Job failed: ${job?.id} - ${errorMessage}`);
  });

  return { worker, connection };
}

// Start the worker if this file is executed directly
if (require.main === module) {
  (async () => {
    const { worker, connection } = createWorker();

    // Handle process termination
    process.on('SIGTERM', async () => {
      logInfo('SIGTERM received, closing worker and connections...');
      await worker.close();
      await connection.quit();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logInfo('SIGINT received, closing worker and connections...');
      await worker.close();
      await connection.quit();
      process.exit(0);
    });
  })();
}
