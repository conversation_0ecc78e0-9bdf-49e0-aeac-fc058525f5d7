// src/queue/queue.ts
import { Queue } from "bullmq";
import IORedis from "ioredis";

const connection = new IORedis(process.env.REDIS_URL || "redis://127.0.0.1:6379",{
  maxRetriesPerRequest: null,
  enableReadyCheck: false,
  connectTimeout: 10000,      // 10 seconds
  commandTimeout: 10000,      // 10 seconds
  retryStrategy: (times) => {
    // Exponential backoff with a maximum of 3 seconds
    return Math.min(times * 200, 3000);
  }
});

export const alertQueue = new Queue("alertQueue", {
  connection,
  defaultJobOptions: {
    attempts: 5,                               // 5 real tries is enough
    backoff:  { type: "fixed", delay: 2000 },  // wait 2 s between tries
    removeOnComplete: {
      count: 10,                               // Keep only the last 10 completed jobs
      age: 60 * 60 * 24                        // Keep completed jobs for 1 day (in seconds)
    },
    removeOnFail: {
      count: 10,                               // Keep only the last 10 failed jobs
      age: 60 * 60 * 24 * 7                    // Keep failed jobs for 7 days (in seconds)
    }
  }
});
