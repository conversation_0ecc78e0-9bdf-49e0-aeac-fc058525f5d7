import { Queue } from "bullmq";
import IORedis from "ioredis";

const connection = new IORedis("redis://127.0.0.1:6379", {
  maxRetriesPerRequest: null,
  enableReadyCheck: false,
});
const myQueue = new Queue("myQueueName", { connection });

async function clearQueue() {
  // By default, obliterate() expects the queue to be empty.
  // If not, you must pass { force: true } to override:
  await myQueue.obliterate({ force: true });
  console.log("Queue emptied.");
}

clearQueue().catch(console.error);
