import { Request, Response, NextFunction } from 'express';
import { logInfo, logWarn } from '../utils/logger';

// Constants for IP filtering
const IP_HEADERS = {
  X_FORWARDED_FOR: 'x-forwarded-for',
  X_REAL_IP: 'x-real-ip'
} as const;

const ERROR_MESSAGES = {
  IP_NOT_ALLOWED: 'Access denied: IP address not allowed',
  IP_UNDETERMINED: 'Access denied: Unable to determine IP address'
} as const;

// Cache for parsed allowed IPs to avoid parsing on every request
let cachedAllowedIPs: string[] | null = null;
let lastEnvValue: string | undefined = undefined;

/**
 * Basic IP address format validation
 */
function isValidIPFormat(ip: string): boolean {
  // Basic IPv4 pattern
  const ipv4Pattern = /^(\d{1,3}\.){3}\d{1,3}$/;
  // Basic IPv6 pattern (simplified)
  const ipv6Pattern = /^([0-9a-fA-F]{0,4}:){2,7}[0-9a-fA-F]{0,4}$/;

  return ipv4Pattern.test(ip) || ipv6Pattern.test(ip) || ip === '::1';
}

/**
 * Extract the real IP address from the request
 * Checks various headers and fallbacks in order of priority
 */
function extractRealIP(req: Request): string | undefined {
  // 1. Check x-forwarded-for header (first IP in the list)
  const xForwardedFor = req.headers[IP_HEADERS.X_FORWARDED_FOR];
  if (xForwardedFor) {
    const ips = Array.isArray(xForwardedFor) ? xForwardedFor[0] : xForwardedFor;
    const firstIP = ips.split(',')[0].trim();
    if (firstIP && isValidIPFormat(firstIP)) {
      return firstIP;
    }
  }

  // 2. Check x-real-ip header
  const xRealIP = req.headers[IP_HEADERS.X_REAL_IP];
  if (xRealIP) {
    const realIP = Array.isArray(xRealIP) ? xRealIP[0] : xRealIP;
    if (realIP && isValidIPFormat(realIP.trim())) {
      return realIP.trim();
    }
  }

  // 3. Check req.ip
  if (req.ip) {
    return req.ip;
  }

  // 4. Check connection.remoteAddress
  if (req.connection && req.connection.remoteAddress) {
    return req.connection.remoteAddress;
  }

  // 5. Check socket.remoteAddress
  if (req.socket && req.socket.remoteAddress) {
    return req.socket.remoteAddress;
  }

  return undefined;
}

/**
 * Parse the ALLOWED_IPS environment variable into a clean array of IP addresses
 * Uses caching to avoid parsing on every request
 */
function parseAllowedIPs(): string[] {
  const currentEnvValue = process.env.ALLOWED_IPS;

  // Use cached value if environment hasn't changed
  if (cachedAllowedIPs !== null && lastEnvValue === currentEnvValue) {
    return cachedAllowedIPs;
  }

  // Update cache
  lastEnvValue = currentEnvValue;

  if (!currentEnvValue || currentEnvValue.trim() === '') {
    cachedAllowedIPs = [];
    return cachedAllowedIPs;
  }

  cachedAllowedIPs = currentEnvValue
    .split(',')
    .map(ip => ip.trim())
    .filter(ip => ip.length > 0 && isValidIPFormat(ip)); // Filter out empty strings and invalid IPs

  return cachedAllowedIPs;
}

/**
 * Check if an IP address is in the allowed list
 */
function isIPAllowed(clientIP: string, allowedIPs: string[]): boolean {
  return allowedIPs.includes(clientIP);
}

/**
 * Middleware to filter webhook requests based on IP addresses
 * Reads allowed IPs from ALLOWED_IPS environment variable
 *
 * @param req Express request object
 * @param res Express response object
 * @param next Express next function
 */
export async function ipFilterMiddleware(req: Request, res: Response, next: NextFunction): Promise<any> {
  try {
    // Parse allowed IPs from environment variable
    const allowedIPs = parseAllowedIPs();

    // If no allowed IPs are configured, allow all requests (fail-open approach)
    if (allowedIPs.length === 0) {
      logInfo('IP filtering disabled: ALLOWED_IPS not configured');
      return next();
    }

    // Extract the real IP address from the request
    const clientIP = extractRealIP(req);

    // If we can't determine the IP address, deny the request
    if (!clientIP) {
      logWarn('IP filtering: Unable to determine client IP address', {
        userAgent: req.headers['user-agent'],
        url: req.url,
        method: req.method
      });
      return res.status(403).json({
        error: ERROR_MESSAGES.IP_UNDETERMINED
      });
    }

    // Check if the IP is in the allowed list
    if (isIPAllowed(clientIP, allowedIPs)) {
      logInfo(`IP filtering: Allowed request from IP ${clientIP}`, {
        url: req.url,
        method: req.method,
        userAgent: req.headers['user-agent']
      });
      return next();
    } else {
      logWarn(`IP filtering: Blocked request from IP ${clientIP}. Allowed IPs: ${allowedIPs.join(', ')}`, {
        url: req.url,
        method: req.method,
        userAgent: req.headers['user-agent']
      });
      return res.status(403).json({
        error: ERROR_MESSAGES.IP_NOT_ALLOWED,
        ip: clientIP
      });
    }
  } catch (error) {
    // In case of any error, log it and allow the request to proceed (fail-open approach)
    logWarn('IP filtering: Error in middleware, allowing request to proceed', error);
    return next();
  }
}

export default ipFilterMiddleware;
