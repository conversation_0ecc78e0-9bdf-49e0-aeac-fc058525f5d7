import { Request, Response, NextFunction } from "express";
import Agent from "../databaseModels/agent";
import { logInfo, logWarn } from "../utils/logger";

/**
 * Interface for signal types
 */
type SignalType = "buy" | "sell" | "buyOpenBar" | "sellOpenBar";

/**
 * Middleware to filter webhook requests based on signal thresholds
 * This helps reduce queue traffic by filtering out requests that don't need processing
 *
 * @param req Express request object
 * @param res Express response object
 * @param next Express next function
 */
export async function webhookFilterMiddleware(req: Request, res: Response, next: NextFunction):Promise<any> {
  const agentId = req.params.agentId;
  const alertData = req.body;
  const { action, signalName, ticker } = alertData;

  if (!agentId) {
    logWarn("Webhook request missing agentId");
    return res.status(400).send("Missing agentId");
  }

  // Validate essential fields in the request
  if (!action) {
    logWarn(`Webhook request for agentId=${agentId} missing action`);
    return res.status(400).send("Missing action");
  }

  if (!signalName && action === "confirmationMinute") {
    logWarn(`Webhook request for agentId=${agentId} missing signalName for confirmationMinute action`);
    return res.status(400).send("Missing signalName for confirmationMinute action");
  }

  if (!ticker) {
    logWarn(`Webhook request for agentId=${agentId} missing ticker`);
    return res.status(400).send("Missing ticker");
  }

  logInfo(`Filtering webhook for agentId=${agentId}`, {
    action,
    signalName,
    ticker
  });

  try {
    // Only filter for confirmation signals
    if (action === "confirmationMinute") {
      // Determine which fields to fetch based on the signal name
      let fieldsToFetch = {};

      if (signalName === "buy") {
        fieldsToFetch = {
          'signals.buy': 1,
          'requiredBuyConfirmationsOpen': 1,
          'requiredBuyConfirmationsClose': 1,
          'requiredBuyConfirmationsResetCounter': 1,
          'requiredBuyConfirmationsOverride': 1
        };
      } else if (signalName === "sell") {
        fieldsToFetch = {
          'signals.sell': 1,
          'requiredSellConfirmationsOpen': 1,
          'requiredSellConfirmationsClose': 1,
          'requiredSellConfirmationsResetCounter': 1,
          'requiredSellConfirmationsOverride': 1
        };
      } else {
        // If it's not a buy or sell signal, proceed with normal processing
        return next();
      }

      // Use a lightweight query to only fetch the necessary fields
      // This reduces memory usage by not loading the entire agent document
      const agentSignals = await Agent.findOne(
        { botId: agentId.toString() },
        fieldsToFetch
      ).lean(); // Use lean() to get a plain JS object instead of a Mongoose document

      if (agentSignals) {
        // Initialize signals if needed and check thresholds based on signal type
        if (signalName === "buy") {
          const buySignal = agentSignals.signals?.buy || 0;
          const requiredBuyConfirmationsOpen = agentSignals.requiredBuyConfirmationsOpen || 0;
          const requiredBuyConfirmationsClose = agentSignals.requiredBuyConfirmationsClose || 0;
          const requiredBuyConfirmationsResetCounter = agentSignals.requiredBuyConfirmationsResetCounter || 0;
          const requiredBuyConfirmationsOverride = agentSignals.requiredBuyConfirmationsOverride || 0;
          // Calculate the maximum threshold
          const maxBuyThreshold = Math.max(
            requiredBuyConfirmationsOpen,
            requiredBuyConfirmationsClose,
            requiredBuyConfirmationsResetCounter,
            requiredBuyConfirmationsOverride
          );


          // Check if we should filter this request
          if (buySignal > maxBuyThreshold) {
            logWarn(`Filtering webhook for agentId=${agentId}: signals.buy(${buySignal}) > maxBuyThreshold(${maxBuyThreshold})`);
            // Return 202 Accepted to indicate we received but won't process
            return res.status(202).send("Request filtered: buy signal threshold already met");
          }
        } else if (signalName === "sell") {
          const sellSignal = agentSignals.signals?.sell || 0;
          const requiredSellConfirmationsOpen = agentSignals.requiredSellConfirmationsOpen || 0;
          const requiredSellConfirmationsClose = agentSignals.requiredSellConfirmationsClose || 0;
          const requiredSellConfirmationsResetCounter = agentSignals.requiredSellConfirmationsResetCounter || 0;
          const requiredSellConfirmationsOverride = agentSignals.requiredSellConfirmationsOverride || 0;

          // Calculate the maximum threshold
          const maxSellThreshold = Math.max(
            requiredSellConfirmationsOpen,
            requiredSellConfirmationsClose,
            requiredSellConfirmationsResetCounter,
            requiredSellConfirmationsOverride
          );
          //console.log(requiredSellConfirmationsOpen,requiredSellConfirmationsClose,requiredSellConfirmationsResetCounter,requiredSellConfirmationsOverride,maxSellThreshold);

          // Check if we should filter this request
          if (sellSignal > maxSellThreshold) {
            logWarn(`Filtering webhook for agentId=${agentId}: signals.sell(${sellSignal}) > maxSellThreshold(${maxSellThreshold})`);
            // Return 202 Accepted to indicate we received but won't process
            return res.status(202).send("Request filtered: sell signal threshold already met");
          }
        }
      } else {
        logWarn(`Agent not found for agentId=${agentId}, proceeding with queue addition`);
      }
    }

    // If we reach here, the request wasn't filtered, so proceed with normal processing
    next();
  } catch (error) {
    logWarn(`Error in webhook filter middleware for agentId=${agentId}:`, error);
    // In case of error, proceed with normal processing rather than blocking the request
    next();
  }
}

export default webhookFilterMiddleware;
