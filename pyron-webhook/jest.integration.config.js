module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/test/integration/**/*.test.ts'],
  setupFiles: ['<rootDir>/test/jest.setup.integration.js'],
  testTimeout: 240000, // Longer timeout for integration tests
  setupFilesAfterEnv: ['./test/setup.js'],
  maxWorkers: 1, // Run tests sequentially to avoid resource conflicts
  forceExit: true, // Force Jest to exit after tests complete
  detectOpenHandles: false, // Disable open handles detection for now
  verbose: true,
  bail: false, // Continue running tests even if some fail
  collectCoverage: false // Disable coverage for integration tests
};
