# Test Plan for Pyron Webhook Service

## Core Components to Test

### 1. UTILITY FUNCTIONS

#### createKeypairFromSecretKey.ts
- Valid secret key conversion
- Invalid secret key formats
- Error handling for malformed keys

#### getMarketId.ts
- Valid market ID resolution
- Unknown market handling
- Error handling for invalid markets

#### getTicker.ts
- Valid ticker symbol processing
- Case conversion and whitespace handling
- Invalid input handling

#### processAlert.ts
- Alert processing logic
- Order placement integration
- Error handling scenarios

### 2. DRIFT PROTOCOL INTEGRATION

#### Client Management (getClient.ts)
- Successful Drift client creation
- Invalid RPC endpoint handling
- Client configuration validation
- Valid keypair authentication
- Invalid keypair handling

#### Price Data (getPerpPrice.ts)
- Valid price data fetching
- Market not found scenarios
- Price data validation
- Network failure recovery

#### Order Management (placeOrder.ts)
- Valid buy/sell order placement
- Order size calculations
- Position direction handling
- Invalid order parameters
- Insufficient balance scenarios
- Transaction confirmation
- Transaction failure recovery

#### Order Cancellation (cancelOrders.ts)
- Valid order cancellation
- Order not found scenarios
- Already filled orders

### 3. MIDDLEWARE & FILTERING

#### IP Filter Middleware
- ALLOWED_IPS environment variable handling
- IP address extraction from headers (x-forwarded-for, x-real-ip)
- Fallback to req.ip and connection.remoteAddress
- IPv6 address support
- Error handling for malformed configurations
- Graceful handling of missing IP addresses

#### Webhook Filter Middleware
- Request validation (agentId, action, signalName, ticker)
- Signal filtering based on agent thresholds
- Buy/sell signal threshold comparisons
- Error handling and agent lookup failures
- Action-specific validation logic

### 4. QUEUE MANAGEMENT

#### Queue Operations
- Mock queue functionality for unit tests
- Job addition and processing
- Queue cleanup operations
- Pause and resume operations

### 5. END-TO-END TESTING

#### Complete Webhook Workflow
- TradingView webhook → IP filter → webhook filter → controller → queue → worker → trade execution
- Buy signal processing end-to-end
- Sell signal processing end-to-end
- Signal confirmation accumulation
- Position opening and closing

#### Error Recovery
- Network failures during trade execution
- Queue processing failures
- Database unavailability

#### Multi-Agent Scenarios
- Multiple agents processing simultaneously
- Agent state consistency
- Signal counter synchronization
- Concurrent webhook processing (up to 100 agents)

## Test Implementation Approach

### Testing Framework
- Jest - Primary testing framework
- Supertest - HTTP endpoint testing
- MongoDB & Redis - Real database testing for integration
- Mock Functions - External dependency mocking for unit tests

### Test Organization
- Unit Tests - Fast, isolated component testing (`test/unit/`)
  - Focus on business logic, utilities, and configuration
  - Mock external dependencies (database, queue, worker)
  - Avoid testing framework features or simple data operations
- Integration Tests - Component interaction testing (`test/integration/`)
  - Test complete workflows with real databases
  - Verify database models and worker processes in realistic scenarios
- End-to-End Tests - Complete workflow testing
- Error Scenario Tests - Comprehensive failure case validation

### Test Environment
- Separate Test Databases - Isolated MongoDB and Redis instances for integration tests
- Environment Configuration - `.env.test` file for test settings
- Mock Queue - Unit tests use mock queue, integration tests use real queue
- Mock Database - Unit tests mock database operations, integration tests use real databases
- Database Isolation - Different databases for unit vs integration tests

