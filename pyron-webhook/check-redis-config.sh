#!/bin/bash

# <PERSON>ript to check and optimize Redis configuration for webhook processing

echo "Checking Redis configuration..."

# Check if Redis is running
if ! redis-cli ping > /dev/null; then
  echo "Error: Redis is not running. Please start Redis first."
  exit 1
fi

# Get current Redis configuration values
get_config() {
  redis-cli CONFIG GET "$1" | awk 'NR==2'
}

# Set Redis configuration value
set_config() {
  echo "Setting $1 to $2 (was $(get_config $1))"
  redis-cli CONFIG SET "$1" "$2"
}

# Check and optimize key Redis parameters
echo "Current Redis configuration:"
echo "- maxclients: $(get_config maxclients)"
echo "- timeout: $(get_config timeout)"
echo "- tcp-keepalive: $(get_config tcp-keepalive)"
echo "- maxmemory-policy: $(get_config maxmemory-policy)"
echo "- databases: $(get_config databases)"

echo -e "\nOptimizing Redis configuration for webhook processing..."

# Increase client timeout to prevent premature disconnections
set_config timeout 300

# Enable TCP keepalive to detect dead connections
set_config tcp-keepalive 60

# Set memory policy to volatile-lru (if using Redis for caching)
# or noeviction (if using Redis for queuing)
set_config maxmemory-policy noeviction

echo -e "\nRedis configuration updated successfully."
echo "For permanent changes, update your redis.conf file."

# Provide instructions for permanent configuration
echo -e "\nTo make these changes permanent, add the following to your redis.conf file:"
echo "timeout 300"
echo "tcp-keepalive 60"
echo "maxmemory-policy noeviction"

echo -e "\nRestart Redis after updating the configuration file."

# Check Redis client connection pool settings
echo -e "\nRecommended Node.js/IORedis client settings:"
echo "- maxRetriesPerRequest: null (or a high value like 20)"
echo "- connectTimeout: 10000 (10 seconds)"
echo "- commandTimeout: 10000 (10 seconds)"
echo "- retryStrategy: Exponential backoff with jitter"
echo "- enableReadyCheck: false (for performance)"

echo -e "\nRedlock settings for distributed locking:"
echo "- retryCount: 50 (for testing)"
echo "- retryDelay: 800"
echo "- retryJitter: 800"
echo "- driftFactor: 0.2"
echo "- automaticExtensionThreshold: 2000"

echo -e "\nDone!"
