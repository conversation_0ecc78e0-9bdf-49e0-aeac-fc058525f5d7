# Testing Guide for Pyron Webhook

This guide explains how to run the unit tests in the pyron-webhook project.

## Current Test Status

✅ **Unit Tests**: All passing (191 tests) - ⚡ Fast execution (~2.8s)
✅ **Integration Tests**: Working but slow (~2+ minutes) - Require MongoDB & Redis

### Last Test Run Results
```
Test Suites: 8 passed, 8 total
Tests:       191 passed, 191 total
Snapshots:   0 total
Time:        2.824 s
```

## Quick Start - Running Tests

### Run Only Unit Tests (Recommended)
```bash
# Run unit tests only - these should pass quickly
npx jest test/unit --verbose
```

### Run All Tests (Including Integration)
```bash
# This will include integration tests (slower but comprehensive)
npm test
```

## Test Structure

```
test/
├── unit/                           # Fast, isolated tests ✅
│   ├── ipFilterMiddleware.test.ts   # IP filtering logic (14 tests)
│   ├── webhookFilterMiddleware.test.ts # Webhook validation (11 tests)
│   ├── processAlert.test.ts        # Alert processing logic (43 tests)
│   ├── queue.test.ts               # Queue operations (37 tests)
│   └── drift/                      # Drift protocol tests (86 tests)
│       ├── getClient.test.ts       # Client creation tests
│       ├── placeOrder.test.ts      # Order placement tests
│       ├── cancelOrders.test.ts    # Order cancellation tests
│       └── getPerpPrice.test.ts    # Price fetching tests
├── integration/                    # Slow, comprehensive tests ✅
│   ├── trade.test.ts              # Trading logic integration
│   └── multi-agent.test.ts        # Multi-agent scenarios
├── setup.js                       # Global test setup
├── mocks/                         # Test mocks and utilities
└── utils/                         # Test utility functions
```

## Test Environment Setup
Tests now use a separate `.env.test` file with isolated test databases.

**Current Setup**:
- ✅ **Test MongoDB**: `localhost:27017/pyron-test` (isolated test database)
- ✅ **Test Redis**: `redis://127.0.0.1:6379` (local Redis instance)
- ✅ **No production data risk**: Tests are completely isolated

**How to Set Up Your Test Environment**:

**Step 1: Create MongoDB with Authentication**
```bash
# Stop any existing test containers
docker stop test-mongo test-redis 2>/dev/null || true
docker rm test-mongo test-redis 2>/dev/null || true

# Start MongoDB with authentication
docker run -d -p 27017:27017 --name test-mongo \
  -e MONGO_INITDB_ROOT_USERNAME=test \
  -e MONGO_INITDB_ROOT_PASSWORD=test \
  -e MONGO_INITDB_DATABASE=pyron-test \
  mongo:latest

# Start Redis
docker run -d -p 6379:6379 --name test-redis redis:latest
```

**Step 2: Verify Database Connection**
```bash
# Wait for MongoDB to start (about 10 seconds)
sleep 10

# Test MongoDB connection
docker exec test-mongo mongosh -u test -p test --authenticationDatabase admin pyron-test --eval "db.runCommand('ping')"
# Should output: { ok: 1 }

# Test Redis connection
docker exec test-redis redis-cli ping
# Should output: PONG
```

**Step 3: Your .env.test File Should Look Like This**
```bash
NODE_ENV=test
ADMIN_KEY=test_key_for_testing_only
RPC_URL=https://api.devnet.solana.com
MONGO_HOST=localhost
MONGO_USER=test
MONGO_PASSWORD=test
MONGO_DB=pyron-test
MONGO_PORT=27017
PORT=0
REDIS_URL=redis://127.0.0.1:6379
WORKER_CONCURRENCY=3
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3004
ALLOWED_IPS=127.0.0.1,::1
```

**Important Notes**:
- ✅ **MONGO_USER and MONGO_PASSWORD must match your Docker container credentials**
- ✅ **MONGO_DB should be a test-specific database name**
- ✅ **Never use production credentials in .env.test**
- ✅ **The Jest configuration automatically loads .env.test for tests**
- ✅ **IP Filtering**: The middleware uses `ALLOWED_IPS` for IP filtering.


## Test Commands Reference

```bash
# Run only unit tests (fast, no external dependencies)
npx jest test/unit

# Run with verbose output
npx jest test/unit --verbose

# Run specific test file
npx jest test/unit/ipFilterMiddleware.test.ts

# Run tests with coverage
npx jest test/unit --coverage

# Run all tests (including integration)
npm test

# Run integration tests only (requires MongoDB + Redis)
npx jest test/integration
```

## Understanding Test Output

### Successful Unit Test Run
```
PASS test/unit/ipFilterMiddleware.test.ts
PASS test/unit/webhookFilterMiddleware.test.ts

Test Suites: 2 passed, 2 total
Tests: 25 passed, 25 total
Time: 1.173s
```



## Troubleshooting

### Tests Won't Start
- Check if TypeScript is compiled: `npx tsc --noEmit`
- Install dependencies: `npm install`

### Unit Tests Failing
- Check environment variables in `.env.test`
- Verify no external service dependencies in unit tests

### Integration Tests Timeout
- Increase timeout in jest.config.js
- Check if MongoDB and Redis are accessible
- Verify network connectivity

### Memory Issues
- Integration tests create many agents and jobs
- Consider reducing `NUM_AGENTS` in test files
- Monitor memory usage during tests

## Best Practices

1. **Always run unit tests first** - they're fast and catch most issues
2. **Use integration tests sparingly** - only when testing full workflows
3. **Mock external services** in unit tests
4. **Keep test data isolated** - use separate test databases
5. **Clean up after tests** - remove test data and close connections

## Current Test Coverage

### Unit Tests (All Passing ✅)

**IP Filter Middleware** (14 tests):
- ✅ ALLOWED_IPS environment variable handling
- ✅ IP address extraction from headers (x-forwarded-for, x-real-ip)
- ✅ Fallback to req.ip and connection.remoteAddress
- ✅ IPv6 address support
- ✅ Error handling for malformed configurations
- ✅ Graceful handling of missing IP addresses

**Webhook Filter Middleware** (11 tests):
- ✅ Request validation (agentId, action, signalName, ticker)
- ✅ Signal filtering based on agent thresholds
- ✅ Buy/sell signal threshold comparisons
- ✅ Error handling and agent lookup failures
- ✅ Action-specific validation logic

**Process Alert Logic** (43 tests):
- ✅ Alert processing and validation
- ✅ Signal confirmation logic
- ✅ Position management rules
- ✅ Error handling and edge cases

**Queue Operations** (37 tests):
- ✅ Job creation and processing
- ✅ Queue configuration and management
- ✅ Redis connection handling
- ✅ Worker lifecycle management

**Drift Protocol Integration** (86 tests):
- ✅ Client creation and configuration
- ✅ Order placement and execution
- ✅ Order cancellation logic
- ✅ Price fetching and market data
- ✅ Error handling and retry logic

### Integration Tests (Working but Slow ⚠️)

**Trading Logic Integration** (connects to MongoDB + Redis):
- ✅ Position opening/closing rules
- ✅ Signal confirmation thresholds
- ✅ Counter reset mechanisms
- ✅ LookBack function testing
- ⚠️ Takes 2+ minutes to complete

**Multi-Agent Scenarios** (connects to MongoDB + Redis):
- ✅ Concurrent webhook processing
- ✅ Multiple agent state management
- ✅ Queue job processing at scale (up to 100 agents)
- ⚠️ Very slow due to complexity

## Quick Fixes for Common Issues

### "Cannot find module" errors
```bash
npm install
```

### "TypeScript compilation errors"
```bash
npx tsc --noEmit
```

### "Integration tests taking too long"
```bash
# Kill the process and run unit tests only
Ctrl+C
npx jest test/unit
```

### "Want faster feedback during development"
```bash
# Run unit tests only for quick feedback
npx jest test/unit --verbose --watch
```

### "mongosh: command not found"
```bash
# Don't install mongosh locally, use Docker instead:
docker exec test-mongo mongosh -u test -p test --authenticationDatabase admin --eval "db.runCommand('ping')"

# Or just check if the container is running:
docker ps | grep test-mongo
```

### "Authentication failed" errors in tests
```bash
# Check if MongoDB container has correct credentials
docker exec test-mongo mongosh -u test -p test --authenticationDatabase admin --eval "db.runCommand('ping')"

# If that fails, recreate the container with proper auth:
docker stop test-mongo && docker rm test-mongo
docker run -d -p 27017:27017 --name test-mongo \
  -e MONGO_INITDB_ROOT_USERNAME=test \
  -e MONGO_INITDB_ROOT_PASSWORD=test \
  mongo:latest
```

### "Tests still using production database"
```bash
# Verify Jest is loading .env.test:
npx jest test/unit --verbose
# Look for: "🧪 Test environment loaded from .env.test"

# Check your .env.test file exists and has correct values:
cat .env.test | grep MONGO
```

## Summary

**✅ SAFE & FAST**: Unit tests (191 tests) - Run with `npx jest test/unit` (~2.8s)

**✅ SAFE & COMPREHENSIVE**: Integration tests - Now using isolated test databases

**🎯 RECOMMENDATIONS**:
1. **Use unit tests for daily development**: `npx jest test/unit` (fast feedback - 191 tests in ~2.8s)
2. **Use integration tests for thorough testing**: `npx jest test/integration` (comprehensive)
3. **Both test suites are now safe** - no production data risk
4. **Test environment is properly isolated** with `.env.test`
5. **Comprehensive coverage**: Tests cover middleware, queue operations, Drift protocol, and alert processing

## Working Example Commands

With the test environment properly set up, here are the verified working commands:

```bash
# ✅ Check MongoDB with authentication (working)
docker exec test-mongo mongosh -u test -p test --authenticationDatabase admin pyron-test --eval "db.runCommand('ping')"
# Output: { ok: 1 }

# ✅ Check Redis (working)
docker exec test-redis redis-cli ping
# Output: PONG

# ✅ Check services are running
docker ps | grep -E "(test-mongo|test-redis)"

# ✅ Run unit tests (fast - 1.2s)
npx jest test/unit --verbose

# ✅ Run integration tests (working with test databases - 2+ min)
npx jest test/integration/trade.test.ts --testTimeout=120000

# ✅ Run all tests
npm test
```

