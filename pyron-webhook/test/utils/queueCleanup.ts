// test/utils/queueCleanup.ts
// Utility functions for robust queue cleanup in tests

import { Queue } from 'bullmq';

/**
 * Safely cleans up a queue by pausing it, removing all jobs, and obliterating it
 * @param queue - The BullMQ queue to clean up
 * @param options - Cleanup options
 */
export async function safeQueueCleanup(
  queue: Queue, 
  options: { 
    waitForEmpty?: boolean;
    maxAttempts?: number;
    waitTime?: number;
    resume?: boolean;
  } = {}
): Promise<void> {
  const {
    waitForEmpty = true,
    maxAttempts = 10,
    waitTime = 500,
    resume = true
  } = options;

  try {
    console.log("Starting safe queue cleanup...");
    
    // First, pause the queue to prevent new jobs from being added
    await queue.pause();
    console.log("Queue paused");
    
    // Wait a bit for any active jobs to complete
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Get all jobs from different states
    const waitingJobs = await queue.getWaiting();
    const activeJobs = await queue.getActive();
    const delayedJobs = await queue.getDelayed();
    const completedJobs = await queue.getCompleted();
    const failedJobs = await queue.getFailed();

    const allJobs = [
      ...waitingJobs,
      ...activeJobs,
      ...delayedJobs,
      ...completedJobs,
      ...failedJobs
    ];

    console.log(`Found ${allJobs.length} jobs to clean up`);

    // Remove all jobs individually
    for (const job of allJobs) {
      try {
        await job.remove();
      } catch (err) {
        console.log(`Error removing job ${job?.id}:`, err);
      }
    }

    // Wait for queue to be completely empty if requested
    if (waitForEmpty) {
      let attempts = 0;
      while (attempts < maxAttempts) {
        const activeCount = await queue.getActiveCount();
        const waitingCount = await queue.getWaitingCount();
        const delayedCount = await queue.getDelayedCount();
        
        if (activeCount === 0 && waitingCount === 0 && delayedCount === 0) {
          console.log("Queue is now empty");
          break;
        }
        
        console.log(`Waiting for queue to empty... Active: ${activeCount}, Waiting: ${waitingCount}, Delayed: ${delayedCount}`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        attempts++;
      }
      
      if (attempts >= maxAttempts) {
        console.log("Warning: Queue may not be completely empty after waiting");
      }
    }

    // Try to obliterate the queue
    try {
      await queue.obliterate({ force: true });
      console.log("Queue obliterated successfully");
    } catch (obliterateError) {
      console.log("Error obliterating queue:", obliterateError);
      // Try alternative cleanup methods
      try {
        await queue.drain();
        console.log("Queue drained as fallback");
      } catch (drainError) {
        console.log("Error draining queue:", drainError);
      }
    }

    // Resume the queue if requested
    if (resume) {
      await queue.resume();
      console.log("Queue resumed");
    }
    
  } catch (error) {
    console.log("Error during queue cleanup:", error);
    
    // Try to resume the queue even if cleanup failed
    if (resume) {
      try {
        await queue.resume();
        console.log("Queue resumed after error");
      } catch (resumeError) {
        console.log("Error resuming queue:", resumeError);
      }
    }
    
    // Don't throw the error, just log it to avoid breaking tests
  }
}

/**
 * Waits for all jobs in the queue to complete
 * @param queue - The BullMQ queue to monitor
 * @param options - Wait options
 */
export async function waitForQueueEmpty(
  queue: Queue,
  options: {
    maxAttempts?: number;
    waitTime?: number;
    checkInterval?: number;
  } = {}
): Promise<boolean> {
  const {
    maxAttempts = 50,
    waitTime = 30000, // 30 seconds total
    checkInterval = 600 // 600ms between checks
  } = options;

  const startTime = Date.now();
  let attempts = 0;

  while (attempts < maxAttempts && (Date.now() - startTime) < waitTime) {
    const activeCount = await queue.getActiveCount();
    const waitingCount = await queue.getWaitingCount();
    const delayedCount = await queue.getDelayedCount();

    if (activeCount === 0 && waitingCount === 0 && delayedCount === 0) {
      return true;
    }

    await new Promise(resolve => setTimeout(resolve, checkInterval));
    attempts++;
  }

  return false;
}
