// Jest setup file to configure test environment
const dotenv = require('dotenv');
const path = require('path');

// Load test environment variables from .env.test
dotenv.config({ path: path.resolve(__dirname, '../.env.test') });

// Ensure NODE_ENV is set to test
process.env.NODE_ENV = 'test';

// Determine if this is a unit test by checking the test file path or Jest arguments
const isUnitTest = process.argv.some(arg => arg.includes('test/unit')) ||
                   process.argv.some(arg => arg.includes('test\\unit')) || // Windows path
                   process.env.npm_config_argv && JSON.parse(process.env.npm_config_argv).original.includes('test:unit');

if (isUnitTest) {
  // Mock the queue module for unit tests to avoid Redis dependency
  jest.mock('../src/queue/queue', () => {
    const { mockAlertQueue } = require('./mocks/mockQueue');
    return {
      alertQueue: mockAlertQueue
    };
  });

  // Mock the worker module for unit tests
  jest.mock('../src/queue/worker', () => ({
    createWorker: jest.fn().mockReturnValue({
      worker: {
        close: jest.fn().mockResolvedValue(true),
        on: jest.fn()
      },
      connection: {
        disconnect: jest.fn().mockResolvedValue(true),
        quit: jest.fn().mockResolvedValue(true)
      }
    })
  }));

  console.log('🧪 Unit test environment loaded with mocked queue');
  console.log(`📊 MongoDB: ${process.env.MONGO_HOST}:${process.env.MONGO_PORT}/${process.env.MONGO_DB}`);
  console.log(`🔴 Redis: MOCKED (no real Redis connection for unit tests)`);
} else {
  // For integration tests, use different Redis databases to avoid conflicts
  const originalRedisUrl = process.env.REDIS_URL || 'redis://127.0.0.1:6379';

  // Parse the Redis URL and add a database number for integration tests
  if (originalRedisUrl.includes('redis://127.0.0.1:6379')) {
    // Use database 1 for integration tests (default is 0)
    process.env.REDIS_URL = originalRedisUrl.replace('redis://127.0.0.1:6379', 'redis://127.0.0.1:6379/1');
  }

  console.log('🧪 Integration test environment loaded from .env.test');
  console.log(`📊 MongoDB: ${process.env.MONGO_HOST}:${process.env.MONGO_PORT}/${process.env.MONGO_DB}`);
  console.log(`🔴 Redis: ${process.env.REDIS_URL} (isolated database for integration tests)`);
}
