// Jest setup file specifically for integration tests
const dotenv = require('dotenv');
const path = require('path');

// Load test environment variables from .env.test
dotenv.config({ path: path.resolve(__dirname, '../.env.test') });

// Ensure NODE_ENV is set to test
process.env.NODE_ENV = 'test';

// For integration tests, use different Redis databases to avoid conflicts
const originalRedisUrl = process.env.REDIS_URL || 'redis://127.0.0.1:6379';

// Parse the Redis URL and add a database number for integration tests
if (originalRedisUrl.includes('redis://127.0.0.1:6379')) {
  // Use database 1 for integration tests (default is 0)
  process.env.REDIS_URL = originalRedisUrl.replace('redis://127.0.0.1:6379', 'redis://127.0.0.1:6379/1');
}

console.log('🧪 Integration test environment loaded from .env.test');
console.log(`📊 MongoDB: ${process.env.MONGO_HOST}:${process.env.MONGO_PORT}/${process.env.MONGO_DB}`);
console.log(`🔴 Redis: ${process.env.REDIS_URL} (isolated database for integration tests)`);
