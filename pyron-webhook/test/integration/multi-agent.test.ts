import request from 'supertest';
import mongoose from 'mongoose';
import app from '../../main';
import Agent from '../../src/databaseModels/agent';
import Log from '../../src/databaseModels/log';
import { alertQueue } from '../../src/queue/queue';
import { createTestWorker } from '../testWorker';
import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to wait for job completion
const waitForJobCompletion = async () => {
  let jobProcessed = false;
  const maxAttempts = 50;
  const pollInterval = 100;

  console.log("Starting to wait for job completion...");

  const initialActiveJobs = await alertQueue.getActiveCount();
  const initialWaitingJobs = await alertQueue.getWaitingCount();
  const initialDelayedJobs = await alertQueue.getDelayedCount();

  console.log(`Initial queue state - Active: ${initialActiveJobs}, Waiting: ${initialWaitingJobs}, Delayed: ${initialDelayedJobs}`);

  if (initialActiveJobs === 0 && initialWaitingJobs === 0 && initialDelayedJobs === 0) {
    console.log("No jobs in queue initially, waiting briefly for jobs to be added...");
    await delay(200);

    const updatedActiveJobs = await alertQueue.getActiveCount();
    const updatedWaitingJobs = await alertQueue.getWaitingCount();
    const updatedDelayedJobs = await alertQueue.getDelayedCount();

    console.log(`Updated queue state - Active: ${updatedActiveJobs}, Waiting: ${updatedWaitingJobs}, Delayed: ${updatedDelayedJobs}`);

    if (updatedActiveJobs === 0 && updatedWaitingJobs === 0 && updatedDelayedJobs === 0) {
      console.log("No jobs were added to the queue. Nothing to wait for.");
      return true;
    }
  }

  let lastActiveCount = -1;
  let unchangedCount = 0;
  const maxUnchangedCount = 10;

  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      const activeJobs = await alertQueue.getActiveCount();
      const waitingJobs = await alertQueue.getWaitingCount();
      const delayedJobs = await alertQueue.getDelayedCount();
      const completedCount = await alertQueue.getCompletedCount();

      console.log(`Attempt ${attempt+1}/${maxAttempts} - Queue state: Active: ${activeJobs}, Waiting: ${waitingJobs}, Delayed: ${delayedJobs}, Completed: ${completedCount}`);

      if (activeJobs === 0 && waitingJobs === 0 && delayedJobs === 0) {
        await delay(300);
        jobProcessed = true;
        break;
      }

      if (activeJobs === lastActiveCount) {
        unchangedCount++;
        if (unchangedCount >= maxUnchangedCount) {
          console.log(`Active job count unchanged for ${maxUnchangedCount} attempts, may be stuck`);
          const activeJobDetails = await alertQueue.getActive();
          if (activeJobDetails.length > 0) {
            console.log(`Active jobs details:`, activeJobDetails.map(job => ({
              id: job.id,
              name: job.name,
              data: job.data,
              attemptsMade: job.attemptsMade
            })));
          }
        }
      } else {
        unchangedCount = 0;
        lastActiveCount = activeJobs;
      }

      await delay(pollInterval);
    } catch (error) {
      console.error('Error checking job status:', error);
    }
  }

  const activeJobs = await alertQueue.getActiveCount();
  const waitingJobs = await alertQueue.getWaitingCount();
  const delayedJobs = await alertQueue.getDelayedCount();
  const completedCount = await alertQueue.getCompletedCount();

  console.log(`Final queue state - Active: ${activeJobs}, Waiting: ${waitingJobs}, Delayed: ${delayedJobs}, Completed: ${completedCount}, Processed: ${jobProcessed}`);

  if (!jobProcessed) {
    console.warn('Warning: Timed out waiting for job completion');
    try {
      const activeJobs = await alertQueue.getActive();
      if (activeJobs.length > 0) {
        console.log(`Found ${activeJobs.length} stuck active jobs:`,
          activeJobs.map(job => ({ id: job.id, name: job.name })));

        for (const job of activeJobs) {
          console.log(`Stuck job ${job.id} details:`, {
            name: job.name,
            data: job.data,
            timestamp: job.timestamp,
            attemptsMade: job.attemptsMade
          });
        }
      }
    } catch (error) {
      console.error('Error getting active jobs:', error);
    }
  }

  await delay(300);
  console.log("Waited for job completion", jobProcessed);
  return jobProcessed;
};

// Test constants
const NUM_AGENTS = 20;
const testTicker = 'SOL';

// Mock console.log to capture logs
const originalConsoleLog = console.log;
let capturedLogs: string[] = [];

beforeEach(async () => {
  // Reset captured logs
  capturedLogs = [];
  console.log = (...args: any[]) => {
    capturedLogs.push(args.join(' '));
    originalConsoleLog.apply(console, args);
  };

  // Clean up any leftover jobs from previous tests
  try {
    const waitingJobs = await alertQueue.getWaiting();
    const activeJobs = await alertQueue.getActive();
    const delayedJobs = await alertQueue.getDelayed();

    const allJobs = [...waitingJobs, ...activeJobs, ...delayedJobs];

    if (allJobs.length > 0) {
      console.log(`Cleaning up ${allJobs.length} leftover jobs before test`);
      for (const job of allJobs) {
        try {
          await job.remove();
        } catch (err) {
          // Ignore errors during cleanup
        }
      }
    }
  } catch (error) {
    // Ignore errors during cleanup
  }
});

afterEach(async () => {
  console.log = originalConsoleLog;

  // Clean up any jobs created during the test
  try {
    const waitingJobs = await alertQueue.getWaiting();
    const activeJobs = await alertQueue.getActive();
    const delayedJobs = await alertQueue.getDelayed();

    const allJobs = [...waitingJobs, ...activeJobs, ...delayedJobs];

    if (allJobs.length > 0) {
      console.log(`Cleaning up ${allJobs.length} jobs after test`);
      for (const job of allJobs) {
        try {
          await job.remove();
        } catch (err) {
          // Ignore errors during cleanup
        }
      }
    }
  } catch (error) {
    // Ignore errors during cleanup
  }
});

// Set a longer timeout for all tests in this suite
jest.setTimeout(120000); // Increased timeout for multiple agents

describe('Multi-Agent Trading Rules Integration Tests', () => {
  let server: any;
  let testWorkerObj: any;
  let testAgents: any[] = [];

  beforeAll(async () => {
    // Disconnect from any existing connections
    await mongoose.disconnect();

    // Connect to test database
    const username = encodeURIComponent(process.env.MONGO_USER || 'test');
    const password = encodeURIComponent(process.env.MONGO_PASSWORD || 'test');
    const dbName = 'pyron-test';
    const host = process.env.MONGO_HOST || 'localhost';
    const portDb = process.env.MONGO_PORT || '27017';

    const TEST_DB_URI = `mongodb://${username}:${password}@${host}:${portDb}/${dbName}?authSource=admin`;

    await mongoose.connect(TEST_DB_URI);

    // Clear test database
    await Agent.deleteMany({});
    await Log.deleteMany({});

    // Properly clean up the queue before starting tests
    console.log("Cleaning up queue before starting tests...");

    try {
      // First, pause the queue to prevent new jobs from being added
      await alertQueue.pause();

      // Wait a bit for any active jobs to complete
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get all job IDs from different states
      const waitingJobs = await alertQueue.getWaiting();
      const activeJobs = await alertQueue.getActive();
      const delayedJobs = await alertQueue.getDelayed();
      const completedJobs = await alertQueue.getCompleted();
      const failedJobs = await alertQueue.getFailed();

      // Combine all job IDs
      const allJobs = [
        ...waitingJobs,
        ...activeJobs,
        ...delayedJobs,
        ...completedJobs,
        ...failedJobs
      ];

      console.log(`Found ${allJobs.length} jobs to clean up before tests`);

      // Remove all jobs
      for (const job of allJobs) {
        try {
          await job.remove();
        } catch (err) {
          console.log(`Error removing job ${job?.id}:`, err);
        }
      }

      // Wait for queue to be completely empty
      let attempts = 0;
      while (attempts < 10) {
        const activeCount = await alertQueue.getActiveCount();
        const waitingCount = await alertQueue.getWaitingCount();
        if (activeCount === 0 && waitingCount === 0) {
          break;
        }
        await new Promise(resolve => setTimeout(resolve, 500));
        attempts++;
      }

      // Now obliterate the empty queue with force option
      await alertQueue.obliterate({ force: true });
      console.log("Queue obliterated successfully");

      // Resume the queue for testing
      await alertQueue.resume();
    } catch (error) {
      console.log("Error during queue cleanup:", error);
      // Try to resume the queue even if cleanup failed
      try {
        await alertQueue.resume();
      } catch (resumeError) {
        console.log("Error resuming queue:", resumeError);
      }
    }

    // Create test worker with moderate concurrency
    testWorkerObj = createTestWorker(5); // Increased concurrency for multiple agents
    console.log("Created test worker with concurrency 5");

    // Make sure we start with a clean slate
    testAgents = [];

    // Double-check that all agents were deleted
    const existingAgents = await Agent.find({});
    if (existingAgents.length > 0) {
      console.log(`Found ${existingAgents.length} existing agents, deleting them...`);
      await Agent.deleteMany({});
    }

    // Create multiple test agents
    for (let i = 0; i < NUM_AGENTS; i++) {
      const agentId = `test-agent-${i}`;
      const chatId = `test-chat-${i}`;
      const agent = await Agent.create({
        agentName: `test-agent-${i}`,
        deposit: 1000,
        botId: agentId,
        chatId: chatId,
        assetPair: testTicker,
        tradingStatus: 'on',
        number: i,
        pubkey: 'AUQKdAdqSi9bfjJyYuaoHNARfqsF6W38PpLBeaWstGw',
        signals: {
          buy: 0,
          sell: 0,
          buyOpenBar: 0,
          sellOpenBar: 0
        },
        requiredBuyConfirmationsClose: 15,
        requiredSellConfirmationsClose: 15,
        requiredBuyConfirmationsOpen: 20,
        requiredSellConfirmationsOpen: 20,
        requiredBuyConfirmationsOpenBar: 1,
        requiredSellConfirmationsOpenBar: 1,
        requiredBuyConfirmationsResetCounter: 4,
        requiredSellConfirmationsResetCounter: 4
      });
      testAgents.push(agent);
    }

    // Verify all agents were created
    const createdAgents = await Agent.find({});
    console.log(`Created ${createdAgents.length} agents`);

    // Instead of expecting an exact number, make sure we have at least the required number
    expect(createdAgents.length).toBeGreaterThanOrEqual(NUM_AGENTS);

    // Start server on a different port for tests
    server = app.listen(0);
  });

  afterAll(async () => {
    try {
      // Close test worker first
      if (testWorkerObj && testWorkerObj.worker) {
        console.log("Closing test worker...");
        await testWorkerObj.worker.close();
      }

      // Clean up queue before closing connections
      console.log("Draining queue before cleanup...");
      try {
        await alertQueue.pause();

        // Wait for any active jobs to complete
        await new Promise(resolve => setTimeout(resolve, 1000));

        const waitingJobs = await alertQueue.getWaiting();
        const activeJobs = await alertQueue.getActive();
        const delayedJobs = await alertQueue.getDelayed();
        const completedJobs = await alertQueue.getCompleted();
        const failedJobs = await alertQueue.getFailed();

        const allJobs = [
          ...waitingJobs,
          ...activeJobs,
          ...delayedJobs,
          ...completedJobs,
          ...failedJobs
        ];

        console.log(`Found ${allJobs.length} jobs to clean up`);

        for (const job of allJobs) {
          try {
            await job.remove();
          } catch (err) {
            console.log(`Error removing job ${job?.id}:`, err);
          }
        }

        // Wait for queue to be completely empty
        let attempts = 0;
        while (attempts < 10) {
          const activeCount = await alertQueue.getActiveCount();
          const waitingCount = await alertQueue.getWaitingCount();
          if (activeCount === 0 && waitingCount === 0) {
            break;
          }
          await new Promise(resolve => setTimeout(resolve, 500));
          attempts++;
        }

        await alertQueue.obliterate({ force: true });
        console.log("Queue obliterated successfully");
      } catch (error) {
        console.log("Error during final queue cleanup:", error);
      }

      // Clean up database only if connection is still active
      try {
        if (mongoose.connection.readyState === 1) {
          await Agent.deleteMany({});
          await Log.deleteMany({});
          console.log("Final cleanup: All agents and logs deleted");
        }
      } catch (error) {
        console.log("Error during database cleanup:", error);
      }

      // Close server
      if (server) {
        await new Promise((resolve) => server.close(resolve));
      }

      // Close Redis connections
      if (testWorkerObj && testWorkerObj.connection) {
        await testWorkerObj.connection.disconnect();
      }

      const redisClient = (alertQueue as any).client;
      if (redisClient && typeof redisClient.disconnect === 'function') {
        await redisClient.disconnect();
      }

      // Close MongoDB connection last
      if (mongoose.connection.readyState !== 0) {
        await mongoose.connection.close();
      }

      await delay(500);
    } catch (error) {
      console.error('Error during test cleanup:', error);
    }
  });

  // Helper function to send webhook requests
  const sendWebhook = async (agentId: string, action: string, signalName?: string, waitForCompletion: boolean = true) => {
    const port = server.address().port;
    console.log(`Sending webhook to port ${port} for agent ${agentId}`);
    console.log("Request payload:", { action, signalName, ticker: testTicker });

    const response = await request(`http://localhost:${port}`)
      .post(`/webhook/${agentId}`)
      .send({
        action,
        signalName,
        ticker: testTicker
      });

    console.log(`Response status for agent ${agentId}:`, response.status);
    console.log(`Response body for agent ${agentId}:`, response.text);

    if (waitForCompletion) {
      await waitForJobCompletion();
    }

    return response;
  };

  // Helper function to send multiple webhook requests concurrently for multiple agents
  const sendConcurrentWebhooksForAgents = async (agentIds: string[], requests: Array<{action: string, signalName?: string}>) => {
    console.log(`Sending ${requests.length} concurrent webhook requests for ${agentIds.length} agents`);

    const allResponses = [];
    const batchSize = 10; // Process agents in batches to avoid overwhelming the system

    for (let i = 0; i < agentIds.length; i += batchSize) {
      const batch = agentIds.slice(i, i + batchSize);
      console.log(`Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(agentIds.length/batchSize)} (${batch.length} agents)`);

      for (const agentId of batch) {
        for (const req of requests) {
          try {
            const response = await sendWebhook(agentId, req.action, req.signalName, false);
            allResponses.push(response);
            await delay(50); // Small delay between requests to reduce contention
          } catch (error) {
            console.log(`Error sending webhook for agent ${agentId}:`, error);
            // Continue with other agents even if one fails
          }
        }
      }

      // Wait a bit between batches
      if (i + batchSize < agentIds.length) {
        await delay(500);
      }
    }

    console.log(`Sent ${allResponses.length} webhook requests, waiting for completion...`);
    await waitForJobCompletion();

    const completedCount = await alertQueue.getCompletedCount();
    console.log(`Completed jobs: ${completedCount}`);

    return allResponses;
  };

  // Helper function to get current agent state
  const getAgentState = async (agentId: string) => {
    const agent = await Agent.findOne({ botId: agentId });
    if (!agent) {
      throw new Error(`Test agent ${agentId} not found`);
    }
    console.log(`Current agent state for ${agentId}:`, JSON.stringify(agent, null, 2));
    return agent;
  };

  describe('Multi-Agent Position Opening Rules', () => {
    test('Should open long positions for multiple agents on Buy Confirm x 20 (Minute Bar) + Buy (Open Bar)', async () => {
      // Verify we have all agents before starting the test
      const allAgents = await Agent.find({});
      expect(allAgents.length).toBeGreaterThanOrEqual(NUM_AGENTS);
      console.log(`Starting test with ${allAgents.length} agents`);

      // Reset all agents' state
      for (const agent of testAgents) {
        await Agent.updateOne(
          { botId: agent.botId },
          { $set: {
            'signals.buy': 0,
            'signals.sell': 0,
            'signals.buyOpenBar': 0,
            'signals.sellOpenBar': 0
          }}
        );
      }

      // Verify all agents were reset
      const resetAgents = await Agent.find({});
      for (const agent of resetAgents) {
        expect(agent.signals.buy).toBe(0);
        expect(agent.signals.sell).toBe(0);
        expect(agent.signals.buyOpenBar).toBe(0);
        expect(agent.signals.sellOpenBar).toBe(0);
      }

      // Set buy confirmation count to 20 for all agents
      for (const agent of testAgents) {
        await Agent.updateOne(
          { botId: agent.botId },
          { $set: { 'signals.buy': 20 }}
        );
      }

      // Verify initial state for all agents
      const agentsWithBuySignals = await Agent.find({ 'signals.buy': 20 });
      expect(agentsWithBuySignals.length).toBeGreaterThanOrEqual(NUM_AGENTS);
      console.log(`Verified ${agentsWithBuySignals.length} agents have buy signal set to 20`);

      // Send Open Bar signals for all agents
      const openBarResponses = await sendConcurrentWebhooksForAgents(
        testAgents.map(agent => agent.botId),
        [{ action: 'confirmationOpenBar', signalName: 'buyOpenBar' }]
      );

      // Verify Open Bar signals were received for all agents
      const agentsWithOpenBar = await Agent.find({ 'signals.buyOpenBar': 1 });
      expect(agentsWithOpenBar.length).toBeGreaterThanOrEqual(NUM_AGENTS);
      console.log(`Verified ${agentsWithOpenBar.length} agents have buyOpenBar signal set to 1`);

      // Send final buy actions for all agents
      const buyResponses = await sendConcurrentWebhooksForAgents(
        testAgents.map(agent => agent.botId),
        [{ action: 'buy', signalName: 'buy' }]
      );

      // Add a longer delay to ensure all jobs are processed
      await delay(5000);

      // Verify final state for all agents
      const finalAgents = await Agent.find({});
      expect(finalAgents.length).toBeGreaterThanOrEqual(NUM_AGENTS);

      for (const agent of finalAgents) {
        console.log(`Final signal values for agent ${agent.botId}:`, {
          buy: agent.signals.buy,
          sell: agent.signals.sell,
          buyOpenBar: agent.signals.buyOpenBar,
          sellOpenBar: agent.signals.sellOpenBar
        });

        // All signals should be reset after opening the position
        expect(agent.signals.buy).toBe(0);
        expect(agent.signals.buyOpenBar).toBe(0);
      }
    }, 120000); // Increased timeout for multiple agents

    test('Should open long positions for 100 agents on Buy Confirm x 20 (Minute Bar) + Buy (Open Bar)', async () => {
      const NUM_AGENTS_100 = 100;
      const testAgents100 = [];

      try {
        // Ensure we start with a clean slate
        await Agent.deleteMany({ botId: { $regex: '^test-agent-100-' } });

        // Create 100 test agents
        console.log(`Creating ${NUM_AGENTS_100} test agents...`);
        for (let i = 0; i < NUM_AGENTS_100; i++) {
          const agentId = `test-agent-100-${i}`;
          const chatId = `test-chat-100-${i}`;
          const agent = await Agent.create({
            agentName: `test-agent-100-${i}`,
            deposit: 1000,
            botId: agentId,
            chatId: chatId,
            assetPair: testTicker,
            tradingStatus: 'on',
            number: i + 1000, // Use different numbers to avoid conflicts
            pubkey: 'AUQKdAdqSi9bfjJyYuaoHNARfqsF6W38PpLBeaWstGw',
            signals: {
              buy: 20, // Set directly to avoid update operations
              sell: 0,
              buyOpenBar: 0,
              sellOpenBar: 0
            },
            requiredBuyConfirmationsClose: 15,
            requiredSellConfirmationsClose: 15,
            requiredBuyConfirmationsOpen: 20,
            requiredSellConfirmationsOpen: 20,
            requiredBuyConfirmationsOpenBar: 1,
            requiredSellConfirmationsOpenBar: 1,
            requiredBuyConfirmationsResetCounter: 4,
            requiredSellConfirmationsResetCounter: 4
          });
          testAgents100.push(agent);
        }

        // Verify all agents were created
        const createdAgents = await Agent.find({ botId: { $regex: '^test-agent-100-' } });
        console.log(`Created ${createdAgents.length} agents`);
        expect(createdAgents.length).toBe(NUM_AGENTS_100);

        // Send Open Bar signals for all agents
        await sendConcurrentWebhooksForAgents(
          testAgents100.map(agent => agent.botId),
          [{ action: 'confirmationOpenBar', signalName: 'buyOpenBar' }]
        );

        // Verify Open Bar signals were received
        const agentsWithOpenBar = await Agent.find({
          botId: { $regex: '^test-agent-100-' },
          'signals.buyOpenBar': 1
        });
        console.log(`Verified ${agentsWithOpenBar.length} agents have buyOpenBar signal set to 1`);

        // Send final buy actions for all agents
        await sendConcurrentWebhooksForAgents(
          testAgents100.map(agent => agent.botId),
          [{ action: 'buy', signalName: 'buy' }]
        );

        // Add a longer delay to ensure all jobs are processed
        await delay(15000);

        // Verify final state for all agents
        const finalAgents = await Agent.find({ botId: { $regex: '^test-agent-100-' } });
        console.log(`Found ${finalAgents.length} agents after processing`);
        expect(finalAgents.length).toBe(NUM_AGENTS_100);

        for (const agent of finalAgents) {
          expect(agent.signals.buy).toBe(0);
          expect(agent.signals.buyOpenBar).toBe(0);
        }

      } finally {
        // Cleanup - always run this even if test fails
        try {
          await Agent.deleteMany({ botId: { $regex: '^test-agent-100-' } });
          console.log("Cleaned up 100-agent test data");
        } catch (cleanupError) {
          console.log("Error during 100-agent test cleanup:", cleanupError);
        }
      }
    }, 240000); // Increased timeout for 100 agents

    test.skip('Should open long positions for 1000 agents on Buy Confirm x 20 (Minute Bar) + Buy (Open Bar)', async () => {
      const NUM_AGENTS_1000 = 1000;
      const testAgents1000 = [];

      // Create 1000 test agents
      for (let i = 0; i < NUM_AGENTS_1000; i++) {
        const agentId = `test-agent-1000-${i}`;
        const chatId = `test-chat-1000-${i}`;
        const agent = await Agent.create({
          agentName: `test-agent-1000-${i}`,
          deposit: 1000,
          botId: agentId,
          chatId: chatId,
          assetPair: testTicker,
          tradingStatus: 'on',
          number: i,
          pubkey: 'AUQKdAdqSi9bfjJyYuaoHNARfqsF6W38PpLBeaWstGw',
          signals: {
            buy: 0,
            sell: 0,
            buyOpenBar: 0,
            sellOpenBar: 0
          },
          requiredBuyConfirmationsClose: 15,
          requiredSellConfirmationsClose: 15,
          requiredBuyConfirmationsOpen: 20,
          requiredSellConfirmationsOpen: 20,
          requiredBuyConfirmationsOpenBar: 1,
          requiredSellConfirmationsOpenBar: 1,
          requiredBuyConfirmationsResetCounter: 4,
          requiredSellConfirmationsResetCounter: 4
        });
        testAgents1000.push(agent);
      }

      // Reset all agents' state
      for (const agent of testAgents1000) {
        await Agent.updateOne(
          { botId: agent.botId },
          { $set: {
            'signals.buy': 0,
            'signals.sell': 0,
            'signals.buyOpenBar': 0,
            'signals.sellOpenBar': 0
          }}
        );
      }

      // Set buy confirmation count to 20 for all agents
      for (const agent of testAgents1000) {
        await Agent.updateOne(
          { botId: agent.botId },
          { $set: { 'signals.buy': 20 }}
        );
      }

      // Send Open Bar signals for all agents
      const openBarResponses = await sendConcurrentWebhooksForAgents(
        testAgents1000.map(agent => agent.botId),
        [{ action: 'confirmationOpenBar', signalName: 'buyOpenBar' }]
      );

      // Send final buy actions for all agents
      const buyResponses = await sendConcurrentWebhooksForAgents(
        testAgents1000.map(agent => agent.botId),
        [{ action: 'buy', signalName: 'buy' }]
      );

      // Add a longer delay to ensure all jobs are processed
      await delay(20000);

      // Verify final state for all agents
      const finalAgents = await Agent.find({ botId: { $regex: '^test-agent-1000-' } });
      expect(finalAgents.length).toBe(NUM_AGENTS_1000);

      for (const agent of finalAgents) {
        expect(agent.signals.buy).toBe(0);
        expect(agent.signals.buyOpenBar).toBe(0);
      }

      // Cleanup
      await Agent.deleteMany({ botId: { $regex: '^test-agent-1000-' } });
    }, 300000); // Increased timeout for 1000 agents

    test.skip('Should open long positions for 10000 agents on Buy Confirm x 20 (Minute Bar) + Buy (Open Bar)', async () => {
      const NUM_AGENTS_10000 = 10000;
      const testAgents10000 = [];

      // Create 10000 test agents
      for (let i = 0; i < NUM_AGENTS_10000; i++) {
        const agentId = `test-agent-10000-${i}`;
        const chatId = `test-chat-10000-${i}`;
        const agent = await Agent.create({
          agentName: `test-agent-10000-${i}`,
          deposit: 1000,
          botId: agentId,
          chatId: chatId,
          assetPair: testTicker,
          tradingStatus: 'on',
          number: i,
          pubkey: 'AUQKdAdqSi9bfjJyYuaoHNARfqsF6W38PpLBeaWstGw',
          signals: {
            buy: 0,
            sell: 0,
            buyOpenBar: 0,
            sellOpenBar: 0
          },
          requiredBuyConfirmationsClose: 15,
          requiredSellConfirmationsClose: 15,
          requiredBuyConfirmationsOpen: 20,
          requiredSellConfirmationsOpen: 20,
          requiredBuyConfirmationsOpenBar: 1,
          requiredSellConfirmationsOpenBar: 1,
          requiredBuyConfirmationsResetCounter: 4,
          requiredSellConfirmationsResetCounter: 4
        });
        testAgents10000.push(agent);
      }

      // Reset all agents' state
      for (const agent of testAgents10000) {
        await Agent.updateOne(
          { botId: agent.botId },
          { $set: {
            'signals.buy': 0,
            'signals.sell': 0,
            'signals.buyOpenBar': 0,
            'signals.sellOpenBar': 0
          }}
        );
      }

      // Set buy confirmation count to 20 for all agents
      for (const agent of testAgents10000) {
        await Agent.updateOne(
          { botId: agent.botId },
          { $set: { 'signals.buy': 20 }}
        );
      }

      // Send Open Bar signals for all agents
      const openBarResponses = await sendConcurrentWebhooksForAgents(
        testAgents10000.map(agent => agent.botId),
        [{ action: 'confirmationOpenBar', signalName: 'buyOpenBar' }]
      );

      // Send final buy actions for all agents
      const buyResponses = await sendConcurrentWebhooksForAgents(
        testAgents10000.map(agent => agent.botId),
        [{ action: 'buy', signalName: 'buy' }]
      );

      // Add a longer delay to ensure all jobs are processed
      await delay(60000);

      // Verify final state for all agents
      const finalAgents = await Agent.find({ botId: { $regex: '^test-agent-10000-' } });
      expect(finalAgents.length).toBe(NUM_AGENTS_10000);

      for (const agent of finalAgents) {
        expect(agent.signals.buy).toBe(0);
        expect(agent.signals.buyOpenBar).toBe(0);
      }

      // Cleanup
      await Agent.deleteMany({ botId: { $regex: '^test-agent-10000-' } });
    }, 600000); // Increased timeout for 10000 agents
  });
});