import { Request, Response, NextFunction } from 'express';
import { webhookFilterMiddleware } from '../../src/middleware/webhookFilterMiddleware';
import Agent from '../../src/databaseModels/agent';

// Mock the Agent model
jest.mock('../../src/databaseModels/agent');

// Mock the logger
jest.mock('../../src/utils/logger', () => ({
  logInfo: jest.fn(),
  logWarn: jest.fn(),
  logError: jest.fn(),
}));

describe('Webhook Filter Middleware', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: NextFunction;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Setup request, response, and next function
    req = {
      params: { agentId: 'test-agent-id' },
      body: {
        action: 'confirmationMinute',
        signalName: 'buy',
        ticker: 'BTC'
      }
    };

    res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn()
    };

    next = jest.fn();
  });

  test('should return 400 if agentId is missing', async () => {
    req.params = {}; // No agentId

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.send).toHaveBeenCalledWith('Missing agentId');
    expect(next).not.toHaveBeenCalled();
  });

  test('should return 400 if action is missing', async () => {
    req.body.action = undefined; // No action

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.send).toHaveBeenCalledWith('Missing action');
    expect(next).not.toHaveBeenCalled();
  });

  test('should return 400 if signalName is missing for confirmationMinute action', async () => {
    req.body.signalName = undefined; // No signalName

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.send).toHaveBeenCalledWith('Missing signalName for confirmationMinute action');
    expect(next).not.toHaveBeenCalled();
  });

  test('should return 400 if ticker is missing', async () => {
    req.body.ticker = undefined; // No ticker

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.send).toHaveBeenCalledWith('Missing ticker');
    expect(next).not.toHaveBeenCalled();
  });

  test('should call next() if action is not confirmationMinute', async () => {
    req.body.action = 'someOtherAction';

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });

  test('should call next() if signalName is not buy or sell', async () => {
    req.body.signalName = 'someOtherSignal';

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });

  test('should filter buy signal if signals.buy > max of all buy thresholds', async () => {
    // Mock Agent.findOne to return an agent with signals.buy > max of all thresholds
    const mockLean = jest.fn().mockResolvedValue({
      signals: { buy: 25 },
      requiredBuyConfirmationsOpen: 20,
      requiredBuyConfirmationsClose: 15,
      requiredBuyConfirmationsResetCounter: 4,
      requiredBuyConfirmationsOverride: 10
    });

    (Agent.findOne as jest.Mock).mockReturnValue({
      lean: mockLean
    });

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(Agent.findOne).toHaveBeenCalledWith(
      { botId: 'test-agent-id' },
      {
        'signals.buy': 1,
        'requiredBuyConfirmationsOpen': 1,
        'requiredBuyConfirmationsClose': 1,
        'requiredBuyConfirmationsResetCounter': 1,
        'requiredBuyConfirmationsOverride': 1
      }
    );
    expect(res.status).toHaveBeenCalledWith(202);
    expect(res.send).toHaveBeenCalledWith('Request filtered: buy signal threshold already met');
    expect(next).not.toHaveBeenCalled();
  });

  test('should filter sell signal if signals.sell > max of all sell thresholds', async () => {
    req.body.signalName = 'sell';

    // Mock Agent.findOne to return an agent with signals.sell > max of all thresholds
    const mockLean = jest.fn().mockResolvedValue({
      signals: { sell: 25 },
      requiredSellConfirmationsOpen: 20,
      requiredSellConfirmationsClose: 15,
      requiredSellConfirmationsResetCounter: 4,
      requiredSellConfirmationsOverride: 10
    });

    (Agent.findOne as jest.Mock).mockReturnValue({
      lean: mockLean
    });

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(Agent.findOne).toHaveBeenCalledWith(
      { botId: 'test-agent-id' },
      {
        'signals.sell': 1,
        'requiredSellConfirmationsOpen': 1,
        'requiredSellConfirmationsClose': 1,
        'requiredSellConfirmationsResetCounter': 1,
        'requiredSellConfirmationsOverride': 1
      }
    );
    expect(res.status).toHaveBeenCalledWith(202);
    expect(res.send).toHaveBeenCalledWith('Request filtered: sell signal threshold already met');
    expect(next).not.toHaveBeenCalled();
  });

  test('should call next() if signals.buy <= max of all buy thresholds', async () => {
    // Mock Agent.findOne to return an agent with signals.buy <= max of all thresholds
    const mockLean = jest.fn().mockResolvedValue({
      signals: { buy: 20 },
      requiredBuyConfirmationsOpen: 20,
      requiredBuyConfirmationsClose: 15,
      requiredBuyConfirmationsResetCounter: 4,
      requiredBuyConfirmationsOverride: 10
    });

    (Agent.findOne as jest.Mock).mockReturnValue({
      lean: mockLean
    });

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });

  test('should call next() if agent not found', async () => {
    // Mock Agent.findOne to return null (agent not found)
    const mockLean = jest.fn().mockResolvedValue(null);

    (Agent.findOne as jest.Mock).mockReturnValue({
      lean: mockLean
    });

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });

  test('should call next() if an error occurs', async () => {
    // Mock Agent.findOne to throw an error
    // First, create a mock implementation that returns a chainable object
    const mockFindOne = jest.fn().mockImplementation(() => {
      return {
        lean: jest.fn().mockImplementation(() => {
          throw new Error('Database error');
        })
      };
    });

    // Replace the implementation for this test only
    (Agent.findOne as jest.Mock) = mockFindOne;

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });

  // ===== ADDITIONAL INPUT VALIDATION TESTS =====

  describe('agentId variations', () => {
    test('should return 400 if agentId is empty string', async () => {
      req.params = { agentId: '' };

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith('Missing agentId');
      expect(next).not.toHaveBeenCalled();
    });

    test('should return 400 if agentId is null', async () => {
      req.params = { agentId: null as any };

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith('Missing agentId');
      expect(next).not.toHaveBeenCalled();
    });

    test('should handle very long agentId', async () => {
      const longAgentId = 'a'.repeat(1000);
      req.params = { agentId: longAgentId };

      // Mock Agent.findOne to return null (agent not found)
      const mockLean = jest.fn().mockResolvedValue(null);
      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(Agent.findOne).toHaveBeenCalledWith(
        { botId: longAgentId },
        expect.any(Object)
      );
      expect(next).toHaveBeenCalled();
    });

    test('should handle special characters in agentId', async () => {
      const specialAgentId = '!@#$%^&*()';
      req.params = { agentId: specialAgentId };

      // Mock Agent.findOne to return null (agent not found)
      const mockLean = jest.fn().mockResolvedValue(null);
      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(Agent.findOne).toHaveBeenCalledWith(
        { botId: specialAgentId },
        expect.any(Object)
      );
      expect(next).toHaveBeenCalled();
    });

    test('should handle numeric agentId by converting to string', async () => {
      const numericAgentId = 12345;
      req.params = { agentId: numericAgentId as any };

      // Mock Agent.findOne to return null (agent not found)
      const mockLean = jest.fn().mockResolvedValue(null);
      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(Agent.findOne).toHaveBeenCalledWith(
        { botId: '12345' },
        expect.any(Object)
      );
      expect(next).toHaveBeenCalled();
    });
  });

  describe('action field variations', () => {
    test('should return 400 if action is empty string', async () => {
      req.body.action = '';

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith('Missing action');
      expect(next).not.toHaveBeenCalled();
    });

    test('should return 400 if action is null', async () => {
      req.body.action = null;

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith('Missing action');
      expect(next).not.toHaveBeenCalled();
    });

    test('should proceed if action is a number (truthy value)', async () => {
      req.body.action = 123;

      // Mock Agent.findOne to return null (agent not found)
      const mockLean = jest.fn().mockResolvedValue(null);
      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since action is truthy, even though it's not 'confirmationMinute'
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should proceed if action is an object (truthy value)', async () => {
      req.body.action = { type: 'confirmationMinute' };

      // Mock Agent.findOne to return null (agent not found)
      const mockLean = jest.fn().mockResolvedValue(null);
      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since action is truthy, even though it's not 'confirmationMinute'
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should proceed if action is an array (truthy value)', async () => {
      req.body.action = ['confirmationMinute'];

      // Mock Agent.findOne to return null (agent not found)
      const mockLean = jest.fn().mockResolvedValue(null);
      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since action is truthy, even though it's not 'confirmationMinute'
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle case-insensitive action comparison', async () => {
      req.body.action = 'CONFIRMATIONMINUTE';

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed to next() since action is not exactly 'confirmationMinute'
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle action with leading/trailing whitespace', async () => {
      req.body.action = ' confirmationMinute ';

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed to next() since action is not exactly 'confirmationMinute'
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });
  });

  describe('signalName variations', () => {
    test('should return 400 if signalName is empty string for confirmationMinute action', async () => {
      req.body.signalName = '';

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith('Missing signalName for confirmationMinute action');
      expect(next).not.toHaveBeenCalled();
    });

    test('should return 400 if signalName is null for confirmationMinute action', async () => {
      req.body.signalName = null;

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith('Missing signalName for confirmationMinute action');
      expect(next).not.toHaveBeenCalled();
    });

    test('should handle case-sensitive signalName comparison - BUY', async () => {
      req.body.signalName = 'BUY';

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed to next() since signalName is not exactly 'buy' or 'sell'
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle case-sensitive signalName comparison - Buy', async () => {
      req.body.signalName = 'Buy';

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed to next() since signalName is not exactly 'buy' or 'sell'
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle case-sensitive signalName comparison - SELL', async () => {
      req.body.signalName = 'SELL';

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed to next() since signalName is not exactly 'buy' or 'sell'
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle signalName with whitespace', async () => {
      req.body.signalName = ' buy ';

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed to next() since signalName is not exactly 'buy' or 'sell'
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should proceed if signalName is a number (truthy value) for confirmationMinute action', async () => {
      req.body.signalName = 123;

      // Mock Agent.findOne to return null (agent not found)
      const mockLean = jest.fn().mockResolvedValue(null);
      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since signalName is truthy, even though it's not 'buy' or 'sell'
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should proceed if signalName is an object (truthy value) for confirmationMinute action', async () => {
      req.body.signalName = { type: 'buy' };

      // Mock Agent.findOne to return null (agent not found)
      const mockLean = jest.fn().mockResolvedValue(null);
      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since signalName is truthy, even though it's not 'buy' or 'sell'
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });
  });

  describe('ticker variations', () => {
    test('should return 400 if ticker is empty string', async () => {
      req.body.ticker = '';

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith('Missing ticker');
      expect(next).not.toHaveBeenCalled();
    });

    test('should return 400 if ticker is null', async () => {
      req.body.ticker = null;

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.send).toHaveBeenCalledWith('Missing ticker');
      expect(next).not.toHaveBeenCalled();
    });

    test('should handle very long ticker', async () => {
      const longTicker = 'A'.repeat(1000);
      req.body.ticker = longTicker;

      // Mock Agent.findOne to return null (agent not found)
      const mockLean = jest.fn().mockResolvedValue(null);
      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle special characters in ticker', async () => {
      req.body.ticker = 'BTC-USD@#$';

      // Mock Agent.findOne to return null (agent not found)
      const mockLean = jest.fn().mockResolvedValue(null);
      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should proceed if ticker is a number (truthy value)', async () => {
      req.body.ticker = 123;

      // Mock Agent.findOne to return null (agent not found)
      const mockLean = jest.fn().mockResolvedValue(null);
      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since ticker is truthy
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should proceed if ticker is an object (truthy value)', async () => {
      req.body.ticker = { symbol: 'BTC' };

      // Mock Agent.findOne to return null (agent not found)
      const mockLean = jest.fn().mockResolvedValue(null);
      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since ticker is truthy
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });
  });

  describe('boundary condition tests', () => {
    test('should call next() when buy signal equals max threshold', async () => {
      // Mock Agent.findOne to return an agent with signals.buy = max threshold
      const mockLean = jest.fn().mockResolvedValue({
        signals: { buy: 20 },
        requiredBuyConfirmationsOpen: 20,
        requiredBuyConfirmationsClose: 15,
        requiredBuyConfirmationsResetCounter: 4,
        requiredBuyConfirmationsOverride: 10
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should filter when buy signal is one above max threshold', async () => {
      // Mock Agent.findOne to return an agent with signals.buy = max threshold + 1
      const mockLean = jest.fn().mockResolvedValue({
        signals: { buy: 21 },
        requiredBuyConfirmationsOpen: 20,
        requiredBuyConfirmationsClose: 15,
        requiredBuyConfirmationsResetCounter: 4,
        requiredBuyConfirmationsOverride: 10
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(202);
      expect(res.send).toHaveBeenCalledWith('Request filtered: buy signal threshold already met');
      expect(next).not.toHaveBeenCalled();
    });

    test('should handle case where all buy thresholds are equal', async () => {
      // Mock Agent.findOne to return an agent with all thresholds equal
      const mockLean = jest.fn().mockResolvedValue({
        signals: { buy: 16 },
        requiredBuyConfirmationsOpen: 15,
        requiredBuyConfirmationsClose: 15,
        requiredBuyConfirmationsResetCounter: 15,
        requiredBuyConfirmationsOverride: 15
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(202);
      expect(res.send).toHaveBeenCalledWith('Request filtered: buy signal threshold already met');
      expect(next).not.toHaveBeenCalled();
    });

    test('should call next() when sell signal equals max threshold', async () => {
      req.body.signalName = 'sell';

      // Mock Agent.findOne to return an agent with signals.sell = max threshold
      const mockLean = jest.fn().mockResolvedValue({
        signals: { sell: 20 },
        requiredSellConfirmationsOpen: 20,
        requiredSellConfirmationsClose: 15,
        requiredSellConfirmationsResetCounter: 4,
        requiredSellConfirmationsOverride: 10
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should filter when sell signal is one above max threshold', async () => {
      req.body.signalName = 'sell';

      // Mock Agent.findOne to return an agent with signals.sell = max threshold + 1
      const mockLean = jest.fn().mockResolvedValue({
        signals: { sell: 21 },
        requiredSellConfirmationsOpen: 20,
        requiredSellConfirmationsClose: 15,
        requiredSellConfirmationsResetCounter: 4,
        requiredSellConfirmationsOverride: 10
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(202);
      expect(res.send).toHaveBeenCalledWith('Request filtered: sell signal threshold already met');
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('agent data edge cases', () => {
    test('should handle agent with missing signals object', async () => {
      // Mock Agent.findOne to return an agent without signals object
      const mockLean = jest.fn().mockResolvedValue({
        requiredBuyConfirmationsOpen: 10,
        requiredBuyConfirmationsClose: 5,
        requiredBuyConfirmationsResetCounter: 3,
        requiredBuyConfirmationsOverride: 8
        // signals object missing
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since buySignal defaults to 0 and 0 <= max threshold
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle agent with null signals', async () => {
      // Mock Agent.findOne to return an agent with null signals
      const mockLean = jest.fn().mockResolvedValue({
        signals: null,
        requiredBuyConfirmationsOpen: 10,
        requiredBuyConfirmationsClose: 5,
        requiredBuyConfirmationsResetCounter: 3,
        requiredBuyConfirmationsOverride: 8
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since buySignal defaults to 0 and 0 <= max threshold
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle agent with undefined buy signal', async () => {
      // Mock Agent.findOne to return an agent with undefined buy signal
      const mockLean = jest.fn().mockResolvedValue({
        signals: { sell: 5 }, // buy is undefined
        requiredBuyConfirmationsOpen: 10,
        requiredBuyConfirmationsClose: 5,
        requiredBuyConfirmationsResetCounter: 3,
        requiredBuyConfirmationsOverride: 8
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since buySignal defaults to 0 and 0 <= max threshold
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle negative signal values', async () => {
      // Mock Agent.findOne to return an agent with negative signal
      const mockLean = jest.fn().mockResolvedValue({
        signals: { buy: -5 },
        requiredBuyConfirmationsOpen: 10,
        requiredBuyConfirmationsClose: 5,
        requiredBuyConfirmationsResetCounter: 3,
        requiredBuyConfirmationsOverride: 8
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since -5 <= max threshold (10)
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle very large signal values', async () => {
      // Mock Agent.findOne to return an agent with very large signal
      const mockLean = jest.fn().mockResolvedValue({
        signals: { buy: Number.MAX_SAFE_INTEGER },
        requiredBuyConfirmationsOpen: 10,
        requiredBuyConfirmationsClose: 5,
        requiredBuyConfirmationsResetCounter: 3,
        requiredBuyConfirmationsOverride: 8
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should filter since MAX_SAFE_INTEGER > max threshold (10)
      expect(res.status).toHaveBeenCalledWith(202);
      expect(res.send).toHaveBeenCalledWith('Request filtered: buy signal threshold already met');
      expect(next).not.toHaveBeenCalled();
    });

    test('should handle non-numeric signal values', async () => {
      // Mock Agent.findOne to return an agent with non-numeric signal
      const mockLean = jest.fn().mockResolvedValue({
        signals: { buy: 'not-a-number' as any },
        requiredBuyConfirmationsOpen: 10,
        requiredBuyConfirmationsClose: 5,
        requiredBuyConfirmationsResetCounter: 3,
        requiredBuyConfirmationsOverride: 8
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since non-numeric signal defaults to 0 via || operator
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });
  });

  describe('mathematical edge cases', () => {
    test('should handle zero signals and thresholds', async () => {
      // Mock Agent.findOne to return an agent with all zero values
      const mockLean = jest.fn().mockResolvedValue({
        signals: { buy: 0 },
        requiredBuyConfirmationsOpen: 0,
        requiredBuyConfirmationsClose: 0,
        requiredBuyConfirmationsResetCounter: 0,
        requiredBuyConfirmationsOverride: 0
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since 0 <= max(0, 0, 0, 0) = 0
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle undefined/null thresholds', async () => {
      // Mock Agent.findOne to return an agent with undefined/null thresholds
      const mockLean = jest.fn().mockResolvedValue({
        signals: { buy: 5 },
        requiredBuyConfirmationsOpen: undefined,
        requiredBuyConfirmationsClose: null,
        requiredBuyConfirmationsResetCounter: undefined,
        requiredBuyConfirmationsOverride: null
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should filter since 5 > max(0, 0, 0, 0) = 0 (undefined/null default to 0)
      expect(res.status).toHaveBeenCalledWith(202);
      expect(res.send).toHaveBeenCalledWith('Request filtered: buy signal threshold already met');
      expect(next).not.toHaveBeenCalled();
    });

    test('should handle floating point signal values', async () => {
      // Mock Agent.findOne to return an agent with floating point signal
      const mockLean = jest.fn().mockResolvedValue({
        signals: { buy: 10.5 },
        requiredBuyConfirmationsOpen: 10,
        requiredBuyConfirmationsClose: 5,
        requiredBuyConfirmationsResetCounter: 3,
        requiredBuyConfirmationsOverride: 8
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should filter since 10.5 > max(10, 5, 3, 8) = 10
      expect(res.status).toHaveBeenCalledWith(202);
      expect(res.send).toHaveBeenCalledWith('Request filtered: buy signal threshold already met');
      expect(next).not.toHaveBeenCalled();
    });

    test('should handle negative threshold values', async () => {
      // Mock Agent.findOne to return an agent with negative thresholds
      const mockLean = jest.fn().mockResolvedValue({
        signals: { buy: 5 },
        requiredBuyConfirmationsOpen: -10,
        requiredBuyConfirmationsClose: -5,
        requiredBuyConfirmationsResetCounter: -3,
        requiredBuyConfirmationsOverride: -8
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should filter since 5 > max(-10, -5, -3, -8) = -3
      expect(res.status).toHaveBeenCalledWith(202);
      expect(res.send).toHaveBeenCalledWith('Request filtered: buy signal threshold already met');
      expect(next).not.toHaveBeenCalled();
    });

    test('should handle very large threshold values', async () => {
      // Mock Agent.findOne to return an agent with very large thresholds
      const mockLean = jest.fn().mockResolvedValue({
        signals: { buy: 100 },
        requiredBuyConfirmationsOpen: Number.MAX_SAFE_INTEGER,
        requiredBuyConfirmationsClose: Number.MAX_SAFE_INTEGER - 1,
        requiredBuyConfirmationsResetCounter: Number.MAX_SAFE_INTEGER - 2,
        requiredBuyConfirmationsOverride: Number.MAX_SAFE_INTEGER - 3
      });

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since 100 <= max(MAX_SAFE_INTEGER, ...) = MAX_SAFE_INTEGER
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });
  });

  describe('database error scenarios', () => {
    test('should handle database timeout gracefully', async () => {
      // Mock Agent.findOne to simulate timeout
      const mockFindOne = jest.fn().mockImplementation(() => ({
        lean: jest.fn().mockRejectedValue(new Error('Database timeout'))
      }));

      (Agent.findOne as jest.Mock) = mockFindOne;

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed with normal processing on error (fail-open approach)
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle invalid ObjectId format', async () => {
      req.params = { agentId: 'invalid-object-id-format' };

      // Mock Agent.findOne to simulate ObjectId error
      const mockFindOne = jest.fn().mockImplementation(() => ({
        lean: jest.fn().mockRejectedValue(new Error('Cast to ObjectId failed'))
      }));

      (Agent.findOne as jest.Mock) = mockFindOne;

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed with normal processing on error (fail-open approach)
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle mongoose validation errors', async () => {
      // Mock Agent.findOne to simulate validation error
      const mockFindOne = jest.fn().mockImplementation(() => ({
        lean: jest.fn().mockRejectedValue(new Error('Validation failed'))
      }));

      (Agent.findOne as jest.Mock) = mockFindOne;

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed with normal processing on error (fail-open approach)
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle connection lost during query', async () => {
      // Mock Agent.findOne to simulate connection error
      const mockFindOne = jest.fn().mockImplementation(() => ({
        lean: jest.fn().mockRejectedValue(new Error('Connection lost'))
      }));

      (Agent.findOne as jest.Mock) = mockFindOne;

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed with normal processing on error (fail-open approach)
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should handle invalid database response format', async () => {
      // Mock Agent.findOne to return unexpected response format
      const mockLean = jest.fn().mockResolvedValue('invalid-response-format');

      (Agent.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

      await webhookFilterMiddleware(req as Request, res as Response, next);

      // Should proceed since the code handles unexpected formats gracefully
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });
  });
});
