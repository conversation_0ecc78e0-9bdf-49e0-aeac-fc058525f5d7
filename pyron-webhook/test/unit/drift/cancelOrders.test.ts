// Mock BN first to avoid constructor issues
jest.mock('bn.js', () => {
  const mockBN = jest.fn().mockImplementation((value) => ({
    toNumber: () => Number(value),
    toString: () => String(value),
    add: jest.fn().mockReturnThis(),
    sub: jest.fn().mockReturnThis(),
    mul: jest.fn().mockReturnThis(),
    div: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnValue(true),
    gt: jest.fn().mockReturnValue(false),
    lt: jest.fn().mockReturnValue(false),
    gte: jest.fn().mockReturnValue(true),
    lte: jest.fn().mockReturnValue(true),
  }));

  return {
    BN: mockBN,
    default: mockBN,
  };
});

// Mock Solana Web3.js completely
jest.mock('@solana/web3.js', () => ({
  PublicKey: jest.fn().mockImplementation((key) => ({
    toBase58: () => typeof key === 'string' ? key : '********************************',
    toString: () => typeof key === 'string' ? key : '********************************',
    equals: jest.fn().mockReturnValue(true),
  })),
  Keypair: {
    generate: jest.fn().mockReturnValue({
      publicKey: {
        toBase58: () => '********************************',
        toString: () => '********************************',
      },
      secretKey: new Uint8Array(64),
    }),
  },
  Connection: jest.fn(),
  Transaction: jest.fn(),
  SystemProgram: {
    transfer: jest.fn(),
  },
  LAMPORTS_PER_SOL: 1000000000,
}));

// Mock external dependencies
jest.mock('../../../src/utils/drift/getClient');
jest.mock('@drift-labs/sdk', () => ({
  DriftClient: jest.fn(),
  Wallet: jest.fn(),
  DRIFT_PROGRAM_ID: 'DRiFtupJYLTosbwoN8koMbEYSx54aFAVLddWsbksjwg7',
  OrderType: {
    MARKET: 'market',
    LIMIT: 'limit',
  },
  PositionDirection: {
    LONG: 'long',
    SHORT: 'short',
  },
  AMM_RESERVE_PRECISION_EXP: 6,
}));

// Mock Light Protocol
jest.mock('@lightprotocol/stateless.js', () => ({
  Rpc: jest.fn(),
  sendAndConfirmTx: jest.fn(),
}));

// Mock the logger
jest.mock('../../../src/utils/logger', () => ({
  logAgentInfo: jest.fn(),
  logAgentWarn: jest.fn(),
  logAgentError: jest.fn(),
}));

import { Connection, PublicKey, Keypair } from '@solana/web3.js';
import { cancelOrders } from '../../../src/utils/drift/cancelOrders';
import { createClient } from '../../../src/utils/drift/getClient';

const mockedCreateClient = createClient as jest.MockedFunction<typeof createClient>;

describe('cancelOrders', () => {
  let mockDriftClient: any;
  let mockUser: any;
  let mockConnection: Connection;
  let mockSigner: Keypair;
  let mockAuthority: PublicKey;
  let mockAgent: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock objects
    mockConnection = {} as Connection;
    mockSigner = (Keypair as any).generate();
    mockAuthority = new (PublicKey as any)('********************************');
    
    // Mock agent object
    mockAgent = {
      botId: 'test-bot-id',
      chatId: 'test-chat-id',
      number: 0,
      pubkey: '22222222222222222222222222222222',
    };
    
    // Mock user with orders
    mockUser = {
      getOpenOrders: jest.fn().mockReturnValue([
        {
          orderId: 1,
          direction: 'long',
          status: 'open',
          marketType: 'perp',
          marketIndex: 0,
        },
        {
          orderId: 2,
          direction: 'short',
          status: 'open',
          marketType: 'perp',
          marketIndex: 0,
        },
        {
          orderId: 3,
          direction: 'long',
          status: 'filled',
          marketType: 'perp',
          marketIndex: 0,
        },
        {
          orderId: 4,
          direction: 'short',
          status: 'open',
          marketType: 'spot',
          marketIndex: 0,
        },
        {
          orderId: 5,
          direction: 'long',
          status: 'open',
          marketType: 'perp',
          marketIndex: 1, // Different market
        },
      ]),
    } as any;
    
    // Mock drift client
    mockDriftClient = {
      subscribe: jest.fn().mockResolvedValue(undefined),
      unsubscribe: jest.fn().mockResolvedValue(undefined),
      getUser: jest.fn().mockReturnValue(mockUser),
      cancelOrder: jest.fn().mockResolvedValue(undefined),
    } as any;
    
    // Set up default mocks
    mockedCreateClient.mockResolvedValue(mockDriftClient);
  });

  describe('Valid order cancellation', () => {
    it('should cancel all orders when side is not specified', async () => {
      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(mockedCreateClient).toHaveBeenCalledWith(mockConnection, mockSigner, mockAuthority);
      expect(mockDriftClient.subscribe).toHaveBeenCalled();
      expect(mockDriftClient.getUser).toHaveBeenCalledWith(0, expect.any(Object));
      
      // Should cancel orders 1 and 2 (both open perp orders for market 0)
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledTimes(2);
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledWith(1, {}, 0);
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledWith(2, {}, 0);
      expect(mockDriftClient.unsubscribe).toHaveBeenCalled();
    });

    it('should cancel only long orders when side is "long"', async () => {
      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'long');

      // Should cancel only order 1 (long open perp order for market 0)
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledTimes(1);
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledWith(1, {}, 0);
    });

    it('should cancel only short orders when side is "short"', async () => {
      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'short');

      // Should cancel only order 2 (short open perp order for market 0)
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledTimes(1);
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledWith(2, {}, 0);
    });

    it('should filter orders by market index correctly', async () => {
      await cancelOrders(mockAgent, 1, mockConnection, mockSigner, mockAuthority, 'all');

      // Should cancel only order 5 (open perp order for market 1)
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledTimes(1);
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledWith(5, {}, 0);
    });

    it('should handle agent with chatId instead of botId', async () => {
      const agentWithChatId = {
        chatId: 'test-chat-id',
        number: 0,
        pubkey: '22222222222222222222222222222222',
      };

      await cancelOrders(agentWithChatId, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(mockDriftClient.cancelOrder).toHaveBeenCalledTimes(2);
    });
  });

  describe('Order not found scenarios', () => {
    it('should handle empty order list gracefully', async () => {
      mockUser.getOpenOrders.mockReturnValue([]);

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(mockDriftClient.cancelOrder).not.toHaveBeenCalled();
      expect(mockDriftClient.unsubscribe).toHaveBeenCalled();
    });

    it('should handle no matching orders for market', async () => {
      await cancelOrders(mockAgent, 999, mockConnection, mockSigner, mockAuthority, 'all');

      expect(mockDriftClient.cancelOrder).not.toHaveBeenCalled();
      expect(mockDriftClient.unsubscribe).toHaveBeenCalled();
    });

    it('should handle no matching orders for side', async () => {
      // Set up orders with only long positions
      mockUser.getOpenOrders.mockReturnValue([
        {
          orderId: 1,
          direction: 'long',
          status: 'open',
          marketType: 'perp',
          marketIndex: 0,
        },
      ]);

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'short');

      expect(mockDriftClient.cancelOrder).not.toHaveBeenCalled();
    });
  });

  describe('Already filled orders', () => {
    it('should not cancel filled orders', async () => {
      // Set up only filled orders
      mockUser.getOpenOrders.mockReturnValue([
        {
          orderId: 1,
          direction: 'long',
          status: 'filled',
          marketType: 'perp',
          marketIndex: 0,
        },
        {
          orderId: 2,
          direction: 'short',
          status: 'filled',
          marketType: 'perp',
          marketIndex: 0,
        },
      ]);

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(mockDriftClient.cancelOrder).not.toHaveBeenCalled();
    });

    it('should not cancel cancelled orders', async () => {
      // Set up only cancelled orders
      mockUser.getOpenOrders.mockReturnValue([
        {
          orderId: 1,
          direction: 'long',
          status: 'cancelled',
          marketType: 'perp',
          marketIndex: 0,
        },
      ]);

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(mockDriftClient.cancelOrder).not.toHaveBeenCalled();
    });

    it('should only cancel open orders among mixed statuses', async () => {
      mockUser.getOpenOrders.mockReturnValue([
        {
          orderId: 1,
          direction: 'long',
          status: 'open',
          marketType: 'perp',
          marketIndex: 0,
        },
        {
          orderId: 2,
          direction: 'long',
          status: 'filled',
          marketType: 'perp',
          marketIndex: 0,
        },
        {
          orderId: 3,
          direction: 'long',
          status: 'cancelled',
          marketType: 'perp',
          marketIndex: 0,
        },
      ]);

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'long');

      expect(mockDriftClient.cancelOrder).toHaveBeenCalledTimes(1);
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledWith(1, {}, 0);
    });
  });

  describe('Error handling', () => {
    it('should handle drift client creation failure', async () => {
      mockedCreateClient.mockResolvedValue(null);

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(mockDriftClient.subscribe).not.toHaveBeenCalled();
      expect(mockDriftClient.cancelOrder).not.toHaveBeenCalled();
    });

    it('should handle user fetch failure', async () => {
      mockDriftClient.getUser.mockReturnValue(null);

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(mockDriftClient.cancelOrder).not.toHaveBeenCalled();
      expect(mockDriftClient.unsubscribe).toHaveBeenCalled();
    });

    it('should handle order cancellation errors and continue', async () => {
      mockDriftClient.cancelOrder
        .mockRejectedValueOnce(new Error('Cancel failed'))
        .mockResolvedValueOnce(undefined);

      await expect(
        cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all')
      ).rejects.toThrow('Cancel failed');

      expect(mockDriftClient.unsubscribe).toHaveBeenCalled();
    });

    it('should handle subscription errors', async () => {
      mockDriftClient.subscribe.mockRejectedValue(new Error('Subscribe failed'));

      await expect(
        cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all')
      ).rejects.toThrow('Subscribe failed');

      expect(mockDriftClient.unsubscribe).toHaveBeenCalled();
    });

    it('should handle unsubscribe errors gracefully', async () => {
      mockDriftClient.unsubscribe.mockRejectedValue(new Error('Unsubscribe failed'));

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(mockDriftClient.unsubscribe).toHaveBeenCalled();
    });
  });

  describe('Logging behavior', () => {
    it('should log appropriate messages during cancellation', async () => {
      const { logAgentInfo, logAgentWarn } = require('../../../src/utils/logger');
      mockedCreateClient.mockResolvedValue(null);

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(logAgentWarn).toHaveBeenCalledWith(
        'test-bot-id',
        'Drift client is not initialized'
      );
    });

    it('should log when user cannot be fetched', async () => {
      const { logAgentWarn } = require('../../../src/utils/logger');
      mockDriftClient.getUser.mockReturnValue(null);

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(logAgentWarn).toHaveBeenCalledWith(
        'test-bot-id',
        'Cannot fetch user'
      );
    });

    it('should log order cancellation details', async () => {
      const { logAgentInfo } = require('../../../src/utils/logger');

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'long');

      expect(logAgentInfo).toHaveBeenCalledWith(
        'test-bot-id',
        'Canceling 1 long orders for market 0'
      );
      expect(logAgentInfo).toHaveBeenCalledWith(
        'test-bot-id',
        'Canceling order 1',
        expect.objectContaining({
          direction: 'long',
          marketIndex: 0,
        })
      );
      expect(logAgentInfo).toHaveBeenCalledWith(
        'test-bot-id',
        'Finished canceling orders for market 0'
      );
    });

    it('should log short order cancellation details', async () => {
      const { logAgentInfo } = require('../../../src/utils/logger');

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'short');

      expect(logAgentInfo).toHaveBeenCalledWith(
        'test-bot-id',
        'Canceling 1 short orders for market 0'
      );
    });

    it('should log all orders cancellation details', async () => {
      const { logAgentInfo } = require('../../../src/utils/logger');

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(logAgentInfo).toHaveBeenCalledWith(
        'test-bot-id',
        'Canceling 2 orders (1 short, 1 long) for market 0'
      );
    });

    it('should log errors during cancellation', async () => {
      const { logAgentError } = require('../../../src/utils/logger');
      const error = new Error('Cancellation failed');
      mockDriftClient.cancelOrder.mockRejectedValue(error);

      await expect(
        cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all')
      ).rejects.toThrow('Cancellation failed');

      expect(logAgentError).toHaveBeenCalledWith(
        'test-bot-id',
        'Error canceling orders:',
        error
      );
    });

    it('should log successful unsubscription', async () => {
      const { logAgentInfo } = require('../../../src/utils/logger');

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(logAgentInfo).toHaveBeenCalledWith(
        'test-bot-id',
        'Drift client unsubscribed after order cancellation'
      );
    });

    it('should log unsubscription errors', async () => {
      const { logAgentError } = require('../../../src/utils/logger');
      const unsubError = new Error('Unsubscribe failed');
      mockDriftClient.unsubscribe.mockRejectedValue(unsubError);

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(logAgentError).toHaveBeenCalledWith(
        'test-bot-id',
        'Error unsubscribing drift client:',
        unsubError
      );
    });
  });

  describe('Market type filtering', () => {
    it('should only cancel perp orders, not spot orders', async () => {
      mockUser.getOpenOrders.mockReturnValue([
        {
          orderId: 1,
          direction: 'long',
          status: 'open',
          marketType: 'perp',
          marketIndex: 0,
        },
        {
          orderId: 2,
          direction: 'long',
          status: 'open',
          marketType: 'spot',
          marketIndex: 0,
        },
      ]);

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'long');

      expect(mockDriftClient.cancelOrder).toHaveBeenCalledTimes(1);
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledWith(1, {}, 0);
    });

    it('should handle mixed market types correctly', async () => {
      mockUser.getOpenOrders.mockReturnValue([
        {
          orderId: 1,
          direction: 'long',
          status: 'open',
          marketType: 'perp',
          marketIndex: 0,
        },
        {
          orderId: 2,
          direction: 'short',
          status: 'open',
          marketType: 'spot',
          marketIndex: 0,
        },
        {
          orderId: 3,
          direction: 'short',
          status: 'open',
          marketType: 'perp',
          marketIndex: 0,
        },
      ]);

      await cancelOrders(mockAgent, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(mockDriftClient.cancelOrder).toHaveBeenCalledTimes(2);
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledWith(1, {}, 0);
      expect(mockDriftClient.cancelOrder).toHaveBeenCalledWith(3, {}, 0);
    });
  });

  describe('Agent ID handling', () => {
    it('should use botId when available', async () => {
      const { logAgentInfo } = require('../../../src/utils/logger');
      const agentWithBotId = {
        botId: 'specific-bot-id',
        chatId: 'chat-id',
        number: 0,
        pubkey: '22222222222222222222222222222222',
      };

      await cancelOrders(agentWithBotId, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(logAgentInfo).toHaveBeenCalledWith(
        'specific-bot-id',
        expect.any(String),
        expect.anything()
      );
    });

    it('should fallback to chatId when botId is not available', async () => {
      const { logAgentInfo } = require('../../../src/utils/logger');
      const agentWithoutBotId = {
        chatId: 'fallback-chat-id',
        number: 0,
        pubkey: '22222222222222222222222222222222',
      };

      await cancelOrders(agentWithoutBotId, 0, mockConnection, mockSigner, mockAuthority, 'all');

      expect(logAgentInfo).toHaveBeenCalledWith(
        'fallback-chat-id',
        expect.any(String),
        expect.anything()
      );
    });
  });
});
