// Mock BN first to avoid constructor issues
jest.mock('bn.js', () => {
  const mockBN = jest.fn().mockImplementation((value) => ({
    toNumber: () => Number(value),
    toString: () => String(value),
    add: jest.fn().mockReturnThis(),
    sub: jest.fn().mockReturnThis(),
    mul: jest.fn().mockReturnThis(),
    div: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnValue(true),
    gt: jest.fn().mockReturnValue(false),
    lt: jest.fn().mockReturnValue(false),
    gte: jest.fn().mockReturnValue(true),
    lte: jest.fn().mockReturnValue(true),
  }));

  return {
    BN: mockBN,
    default: mockBN,
  };
});

// Mock Solana Web3.js completely
jest.mock('@solana/web3.js', () => ({
  PublicKey: jest.fn().mockImplementation((key) => ({
    toBase58: () => typeof key === 'string' ? key : '********************************',
    toString: () => typeof key === 'string' ? key : '********************************',
    equals: jest.fn().mockReturnValue(true),
  })),
  Keypair: {
    generate: jest.fn().mockReturnValue({
      publicKey: {
        toBase58: () => '********************************',
        toString: () => '********************************',
      },
      secretKey: new Uint8Array(64),
    }),
  },
  Connection: jest.fn(),
  Transaction: jest.fn(),
  SystemProgram: {
    transfer: jest.fn(),
  },
  LAMPORTS_PER_SOL: 1000000000,
}));

// Mock the external dependencies
jest.mock('@drift-labs/sdk', () => ({
  DriftClient: jest.fn(),
  Wallet: jest.fn(),
  DRIFT_PROGRAM_ID: 'DRiFtupJYLTosbwoN8koMbEYSx54aFAVLddWsbksjwg7',
  OrderType: {
    MARKET: 'market',
    LIMIT: 'limit',
  },
  PositionDirection: {
    LONG: 'long',
    SHORT: 'short',
  },
  AMM_RESERVE_PRECISION_EXP: 6,
}));

// Mock Light Protocol
jest.mock('@lightprotocol/stateless.js', () => ({
  Rpc: jest.fn(),
  sendAndConfirmTx: jest.fn(),
}));

jest.mock('../../../src/utils/logger', () => ({
  logInfo: jest.fn(),
  logError: jest.fn(),
}));

import { Connection, PublicKey, Keypair } from '@solana/web3.js';
import { createClient } from '../../../src/utils/drift/getClient';

const { DriftClient, Wallet } = require('@drift-labs/sdk');
const MockedDriftClient = DriftClient as jest.MockedClass<typeof DriftClient>;
const MockedWallet = Wallet as jest.MockedClass<typeof Wallet>;

describe('getClient', () => {
  let mockConnection: Connection;
  let mockKeypair: Keypair;
  let mockAuthority: PublicKey;
  let mockWallet: any;
  let mockDriftClient: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock objects
    mockConnection = {} as Connection;
    mockKeypair = (Keypair as any).generate();
    mockAuthority = new (PublicKey as any)('********************************');

    // Mock wallet
    mockWallet = {
      publicKey: mockKeypair.publicKey,
    };
    MockedWallet.mockImplementation(() => mockWallet);

    // Mock drift client
    mockDriftClient = {};
    MockedDriftClient.mockImplementation(() => mockDriftClient);
  });

  describe('Successful Drift client creation', () => {
    it('should create a drift client successfully with valid parameters', async () => {
      const result = await createClient(mockConnection, mockKeypair, mockAuthority);

      expect(MockedWallet).toHaveBeenCalledWith(mockKeypair);
      expect(MockedDriftClient).toHaveBeenCalledWith({
        connection: mockConnection,
        wallet: mockWallet,
        env: 'mainnet-beta',
        programID: expect.anything(),
        opts: {
          commitment: 'confirmed',
        },
        authority: mockAuthority,
        includeDelegates: true,
      });
      expect(result).toBe(mockDriftClient);
    });

    it('should log successful client creation', async () => {
      const { logInfo } = require('../../../src/utils/logger');
      
      await createClient(mockConnection, mockKeypair, mockAuthority);

      expect(logInfo).toHaveBeenCalledWith(
        'Creating drift client with wallet',
        mockKeypair.publicKey.toBase58()
      );
      expect(logInfo).toHaveBeenCalledWith('Drift client created successfully');
    });
  });

  describe('Invalid RPC endpoint handling', () => {
    it('should handle null connection gracefully', async () => {
      MockedDriftClient.mockImplementation(() => {
        throw new Error('Invalid connection');
      });

      const result = await createClient(null as any, mockKeypair, mockAuthority);

      expect(result).toBeNull();
    });

    it('should handle undefined connection gracefully', async () => {
      MockedDriftClient.mockImplementation(() => {
        throw new Error('Connection is undefined');
      });

      const result = await createClient(undefined as any, mockKeypair, mockAuthority);

      expect(result).toBeNull();
    });
  });

  describe('Client configuration validation', () => {
    it('should handle invalid program ID', async () => {
      MockedDriftClient.mockImplementation(() => {
        throw new Error('Invalid program ID');
      });

      const result = await createClient(mockConnection, mockKeypair, mockAuthority);

      expect(result).toBeNull();
    });

    it('should handle invalid environment configuration', async () => {
      MockedDriftClient.mockImplementation(() => {
        throw new Error('Invalid environment');
      });

      const result = await createClient(mockConnection, mockKeypair, mockAuthority);

      expect(result).toBeNull();
    });
  });

  describe('Valid keypair authentication', () => {
    it('should accept valid keypair', async () => {
      const validKeypair = (Keypair as any).generate();

      const result = await createClient(mockConnection, validKeypair, mockAuthority);

      expect(MockedWallet).toHaveBeenCalledWith(validKeypair);
      expect(result).toBe(mockDriftClient);
    });

    it('should handle keypair with valid public key', async () => {
      const keypairWithValidPubkey = {
        publicKey: new (PublicKey as any)('********************************'),
        secretKey: new Uint8Array(64),
      };

      const result = await createClient(mockConnection, keypairWithValidPubkey, mockAuthority);

      expect(result).toBe(mockDriftClient);
    });
  });

  describe('Invalid keypair handling', () => {
    it('should handle null keypair', async () => {
      MockedWallet.mockImplementation(() => {
        throw new Error('Invalid keypair');
      });

      const result = await createClient(mockConnection, null as any, mockAuthority);

      expect(result).toBeNull();
    });

    it('should handle undefined keypair', async () => {
      MockedWallet.mockImplementation(() => {
        throw new Error('Keypair is undefined');
      });

      const result = await createClient(mockConnection, undefined as any, mockAuthority);

      expect(result).toBeNull();
    });

    it('should handle keypair without public key', async () => {
      MockedWallet.mockImplementation(() => {
        throw new Error('Invalid keypair structure');
      });

      const invalidKeypair = { secretKey: new Uint8Array(64) };

      const result = await createClient(mockConnection, invalidKeypair as any, mockAuthority);

      expect(result).toBeNull();
    });

    it('should handle keypair with invalid secret key', async () => {
      MockedWallet.mockImplementation(() => {
        throw new Error('Invalid secret key');
      });

      const invalidKeypair = {
        publicKey: new (PublicKey as any)('********************************'),
        secretKey: new Uint8Array(32), // Invalid length
      };

      const result = await createClient(mockConnection, invalidKeypair as any, mockAuthority);

      expect(result).toBeNull();
    });
  });

  describe('Error handling and logging', () => {
    it('should log errors when client creation fails', async () => {
      const { logError } = require('../../../src/utils/logger');
      const errorMessage = 'Client creation failed';
      
      MockedDriftClient.mockImplementation(() => {
        throw new Error(errorMessage);
      });

      const result = await createClient(mockConnection, mockKeypair, mockAuthority);

      expect(logError).toHaveBeenCalledWith('Error creating drift client:', errorMessage);
      expect(result).toBeNull();
    });

    it('should handle errors without message property', async () => {
      const { logError } = require('../../../src/utils/logger');
      
      MockedDriftClient.mockImplementation(() => {
        throw 'String error';
      });

      const result = await createClient(mockConnection, mockKeypair, mockAuthority);

      expect(logError).toHaveBeenCalledWith('Error creating drift client:', undefined);
      expect(result).toBeNull();
    });

    it('should return null instead of undefined on error', async () => {
      MockedDriftClient.mockImplementation(() => {
        throw new Error('Test error');
      });

      const result = await createClient(mockConnection, mockKeypair, mockAuthority);

      expect(result).toBeNull();
      expect(result).not.toBeUndefined();
    });
  });

  describe('Authority parameter validation', () => {
    it('should handle valid authority public key', async () => {
      const validAuthority = new (PublicKey as any)('22222222222222222222222222222222');

      const result = await createClient(mockConnection, mockKeypair, validAuthority);

      expect(MockedDriftClient).toHaveBeenCalledWith(
        expect.objectContaining({
          authority: validAuthority,
        })
      );
      expect(result).toBe(mockDriftClient);
    });

    it('should handle invalid authority parameter', async () => {
      MockedDriftClient.mockImplementation(() => {
        throw new Error('Invalid authority');
      });

      const result = await createClient(mockConnection, mockKeypair, 'invalid' as any);

      expect(result).toBeNull();
    });
  });
});
