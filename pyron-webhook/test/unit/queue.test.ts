// test/unit/queue.test.ts
import { alertQueue } from '../../src/queue/queue';

describe('Queue Unit Tests', () => {
  beforeEach(() => {
    // Clear any existing jobs before each test
    if (typeof (alertQueue as any).clearJobs === 'function') {
      (alertQueue as any).clearJobs();
    }
  });

  test('should be a mocked queue instance for unit tests', () => {
    // Verify that we're using the mock queue
    expect(alertQueue.name).toBe('alertQueue-unit-test');
  });

  test('should add jobs to mock queue without Redis connection', async () => {
    const jobData = {
      agentId: 'test-agent',
      alertData: {
        action: 'buy',
        signalName: 'buy',
        ticker: 'BTC'
      }
    };

    const job = await alertQueue.add('webhook-alert', jobData);

    expect(job).toBeDefined();
    expect(job.id).toBeDefined();
    expect(job.data).toEqual(jobData);
  });

  test('should track waiting jobs count', async () => {
    const initialCount = await alertQueue.getWaitingCount();
    expect(initialCount).toBe(0);

    await alertQueue.add('test-job', { test: 'data' });
    
    const newCount = await alertQueue.getWaitingCount();
    expect(newCount).toBe(1);
  });

  test('should return zero for active jobs (unit tests do not process)', async () => {
    await alertQueue.add('test-job', { test: 'data' });
    
    const activeCount = await alertQueue.getActiveCount();
    expect(activeCount).toBe(0);
  });

  test('should allow queue cleanup operations', async () => {
    await alertQueue.add('test-job-1', { test: 'data1' });
    await alertQueue.add('test-job-2', { test: 'data2' });
    
    let waitingCount = await alertQueue.getWaitingCount();
    expect(waitingCount).toBe(2);

    await alertQueue.obliterate();
    
    waitingCount = await alertQueue.getWaitingCount();
    expect(waitingCount).toBe(0);
  });

  test('should support pause and resume operations', async () => {
    // These should not throw errors
    await expect(alertQueue.pause()).resolves.toBe(true);
    await expect(alertQueue.resume()).resolves.toBe(true);
  });

  test('should provide job retrieval methods', async () => {
    await alertQueue.add('test-job', { test: 'data' });

    const waitingJobs = await alertQueue.getWaiting();
    expect(waitingJobs).toHaveLength(1);
    expect(waitingJobs[0].data).toEqual({ test: 'data' });

    const activeJobs = await alertQueue.getActive();
    expect(activeJobs).toHaveLength(0);

    const delayedJobs = await alertQueue.getDelayed();
    expect(delayedJobs).toHaveLength(0);
  });

  test('should handle job removal', async () => {
    const job = await alertQueue.add('test-job', { test: 'data' });
    
    expect(job.remove).toBeDefined();
    expect(typeof job.remove).toBe('function');
    
    await expect(job.remove()).resolves.toBe(true);
  });
});
