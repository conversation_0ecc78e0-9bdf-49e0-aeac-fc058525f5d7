import { Request, Response, NextFunction } from 'express';
import { ipFilterMiddleware } from '../../src/middleware/ipFilterMiddleware';

// Mock the logger
jest.mock('../../src/utils/logger', () => ({
  logInfo: jest.fn(),
  logWarn: jest.fn(),
  logError: jest.fn(),
}));

describe('IP Filter Middleware', () => {
  let req: any;
  let res: Partial<Response>;
  let next: NextFunction;
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    // Save original environment
    originalEnv = process.env;

    // Reset mocks
    jest.clearAllMocks();

    // Setup request, response, and next function
    req = {
      ip: '*************',
      headers: {},
      connection: { remoteAddress: '*************' } as any,
      socket: { remoteAddress: '*************' } as any
    };

    res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
      json: jest.fn()
    };

    next = jest.fn();
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  describe('when ALLOWED_IPS environment variable is set', () => {
    test('should allow requests from allowed IP addresses', async () => {
      process.env.ALLOWED_IPS = '*************,********,**********';
      req.ip = '*************';

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should allow requests from allowed IP addresses (second IP in list)', async () => {
      process.env.ALLOWED_IPS = '*************,********,**********';
      req.ip = '********';

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should reject requests from disallowed IP addresses', async () => {
      process.env.ALLOWED_IPS = '*************,********';
      req.ip = '*************';

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Access denied: IP address not allowed',
        ip: '*************'
      });
      expect(next).not.toHaveBeenCalled();
    });

    test('should handle IP addresses with whitespace in environment variable', async () => {
      process.env.ALLOWED_IPS = ' ************* , ******** , ********** ';
      req.ip = '********';

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });
  });

  describe('when ALLOWED_IPS environment variable is not set', () => {
    test('should allow all requests when ALLOWED_IPS is not set', async () => {
      delete process.env.ALLOWED_IPS;
      req.ip = '*************';

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should allow all requests when ALLOWED_IPS is empty string', async () => {
      process.env.ALLOWED_IPS = '';
      req.ip = '*************';

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });
  });

  describe('IP address extraction', () => {
    test('should extract IP from x-forwarded-for header', async () => {
      process.env.ALLOWED_IPS = '***********';
      req.ip = '*************';
      req.headers = { 'x-forwarded-for': '***********, *************' };

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should extract IP from x-real-ip header', async () => {
      process.env.ALLOWED_IPS = '***********';
      req.ip = '*************';
      req.headers = { 'x-real-ip': '***********' };

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should fall back to req.ip when headers are not present', async () => {
      process.env.ALLOWED_IPS = '*************';
      req.ip = '*************';
      req.headers = {};

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should fall back to connection.remoteAddress when req.ip is not available', async () => {
      process.env.ALLOWED_IPS = '*************';
      req.ip = undefined;
      req.connection = { remoteAddress: '*************' } as any;

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    test('should handle missing IP address gracefully', async () => {
      process.env.ALLOWED_IPS = '*************';
      req.ip = undefined;
      req.headers = {};
      req.connection = {} as any;
      req.socket = {} as any;

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Access denied: Unable to determine IP address'
      });
      expect(next).not.toHaveBeenCalled();
    });

    test('should handle malformed ALLOWED_IPS gracefully', async () => {
      process.env.ALLOWED_IPS = 'invalid-ip,*************,another-invalid';
      req.ip = '*************';

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });
  });

  describe('IPv6 support', () => {
    test('should allow IPv6 addresses', async () => {
      process.env.ALLOWED_IPS = '::1,2001:db8::1';
      req.ip = '::1';

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    test('should reject disallowed IPv6 addresses', async () => {
      process.env.ALLOWED_IPS = '::1,2001:db8::1';
      req.ip = '2001:db8::2';

      await ipFilterMiddleware(req as Request, res as Response, next);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Access denied: IP address not allowed',
        ip: '2001:db8::2'
      });
      expect(next).not.toHaveBeenCalled();
    });
  });
});
