import { Connection, PublicKey } from '@solana/web3.js';
import { processAlert } from '../../src/utils/processAlert';
import { placeOrder } from '../../src/utils/drift/placeOrder';
import { cancelOrders } from '../../src/utils/drift/cancelOrders';
import { getMarketId } from '../../src/utils/getMarketId';
import Log from '../../src/databaseModels/log';

// Mock all external dependencies
jest.mock('../../src/utils/drift/placeOrder');
jest.mock('../../src/utils/drift/cancelOrders');
jest.mock('../../src/utils/drift/getClient');
jest.mock('../../src/utils/getMarketId');
jest.mock('../../src/databaseModels/log');

// Mock the logger
jest.mock('../../src/utils/logger', () => ({
  logAgentInfo: jest.fn(),
  logAgentWarn: jest.fn(),
  logAgentError: jest.fn(),
}));

describe('ProcessAlert Utility', () => {
  let mockAgent: any;
  let mockWallet: any;
  let mockConnection: Connection;
  let mockAlertData: any;
  const originalNodeEnv = process.env.NODE_ENV;

  beforeEach(() => {
    jest.clearAllMocks();
    // Override NODE_ENV for unit tests to allow mocked functions to be called
    process.env.NODE_ENV = 'unit-test';

    // Mock agent with all required properties
    mockAgent = {
      botId: 'test-agent-id',
      pubkey: '********************************',
      tradingStatus: 'on',
      assetPair: 'BTC',
      number: 1,
      chatId: 'test-chat-id',
      signals: {
        buy: 0,
        sell: 0,
        buyOpenBar: 0,
        sellOpenBar: 0
      },
      requiredBuyConfirmationsOpen: 3,
      requiredSellConfirmationsOpen: 3,
      requiredBuyConfirmationsClose: 2,
      requiredSellConfirmationsClose: 2,
      requiredBuyConfirmationsResetCounter: 5,
      requiredSellConfirmationsResetCounter: 5,
      requiredBuyConfirmationsOverride: 4,
      requiredSellConfirmationsOverride: 4,
      requiredBuyConfirmationsOpenBar: 2,
      requiredSellConfirmationsOpenBar: 2,
      save: jest.fn().mockResolvedValue(true)
    };

    mockWallet = {
      publicKey: new PublicKey('********************************')
    };

    mockConnection = {} as Connection;

    mockAlertData = {
      action: 'confirmationMinute',
      signalName: 'buy',
      ticker: 'BTC'
    };

    // Mock external functions
    (getMarketId as jest.Mock).mockReturnValue(0);
    (placeOrder as jest.Mock).mockResolvedValue(true);
    (cancelOrders as jest.Mock).mockResolvedValue(true);
    (Log.findOne as jest.Mock).mockReturnValue({
      sort: jest.fn().mockReturnValue({
        lean: jest.fn().mockResolvedValue(null)
      })
    });
    (Log.prototype.save as jest.Mock) = jest.fn().mockResolvedValue(true);
  });

  afterEach(() => {
    // Restore original NODE_ENV
    process.env.NODE_ENV = originalNodeEnv;
  });

  // NORMAL CASES
  describe('Normal Cases', () => {
    test('should process confirmationMinute buy signal correctly', async () => {
      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(mockAgent.signals.buy).toBe(1);
      expect(mockAgent.save).toHaveBeenCalled();
    });

    test('should process confirmationMinute sell signal correctly', async () => {
      mockAlertData.signalName = 'sell';

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(mockAgent.signals.sell).toBe(1);
      expect(mockAgent.save).toHaveBeenCalled();
    });

    test('should process buy action when confirmations are sufficient', async () => {
      mockAlertData.action = 'buy';
      mockAgent.signals.buy = 3; // Equal to requiredBuyConfirmationsOpen

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(cancelOrders).toHaveBeenCalledWith(mockAgent, 0, mockConnection, mockWallet, expect.any(PublicKey), 'short');
      expect(placeOrder).toHaveBeenCalledWith('buy', 100, 1, 'BTC', mockWallet, 'test-chat-id', 'buy', expect.any(PublicKey), 'test-agent-id');
      expect(mockAgent.signals.buy).toBe(0); // Reset after action
      expect(mockAgent.signals.sell).toBe(0);
    });

    test('should process sell action when confirmations are sufficient', async () => {
      mockAlertData.action = 'sell';
      mockAgent.signals.sell = 3; // Equal to requiredSellConfirmationsOpen

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(cancelOrders).toHaveBeenCalledWith(mockAgent, 0, mockConnection, mockWallet, expect.any(PublicKey), 'long');
      expect(placeOrder).toHaveBeenCalledWith('sell', 100, 1, 'BTC', mockWallet, 'test-chat-id', 'sell', expect.any(PublicKey), 'test-agent-id');
      expect(mockAgent.signals.sell).toBe(0); // Reset after action
      expect(mockAgent.signals.buy).toBe(0);
    });

    test('should process overrideBuy action correctly', async () => {
      mockAlertData.action = 'overrideBuy';

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(Log.prototype.save).toHaveBeenCalled();
      expect(mockAgent.save).toHaveBeenCalled();
    });

    test('should process overrideSell action correctly', async () => {
      mockAlertData.action = 'overrideSell';

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(Log.prototype.save).toHaveBeenCalled();
      expect(mockAgent.save).toHaveBeenCalled();
    });
  });

  // EDGE CASES
  describe('Edge Cases', () => {
    test('should skip processing when trading is off', async () => {
      mockAgent.tradingStatus = 'off';

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(mockAgent.save).not.toHaveBeenCalled();
      expect(placeOrder).not.toHaveBeenCalled();
    });

    test('should return early when ticker is missing', async () => {
      delete mockAlertData.ticker;

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBeUndefined();
      expect(mockAgent.save).not.toHaveBeenCalled();
    });

    test('should return early when ticker does not match agent assetPair', async () => {
      mockAlertData.ticker = 'ETH';

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBeUndefined();
      expect(mockAgent.save).not.toHaveBeenCalled();
    });

    test('should return early when market index is not found', async () => {
      (getMarketId as jest.Mock).mockReturnValue(undefined);

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBeUndefined();
      expect(mockAgent.save).not.toHaveBeenCalled();
    });

    test('should initialize signals object if undefined', async () => {
      mockAgent.signals = undefined;

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(mockAgent.signals).toBeDefined();
      expect(mockAgent.signals.buy).toBe(1);
    });

    test('should initialize specific signal if undefined', async () => {
      delete mockAgent.signals.buy;

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(mockAgent.signals.buy).toBe(1);
    });

    test('should handle null signals object', async () => {
      mockAgent.signals = null;

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(mockAgent.signals).toBeDefined();
      expect(mockAgent.signals.buy).toBe(1);
    });
  });

  // CONFIRMATION LOGIC TESTS
  describe('Confirmation Logic', () => {
    test('should close opposite position when buy confirmations reach close threshold', async () => {
      mockAgent.signals.buy = 2; // Equal to requiredBuyConfirmationsClose
      mockAlertData.signalName = 'buy';

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(placeOrder).toHaveBeenCalledWith('closeSell', 100, 1, 'BTC', mockWallet, 'test-chat-id', 'confirmationMinute', expect.any(PublicKey), 'test-agent-id');
      expect(cancelOrders).toHaveBeenCalledWith(mockAgent, 0, mockConnection, mockWallet, expect.any(PublicKey), 'short');
    });

    test('should close opposite position when sell confirmations reach close threshold', async () => {
      mockAgent.signals.sell = 2; // Equal to requiredSellConfirmationsClose
      mockAlertData.signalName = 'sell';

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(placeOrder).toHaveBeenCalledWith('closeBuy', 100, 1, 'BTC', mockWallet, 'test-chat-id', 'confirmationMinute', expect.any(PublicKey), 'test-agent-id');
      expect(cancelOrders).toHaveBeenCalledWith(mockAgent, 0, mockConnection, mockWallet, expect.any(PublicKey), 'long');
    });

    test('should reset opposite signals when buy confirmations reach reset threshold', async () => {
      mockAgent.signals.buy = 5; // Equal to requiredBuyConfirmationsResetCounter
      mockAgent.signals.sell = 3;
      mockAgent.signals.sellOpenBar = 2;
      mockAlertData.signalName = 'buy';

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(mockAgent.signals.sell).toBe(0);
      expect(mockAgent.signals.sellOpenBar).toBe(0);
      expect(mockAgent.signals.buy).toBe(6); // Incremented by 1
    });

    test('should reset opposite signals when sell confirmations reach reset threshold', async () => {
      mockAgent.signals.sell = 5; // Equal to requiredSellConfirmationsResetCounter
      mockAgent.signals.buy = 3;
      mockAgent.signals.buyOpenBar = 2;
      mockAlertData.signalName = 'sell';

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(mockAgent.signals.buy).toBe(0);
      expect(mockAgent.signals.buyOpenBar).toBe(0);
      expect(mockAgent.signals.sell).toBe(6); // Incremented by 1
    });
  });

  // OVERRIDE SIGNAL TESTS
  describe('Override Signal Tests', () => {
    test('should execute buy when override signal matches and confirmations sufficient', async () => {
      mockAgent.signals.buy = 4; // Equal to requiredBuyConfirmationsOverride
      mockAlertData.signalName = 'buy';

      // Mock override signal found
      (Log.findOne as jest.Mock).mockReturnValue({
        sort: jest.fn().mockReturnValue({
          lean: jest.fn().mockResolvedValue({
            webhookSignal: 'overrideBuy',
            timestamps: new Date().toISOString()
          })
        })
      });

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(cancelOrders).toHaveBeenCalledWith(mockAgent, 0, mockConnection, mockWallet, expect.any(PublicKey), 'short');
      expect(placeOrder).toHaveBeenCalledWith('buy', 100, 1, 'BTC', mockWallet, 'test-chat-id', 'confirmationMinute', expect.any(PublicKey), 'test-agent-id');
      expect(mockAgent.signals.buy).toBe(0); // Reset after execution
      expect(mockAgent.signals.sell).toBe(0);
    });

    test('should execute sell when override signal matches and confirmations sufficient', async () => {
      mockAgent.signals.sell = 4; // Equal to requiredSellConfirmationsOverride
      mockAlertData.signalName = 'sell';

      // Mock override signal found
      (Log.findOne as jest.Mock).mockReturnValue({
        sort: jest.fn().mockReturnValue({
          lean: jest.fn().mockResolvedValue({
            webhookSignal: 'overrideSell',
            timestamps: new Date().toISOString()
          })
        })
      });

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(cancelOrders).toHaveBeenCalledWith(mockAgent, 0, mockConnection, mockWallet, expect.any(PublicKey), 'long');
      expect(placeOrder).toHaveBeenCalledWith('sell', 100, 1, 'BTC', mockWallet, 'test-chat-id', 'confirmationMinute', expect.any(PublicKey), 'test-agent-id');
      expect(mockAgent.signals.sell).toBe(0); // Reset after execution
      expect(mockAgent.signals.buy).toBe(0);
    });

    test('should not execute when override signal does not match', async () => {
      mockAgent.signals.buy = 4; // Equal to requiredBuyConfirmationsOverride
      mockAlertData.signalName = 'buy';

      // Mock different override signal found
      (Log.findOne as jest.Mock).mockReturnValue({
        sort: jest.fn().mockReturnValue({
          lean: jest.fn().mockResolvedValue({
            webhookSignal: 'overrideSell', // Different signal
            timestamps: new Date().toISOString()
          })
        })
      });

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(placeOrder).not.toHaveBeenCalledWith('buy', expect.any(Number), expect.any(Number), expect.any(String), expect.any(Object), expect.any(String), expect.any(String), expect.any(PublicKey), expect.any(String));
      expect(mockAgent.signals.buy).toBe(5); // Just incremented, not reset
    });
  });

  // OPEN BAR CONFIRMATION TESTS
  describe('Open Bar Confirmation Tests', () => {
    test('should process confirmationOpenBar buyOpenBar signal correctly', async () => {
      mockAlertData.action = 'confirmationOpenBar';
      mockAlertData.signalName = 'buyOpenBar';

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(mockAgent.signals.buyOpenBar).toBe(1);
      expect(mockAgent.save).toHaveBeenCalled();
    });

    test('should process confirmationOpenBar sellOpenBar signal correctly', async () => {
      mockAlertData.action = 'confirmationOpenBar';
      mockAlertData.signalName = 'sellOpenBar';

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(mockAgent.signals.sellOpenBar).toBe(1);
      expect(mockAgent.save).toHaveBeenCalled();
    });

    test('should execute buy when buyOpenBar override signal matches', async () => {
      mockAlertData.action = 'confirmationOpenBar';
      mockAlertData.signalName = 'buyOpenBar';

      // Mock override signal found
      (Log.findOne as jest.Mock).mockReturnValue({
        sort: jest.fn().mockReturnValue({
          lean: jest.fn().mockResolvedValue({
            webhookSignal: 'overrideBuy',
            timestamps: new Date().toISOString()
          })
        })
      });

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(cancelOrders).toHaveBeenCalledWith(mockAgent, 0, mockConnection, mockWallet, expect.any(PublicKey), 'short');
      expect(placeOrder).toHaveBeenCalledWith('buy', 100, 1, 'BTC', mockWallet, 'test-chat-id', 'confirmationOpenBar', expect.any(PublicKey), 'test-agent-id');
      expect(mockAgent.signals.buyOpenBar).toBe(0); // Reset after execution
    });

    test('should execute sell when sellOpenBar override signal matches', async () => {
      mockAlertData.action = 'confirmationOpenBar';
      mockAlertData.signalName = 'sellOpenBar';

      // Mock override signal found
      (Log.findOne as jest.Mock).mockReturnValue({
        sort: jest.fn().mockReturnValue({
          lean: jest.fn().mockResolvedValue({
            webhookSignal: 'overrideSell',
            timestamps: new Date().toISOString()
          })
        })
      });

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(cancelOrders).toHaveBeenCalledWith(mockAgent, 0, mockConnection, mockWallet, expect.any(PublicKey), 'long');
      expect(placeOrder).toHaveBeenCalledWith('sell', 100, 1, 'BTC', mockWallet, 'test-chat-id', 'confirmationOpenBar', expect.any(PublicKey), 'test-agent-id');
      expect(mockAgent.signals.sellOpenBar).toBe(0); // Reset after execution
    });
  });

  // CLOSE BAR CONFIRMATION TESTS
  describe('Close Bar Confirmation Tests', () => {
    test('should process confirmationCloseBar buy signal correctly', async () => {
      mockAlertData.action = 'confirmationCloseBar';
      mockAlertData.signalName = 'buy';

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(placeOrder).toHaveBeenCalledWith('closeSell', 100, 1, 'BTC', mockWallet, 'test-chat-id', 'confirmationCloseBar', expect.any(PublicKey), 'test-agent-id');
      expect(cancelOrders).toHaveBeenCalledWith(mockAgent, 0, mockConnection, mockWallet, expect.any(PublicKey), 'short');
      expect(mockAgent.save).toHaveBeenCalled();
    });

    test('should process confirmationCloseBar sell signal correctly', async () => {
      mockAlertData.action = 'confirmationCloseBar';
      mockAlertData.signalName = 'sell';

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(placeOrder).toHaveBeenCalledWith('closeBuy', 100, 1, 'BTC', mockWallet, 'test-chat-id', 'confirmationCloseBar', expect.any(PublicKey), 'test-agent-id');
      expect(cancelOrders).toHaveBeenCalledWith(mockAgent, 0, mockConnection, mockWallet, expect.any(PublicKey), 'long');
      expect(mockAgent.save).toHaveBeenCalled();
    });
  });

  // BUY OPEN BAR CONFIRMATION TESTS
  describe('Buy Open Bar Confirmation Tests', () => {
    test('should execute buy when buyOpenBar confirmations are sufficient', async () => {
      mockAlertData.action = 'buy';
      mockAgent.signals.buy = 1; // Less than requiredBuyConfirmationsOpen (3)
      mockAgent.signals.buyOpenBar = 2; // Equal to requiredBuyConfirmationsOpenBar

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(placeOrder).toHaveBeenCalledWith('buy', 100, 1, 'BTC', mockWallet, 'test-chat-id', 'buy', expect.any(PublicKey), 'test-agent-id');
      expect(mockAgent.signals.buy).toBe(0); // Reset after action
      expect(mockAgent.signals.buyOpenBar).toBe(0); // Reset after action
    });

    test('should not execute buy when neither confirmation type is sufficient', async () => {
      mockAlertData.action = 'buy';
      mockAgent.signals.buy = 1; // Less than requiredBuyConfirmationsOpen (3)
      mockAgent.signals.buyOpenBar = 1; // Less than requiredBuyConfirmationsOpenBar (2)

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(placeOrder).not.toHaveBeenCalled();
      expect(mockAgent.save).toHaveBeenCalled(); // Still saves agent state
    });
  });

  // SELL OPEN BAR CONFIRMATION TESTS
  describe('Sell Open Bar Confirmation Tests', () => {
    test('should execute sell when sellOpenBar confirmations are sufficient', async () => {
      mockAlertData.action = 'sell';
      mockAgent.signals.sell = 1; // Less than requiredSellConfirmationsOpen (3)
      mockAgent.signals.sellOpenBar = 2; // Equal to requiredSellConfirmationsOpenBar

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(placeOrder).toHaveBeenCalledWith('sell', 100, 1, 'BTC', mockWallet, 'test-chat-id', 'sell', expect.any(PublicKey), 'test-agent-id');
      expect(mockAgent.signals.sell).toBe(0); // Reset after action
      expect(mockAgent.signals.sellOpenBar).toBe(0); // Reset after action
    });

    test('should not execute sell when neither confirmation type is sufficient', async () => {
      mockAlertData.action = 'sell';
      mockAgent.signals.sell = 1; // Less than requiredSellConfirmationsOpen (3)
      mockAgent.signals.sellOpenBar = 1; // Less than requiredSellConfirmationsOpenBar (2)

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(placeOrder).not.toHaveBeenCalled();
      expect(mockAgent.save).toHaveBeenCalled(); // Still saves agent state
    });
  });

  // ERROR HANDLING TESTS
  describe('Error Handling', () => {
    test('should handle placeOrder failure gracefully', async () => {
      (placeOrder as jest.Mock).mockResolvedValue(false);
      mockAlertData.action = 'buy';
      mockAgent.signals.buy = 3;

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(mockAgent.save).toHaveBeenCalled();
    });

    test('should handle cancelOrders failure gracefully', async () => {
      (cancelOrders as jest.Mock).mockRejectedValue(new Error('Cancel orders failed'));
      mockAlertData.action = 'buy';
      mockAgent.signals.buy = 3;

      await expect(processAlert(mockAlertData, mockWallet, mockAgent, mockConnection)).rejects.toThrow('Cancel orders failed');
    });

    test('should handle database save failure gracefully', async () => {
      mockAgent.save.mockRejectedValue(new Error('Database save failed'));

      await expect(processAlert(mockAlertData, mockWallet, mockAgent, mockConnection)).rejects.toThrow('Database save failed');
    });

    test('should handle Log.findOne failure gracefully', async () => {
      (Log.findOne as jest.Mock).mockImplementation(() => {
        throw new Error('Database query failed');
      });

      await expect(processAlert(mockAlertData, mockWallet, mockAgent, mockConnection)).rejects.toThrow('Database query failed');
    });

    test('should handle invalid PublicKey gracefully', async () => {
      mockAgent.pubkey = 'invalid-public-key';

      await expect(processAlert(mockAlertData, mockWallet, mockAgent, mockConnection)).rejects.toThrow();
    });
  });

  // TEST ENVIRONMENT HANDLING
  describe('Test Environment Handling', () => {
    const originalNodeEnv = process.env.NODE_ENV;

    beforeEach(() => {
      process.env.NODE_ENV = 'test';
    });

    afterEach(() => {
      process.env.NODE_ENV = originalNodeEnv;
    });

    test('should simulate operations in test environment', async () => {
      mockAlertData.action = 'buy';
      mockAgent.signals.buy = 3;

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(placeOrder).not.toHaveBeenCalled(); // Should not actually place orders in test
      expect(cancelOrders).not.toHaveBeenCalled(); // Should not actually cancel orders in test
    });

    test('should simulate close operations in test environment', async () => {
      mockAlertData.action = 'confirmationCloseBar';
      mockAlertData.signalName = 'buy';

      const result = await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(result).toBe('ok');
      expect(placeOrder).not.toHaveBeenCalled(); // Should not actually place orders in test
      expect(cancelOrders).not.toHaveBeenCalled(); // Should not actually cancel orders in test
    });
  });

  // LOGGING VERIFICATION
  describe('Logging Verification', () => {
    const { logAgentInfo, logAgentWarn, logAgentError } = require('../../src/utils/logger');

    test('should log agent info for successful processing', async () => {
      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(logAgentInfo).toHaveBeenCalledWith('test-agent-id', 'Authority', '********************************');
      expect(logAgentInfo).toHaveBeenCalledWith('test-agent-id', 'Agent signals before processing:', expect.any(Object));
    });

    test('should log warning when trading is off', async () => {
      mockAgent.tradingStatus = 'off';

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(logAgentInfo).toHaveBeenCalledWith('test-agent-id', 'User has trades turned off. Skipping execution.');
    });

    test('should log warning when ticker is missing', async () => {
      delete mockAlertData.ticker;

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(logAgentWarn).toHaveBeenCalledWith('test-agent-id', 'No ticker in alert data. Exiting.');
    });

    test('should log warning when ticker mismatch occurs', async () => {
      mockAlertData.ticker = 'ETH';

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(logAgentWarn).toHaveBeenCalledWith('test-agent-id', 'Ticker mismatch. Agent expects BTC, got ETH');
    });

    test('should log warning when market index not found', async () => {
      (getMarketId as jest.Mock).mockReturnValue(undefined);

      await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);

      expect(logAgentWarn).toHaveBeenCalledWith('test-agent-id', 'marketIndex not found for ticker:', 'BTC');
    });

    test('should log error when exception occurs', async () => {
      mockAgent.save.mockRejectedValue(new Error('Test error'));

      try {
        await processAlert(mockAlertData, mockWallet, mockAgent, mockConnection);
      } catch (error) {
        // Expected to throw
      }

      expect(logAgentError).toHaveBeenCalledWith('test-agent-id', 'Error in processAlert:', expect.any(Error));
    });
  });
});
