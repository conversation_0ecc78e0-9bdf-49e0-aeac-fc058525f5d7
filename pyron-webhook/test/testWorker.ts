// test/testWorker.ts
import { createWorker } from "../src/queue/worker";

console.log("=== Using unified worker implementation for tests ===");

// Create a test worker with higher concurrency for testing
export function createTestWorker(concurrency = 3, redisUrl?: string) {
  // Use the unified worker implementation with test-specific settings
  return createWorker({
    concurrency,
    redisUrl,
    isTest: true
  });
}
