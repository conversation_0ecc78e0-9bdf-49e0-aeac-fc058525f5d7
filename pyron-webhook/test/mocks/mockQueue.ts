// test/mocks/mockQueue.ts
// Mock implementation of BullMQ Queue for unit tests

export class MockQueue {
  private jobs: any[] = [];
  private jobIdCounter = 1;
  public name: string;

  constructor(name: string, options?: any) {
    this.name = name;
  }

  // Mock job creation
  async add(jobName: string, data: any, options?: any) {
    const job = {
      id: this.jobIdCounter++,
      name: jobName,
      data,
      options,
      timestamp: Date.now(),
      remove: jest.fn().mockResolvedValue(true),
      attemptsMade: 0
    };
    
    this.jobs.push(job);
    return job;
  }

  // Mock queue state methods
  async getActiveCount() {
    return 0; // Unit tests don't process jobs
  }

  async getWaitingCount() {
    return this.jobs.length;
  }

  async getDelayedCount() {
    return 0;
  }

  async getCompletedCount() {
    return 0;
  }

  async getFailedCount() {
    return 0;
  }

  // Mock job retrieval methods
  async getWaiting() {
    return this.jobs;
  }

  async getActive() {
    return [];
  }

  async getDelayed() {
    return [];
  }

  async getCompleted() {
    return [];
  }

  async getFailed() {
    return [];
  }

  // Mock queue control methods
  async pause() {
    return true;
  }

  async resume() {
    return true;
  }

  async obliterate(options?: any) {
    this.jobs = [];
    return true;
  }

  // Mock cleanup method
  async close() {
    this.jobs = [];
    return true;
  }

  // Helper method for tests to clear jobs
  clearJobs() {
    this.jobs = [];
  }

  // Helper method for tests to get all jobs
  getAllJobs() {
    return [...this.jobs];
  }
}

// Create a mock alertQueue instance for unit tests
export const mockAlertQueue = new MockQueue('alertQueue-unit-test');
