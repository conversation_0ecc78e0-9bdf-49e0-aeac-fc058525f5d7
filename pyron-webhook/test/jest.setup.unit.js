// Jest setup file specifically for unit tests
const dotenv = require('dotenv');
const path = require('path');

// Load test environment variables from .env.test
dotenv.config({ path: path.resolve(__dirname, '../.env.test') });

// Ensure NODE_ENV is set to test
process.env.NODE_ENV = 'test';

// Mock the queue module for unit tests to avoid Redis dependency
jest.mock('../src/queue/queue', () => {
  const { mockAlertQueue } = require('./mocks/mockQueue');
  return {
    alertQueue: mockAlertQueue
  };
});

// Mock the worker module for unit tests
jest.mock('../src/queue/worker', () => ({
  createWorker: jest.fn().mockReturnValue({
    worker: {
      close: jest.fn().mockResolvedValue(true),
      on: jest.fn()
    },
    connection: {
      disconnect: jest.fn().mockResolvedValue(true),
      quit: jest.fn().mockResolvedValue(true)
    }
  })
}));

console.log('🧪 Unit test environment loaded with mocked queue');
console.log(`📊 MongoDB: ${process.env.MONGO_HOST}:${process.env.MONGO_PORT}/${process.env.MONGO_DB}`);
console.log(`🔴 Redis: MOCKED (no real Redis connection for unit tests)`);
