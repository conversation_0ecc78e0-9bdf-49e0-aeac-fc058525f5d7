import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import path from 'path';
import { nodePolyfills } from 'vite-plugin-node-polyfills';
import inject from '@rollup/plugin-inject';
import { componentTagger } from 'lovable-tagger';

export default defineConfig(({ mode }) => ({
  server: {
    host: '::',
    port: 8080,
    watch: {
      ignored: ['**/node_modules/**', '**/venv/**'],
    },
    allowedHosts: ['www.pyron.net','pyron.net'], // 👈 Add this line
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      process: 'process/browser',
      buffer: 'buffer',
      stream: 'stream-browserify',
    },
  },
  optimizeDeps: {
    include: ['process', 'buffer', 'stream', 'readable-stream', 'browserify-sign'],
  },
  define: {
    'process.browser': 'true',
    'process.env.NODE_ENV': JSON.stringify(mode),
  },
  plugins: [
    react(),
    nodePolyfills({
      globals: { process: true, Buffer: true, global: true },
      protocolImports: true,
    }),
    mode === 'development' && componentTagger(),
  ].filter(Boolean),
  build: {
    rollupOptions: {
      plugins: [
        inject({
          Buffer: ['buffer', 'Buffer'],
          process: 'process/browser',
        }),
      ],
    },
  },
}));
