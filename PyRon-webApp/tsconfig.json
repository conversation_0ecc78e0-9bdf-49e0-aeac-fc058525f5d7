{"files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "noImplicitAny": false, "noUnusedParameters": false, "skipLibCheck": true, "allowJs": true, "noUnusedLocals": false, "strictNullChecks": false, "checkJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "strict": true}}