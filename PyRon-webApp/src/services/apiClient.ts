import { getToken, refreshToken, removeToken } from './authService';
import { useWalletStore } from '@/store/walletStore';

const API_BASE_URL = import.meta.env.VITE_BASE_PYRON_URL || '';

// Create authenticated fetch function
export const apiFetch = async (
  endpoint: string, 
  options: RequestInit = {}
): Promise<any> => {
  // Add authentication header if token exists
  const token = getToken();
  const headers = {
    'Content-Type': 'application/json',
    ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
    ...options.headers
  };
  
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers
    });
    
    // Handle 401 Unauthorized - token expired or invalid
    if (response.status === 401) {
      // Try to refresh the token
      const refreshed = await refreshToken();
      if (refreshed) {
        // Retry the request with new token
        return apiFetch(endpoint, options);
      } else {
        // If refresh failed, logout
        removeToken();
        useWalletStore.getState().disconnectWallet();
        throw new Error('Authentication failed');
      }
    }
    
    // Handle other error responses
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'API request failed');
    }
    
    // Parse JSON response if content exists
    if (response.status !== 204) {
      return await response.json();
    }
    
    return null;
  } catch (error) {
    console.error(`API error (${endpoint}):`, error);
    throw error;
  }
};

// API methods
export const api = {
  get: (endpoint: string, options = {}) => 
    apiFetch(endpoint, { method: 'GET', ...options }),
    
  post: (endpoint: string, data: any, options = {}) => 
    apiFetch(endpoint, { 
      method: 'POST', 
      body: JSON.stringify(data),
      ...options 
    }),
    
  put: (endpoint: string, data: any, options = {}) => 
    apiFetch(endpoint, { 
      method: 'PUT', 
      body: JSON.stringify(data),
      ...options 
    }),
    
  delete: (endpoint: string, options = {}) => 
    apiFetch(endpoint, { method: 'DELETE', ...options }),
};