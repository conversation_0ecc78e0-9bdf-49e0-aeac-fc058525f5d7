import { useWalletStore } from '@/store/walletStore';
import { api } from './apiClient';

// Token management
export const getToken = () => localStorage.getItem('jwt_token');
export const setToken = (token: string) => localStorage.setItem('jwt_token', token);
export const removeToken = () => localStorage.removeItem('jwt_token');

// Check if token exists and is not expired
export const isAuthenticated = () => {
  const token = getToken();
  if (!token) return false;

  try {
    // Simple JWT expiration check
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000 > Date.now();
  } catch (e) {
    return false;
  }
};

// Get a new JWT token after wallet connection
export const authenticateWallet = async (retryCount = 3): Promise<boolean> => {
  try {
    const walletAddress = useWalletStore.getState().walletAddress;

    // If no wallet address, wait briefly and retry if we have retries left
    if (!walletAddress) {
      if (retryCount > 0) {
        console.log(`No wallet address found, retrying... (${retryCount} attempts left)`);
        // Wait for 500ms before retrying to allow wallet connection to complete
        await new Promise(resolve => setTimeout(resolve, 500));
        return authenticateWallet(retryCount - 1);
      }
      console.error('Authentication failed: No wallet address available after retries');
      return false;
    }

    console.log(`Authenticating with wallet address: ${walletAddress}`);

    // We can't use the api client here since it depends on the token we're trying to get
    // So we'll use a direct fetch call
    const response = await fetch(`${import.meta.env.VITE_BASE_PYRON_URL}/api/auth/token`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ walletAddress })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'Authentication failed');
    }

    const { token } = await response.json();
    setToken(token);
    console.log('Authentication successful, token received');
    return true;
  } catch (error) {
    console.error('Authentication error:', error);
    // Retry on network errors if we have retries left
    if (error instanceof TypeError && retryCount > 0) {
      console.log(`Network error, retrying... (${retryCount} attempts left)`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return authenticateWallet(retryCount - 1);
    }
    return false;
  }
};

// Refresh token before it expires
export const refreshToken = async (): Promise<boolean> => {
  try {
    const token = getToken();

    // If no token exists, try to authenticate with wallet instead
    if (!token) {
      console.log('No token found, attempting to authenticate with wallet');
      return await authenticateWallet();
    }

    // We can't use the api client here since it depends on the token we're trying to refresh
    // So we'll use a direct fetch call
    const response = await fetch(`${import.meta.env.VITE_BASE_PYRON_URL}/api/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      // If refresh fails, try to get a new token via wallet authentication
      if (response.status === 401) {
        console.log('Token refresh failed with 401, attempting wallet authentication');
        return await authenticateWallet();
      }
      throw new Error('Token refresh failed');
    }

    const { token: newToken } = await response.json();
    setToken(newToken);
    return true;
  } catch (error) {
    console.error('Token refresh error:', error);
    removeToken();
    // Try to authenticate with wallet as a fallback
    return await authenticateWallet();
  }
};

// Logout
export const logout = async (): Promise<void> => {
  try {
    await api.post('/api/auth/logout', {});
  } catch (error) {
    console.error('Logout error:', error);
  } finally {
    removeToken();
  }
};