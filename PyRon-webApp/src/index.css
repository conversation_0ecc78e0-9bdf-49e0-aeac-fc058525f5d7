
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 13%;    /* 2% brighter from 11% */
    --foreground: 0 0% 100%;
    
    --card: 0 0% 16%;          /* 2% brighter from 14% */
    --card-foreground: 0 0% 100%;
    
    --popover: 0 0% 16%;       /* 2% brighter from 14% */
    --popover-foreground: 0 0% 100%;
    
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 16%;   /* 2% brighter from 14% */
    
    --secondary: 0 0% 18%;     /* 2% brighter from 16% */
    --secondary-foreground: 0 0% 100%;
    
    --muted: 0 0% 18%;         /* 2% brighter from 16% */
    --muted-foreground: 0 0% 74%;     /* 2% brighter from 72% */
    
    --accent: 0 0% 18%;        /* 2% brighter from 16% */
    --accent-foreground: 0 0% 100%;
    
    --destructive: 0 62.8% 34.6%;     /* 2% brighter from 32.6% */
    --destructive-foreground: 0 0% 100%;
    
    --border: 0 0% 20%;        /* 2% brighter from 18% */
    --input: 0 0% 20%;         /* 2% brighter from 18% */
    --ring: 0 0% 84%;          /* 2% brighter from 82% */
    
    --radius: 0.5rem;
    
    --sidebar-background: 0 0% 16%;   /* 2% brighter from 14% */
    --sidebar-foreground: 0 0% 100%;
  }
}

@layer base {
  * {
    @apply border-border selection:bg-white/10 selection:text-white;
  }
  
  html, body, #root {
    @apply h-full w-full m-0 p-0;
  }
  
  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
    background: #1e1e1e; /* Updated to 2% brighter from #1c1c1c */
    font-size: 16px; /* Base font size increased */
  }
}

@layer components {
  .gold-border {
    @apply border border-gray-700/30 bg-black/20 backdrop-blur-sm hover:border-gray-700/50 transition-colors shadow-lg shadow-black/20 whitespace-nowrap;
  }

  .neo-card {
    @apply bg-secondary/60 backdrop-blur-sm rounded-lg border border-gray-700/20 shadow-lg shadow-black/20 hover:border-gray-700/40 transition-all whitespace-nowrap;
  }

  .glass-card {
    @apply backdrop-blur-sm bg-black/40 border border-gray-700/20 rounded-lg shadow-xl transition-all hover:border-gray-700/40;
  }
  
  .no-wrap {
    @apply whitespace-nowrap;
  }
  
  .chat-input {
    @apply bg-[#262626] border border-[#373737] rounded-md shadow-lg; /* 2% brighter from #242424 and #353535 */
  }
  
  .chat-button {
    @apply border border-[#373737] bg-[#262626] hover:bg-[#373737] transition-colors text-white rounded-full px-4 py-2 flex items-center gap-2; /* 2% brighter */
  }

  .gold-glow {
    @apply text-gold-light border-gold-dark/30 shadow-[0_0_5px_rgba(173,134,9,0.4)] hover:shadow-[0_0_10px_rgba(205,164,41,0.6)]; /* Updated to match new gold colors */
  }
  
  .gold-button {
    @apply bg-gold-dark/20 backdrop-blur-sm border border-gold-light/30 hover:bg-gold-dark/30 hover:border-gold-light/50 text-white transition-all;
  }
  
  .text-gradient-gold {
    @apply bg-gradient-to-r from-gold-light via-gold-light to-gold-dark bg-clip-text text-transparent;
  }
  
  /* Sidebar styles */
  .sidebar {
    @apply fixed inset-y-0 left-0 z-50 bg-[#262626] w-[320px] transform transition-transform duration-300; /* 2% brighter from #242424 */
  }
  
  .sidebar-open {
    @apply translate-x-0;
  }
  
  .sidebar-closed {
    @apply -translate-x-full;
  }
  
  .sidebar-backdrop {
    @apply fixed inset-0 bg-black/70 backdrop-blur-sm z-40;
  }
}
