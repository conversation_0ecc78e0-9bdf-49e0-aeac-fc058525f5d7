import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getChatMessages, getUserChats } from '@/lib/chat';
import { ExtendedChatMessage, Chat } from '@/types/chat';
import { useWalletStore } from '@/store/walletStore';

interface ChatContextType {
  currentChatId: string | null;
  setCurrentChatId: (chatId: string | null) => void;
  messages: ExtendedChatMessage[];
  setMessages: (messages: ExtendedChatMessage[]) => void;
  loading: boolean;
  agent: any;
  setAgent: (agent: any) => void;
  chats: Chat[];
  setChats: React.Dispatch<React.SetStateAction<Chat[]>>;
}

const ChatContext = createContext<ChatContextType>({
  currentChatId: null,
  setCurrentChatId: () => {},
  messages: [],
  setMessages: () => {},
  loading: false,
  agent: null,
  setAgent: () => {},
  chats: [],
  setChats: () => {},
});

export const useChatContext = () => useContext(ChatContext);

interface ChatProviderProps {
  children: ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const [messages, setMessages] = useState<ExtendedChatMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [agent, setAgent] = useState<any>(null);
  const [chats, setChats] = useState<Chat[]>([]);
  const { isConnected, walletAddress } = useWalletStore();

  useEffect(() => {
    if (isConnected && walletAddress) {
      const fetchChats = async () => {
        try {
          const userChats = await getUserChats(walletAddress);
          setChats(userChats);
        } catch (error) {
          console.error('Error fetching chats:', error);
        }
      };
      fetchChats();
    } else {
      // Reset chat-related states when wallet disconnects
      setCurrentChatId(null);
      setAgent(null);
      setChats([]);
    }
  }, [isConnected, walletAddress]);

  useEffect(() => {
    const fetchMessages = async () => {
      if (currentChatId) {
        setLoading(true);
        try {
          const fetchedMessages = await getChatMessages(currentChatId);
          const extendedMessages = fetchedMessages.map(message => ({
            ...message,
            status: 'sent' as 'sent',
            sender: message.type
          }));
          setMessages(extendedMessages);
        } catch (error) {
          console.error('Error fetching messages:', error);
        } finally {
          setLoading(false);
        }
      }
    };
    fetchMessages();
  }, [currentChatId]);

  return (
    <ChatContext.Provider value={{ currentChatId, setCurrentChatId, messages, setMessages, loading, agent, setAgent, chats, setChats }}>
      {children}
    </ChatContext.Provider>
  );
};
