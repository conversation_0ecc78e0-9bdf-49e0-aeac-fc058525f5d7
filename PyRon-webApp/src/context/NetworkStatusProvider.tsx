
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface NetworkStatusContextType {
  isOnline: boolean;
  retryConnection: () => void;
}

const NetworkStatusContext = createContext<NetworkStatusContextType>({
  isOnline: true,
  retryConnection: () => {}
});

export const useNetworkStatus = () => useContext(NetworkStatusContext);

interface NetworkStatusProviderProps {
  children: ReactNode;
}

export const NetworkStatusProvider: React.FC<NetworkStatusProviderProps> = ({ children }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Check if we can actually reach our backend
  const checkBackendConnection = async () => {
    try {
      // Simple HEAD request to check if backend is reachable
      // We use a direct fetch here since we're just checking connectivity
      const response = await fetch(`${import.meta.env.VITE_BASE_PYRON_URL}/api/health`, {
        method: 'HEAD',
        headers: { 'Cache-Control': 'no-cache' },
      });
      setIsOnline(response.ok);
      return response.ok;
    } catch (error) {
      console.error("Backend connectivity check failed:", error);
      setIsOnline(false);
      return false;
    }
  };

  const retryConnection = () => {
    checkBackendConnection();
  };

  useEffect(() => {
    // Check backend connectivity on mount
    checkBackendConnection();

    // Set up event listeners for browser online/offline status
    const handleOnline = () => {
      checkBackendConnection();
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set up periodic checks
    const intervalId = setInterval(() => {
      if (navigator.onLine) {
        checkBackendConnection();
      }
    }, 30000); // Check every 30 seconds

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(intervalId);
    };
  }, []);

  return (
    <NetworkStatusContext.Provider value={{ isOnline, retryConnection }}>
      {children}
    </NetworkStatusContext.Provider>
  );
};
