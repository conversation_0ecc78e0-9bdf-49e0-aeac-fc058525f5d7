import { api } from "@/services/apiClient";


export async function saveAgent(agent: any): Promise<any> {
  try {
    const response = await api.post(
      "/api/agents/add-agent",
      agent
    );

    console.log("Agent saved successfully");
    return response;
  } catch(error) {
    console.error("Failed to save agent", error);
    return null;
  }
}

export async function getUserAgents(publicKey: string, ticker: string) {
  try {
    const response = await api.get(`/api/agents/get-agent/${publicKey}/${ticker}`);
    console.log({ response });
    return response;
  } catch (error) {
    console.error("Failed to get user agents", error);
    return null;
  }
}

export async function getUserAgentByChatId(chatId: string) {
  try {
    const response = await api.get(`/api/agents/get-agentBychatId/${chatId}`);

    if (!response) {
      return {};
    }
    return response;
  } catch (error) {
    console.error("Failed to get user agent by chat ID", error);
    return null;
  }
}

export async function updateTradeStatus(status: string, botId: string) {
  if (botId) {
    try {
      await api.put(`/api/agents/update-trading-status/${botId}`, {
        tradingStatus: status,
      });
      console.log("Trade status updated successfully");
    } catch (error) {
      console.error("Failed to update trade status", error);
    }
  }
}

export async function saveOrUpdateAgent(agent: any, botId: string) {
  if (botId) {
    try {
      const response = await api.put(`/api/agents/save-or-update-agent/${botId}`, {
        agent: agent,
      });
      console.log("Agent saved successfully");
      return response;
    } catch (error) {
      console.error("Failed to save agent", error);
      return null;
    }
  }
  return null;
}

export async function updateAgentName(chatId: string, newName: string) {
  try {
    const response = await api.put(`/api/agents/update-agent-name/${chatId}`, {
      name: newName,
    });
    console.log("Agent name updated successfully");
    return response;
  } catch (error) {
    console.error("Failed to update agent name", error);
    return null;
  }
}


export async function getAllAgents() {
  try {
    const response = await api.get(`/api/agents/get-all-agents`);
    console.log({ response });
    return response;
  } catch (error) {
    console.error("Failed to get all agents", error);
    return null;
  }
}




