import { api } from "@/services/apiClient";

export async function addPromptsToAgent(agent: any, botId: string) {
    if (botId) {
      try {
        await api.put(`/api/agents/add-prompts-to-agent/${botId}`, {
          agent: agent,
        });
        console.log("Agent saved successfully");
      } catch (error) {
        console.error("Failed to save agent", error);
      }
    }
  }


export async function updateHypothesisStatus(status: string, botId: string) {
    if (botId) {
      try {
        await api.put(`/api/agents/update-hypothesis-status/${botId}`, {
          hypothesisStatus: status,
        });
        console.log("Hypothesis status updated successfully");
      } catch (error) {
        console.error("Failed to update hypothesis status", error);
      }
    }
  }

export async function createHypothesis(logId: string, hypothesis: string) {
  try {
    const response = await api.post(`/api/hypothesis/create`, {
      logId,
      hypothesis,
    });

    console.log("Hypothesis created successfully");
    return response;
  } catch (error) {
    console.error("Error creating hypothesis:", error);
    throw error;
  }
}

export async function getAllHypotheses() {
  try {
    const response = await api.get(`/api/hypothesis/getAll`);
    return response;
  } catch (error) {
    console.error("Error fetching hypotheses:", error);
    throw error;
  }
}

export async function getHypothesesByChatId(chatId: string) {
  try {
    const response = await api.get(`/api/hypothesis/by-chat-id/${chatId}`);
    return response;
  } catch (error) {
    console.error("Error fetching hypotheses:", error);
    throw error;
  }
}