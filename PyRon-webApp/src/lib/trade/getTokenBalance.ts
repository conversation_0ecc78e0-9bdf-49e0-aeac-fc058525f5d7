import { Connection, PublicKey } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID, AccountLayout } from '@solana/spl-token';

/**
 * Fetches the token balance for a specific token in a wallet.
 * @param wallet - The wallet whose token balance is to be fetched.
 * @param connection - The Solana connection object.
 * @param tokenMintAddress - The mint address of the token (e.g., USDC).
 * @returns A promise that resolves to the token balance as a number.
 */
export async function getTokenBalance(wallet: any, connection: Connection, tokenMintAddress: string): Promise<number> {
    try {
        const tokenMintPublicKey = new PublicKey(tokenMintAddress);
        const tokenAccounts = await connection.getTokenAccountsByOwner(wallet.publicKey, {
            mint: tokenMintPublicKey,
            programId: TOKEN_PROGRAM_ID,
        });

        if (tokenAccounts.value.length === 0) {
            console.warn('No token accounts found for the specified mint address.');
            return 0;
        }

        // Iterate over token accounts to find the correct one
        for (const tokenAccount of tokenAccounts.value) {
            const accountInfo = tokenAccount.account.data;
            const accountData = AccountLayout.decode(accountInfo);
            const balance = accountData.amount.toString();

            // Convert balance from smallest unit to actual token amount
            const decimals = 6; // Adjust this according to the token's decimals
            const actualBalance = Number(balance) / Math.pow(10, decimals);

            if (actualBalance > 0) {
                return actualBalance;
            }
        }

        return 0;
    } catch (error) {
        console.error('Failed to fetch token balance:', error);
        throw error;
    }
} 