import { 
    Connection,
    <PERSON>Key, 
    SystemProgram, 
    Transaction, 
    TransactionInstruction, 
    VersionedTransaction
} from "@solana/web3.js";

import { getAssociatedTokenAddress } from "@solana/spl-token";
import { createTransferInstruction } from "@solana/spl-token";

/**
 * Transfer SOL or SPL tokens to a recipient
 * @param wallet wallet instance
 * @param from Send<PERSON>'s public key
 * @param to Recipient's public key
 * @param amount Amount to transfer
 * @param mint Optional mint address for SPL tokens
 * @returns Transaction signature
 */
export async function transfer(
  from: PublicKey,
  to: PublicKey,
  amount: number,
  mint: PublicKey
): Promise<TransactionInstruction> {
  try {
    let transaction: Transaction;
      console.log("mint",mint.toString())
      const fromAta = await getAssociatedTokenAddress(mint, from);
      console.log("fromAta",fromAta.toString())
      const toAta = await getAssociatedTokenAddress(mint, to);
      console.log("toAta",toAta.toString())
      let transferInstruction = createTransferInstruction(
          fromAta,
          toAta,
          from,
          amount*10**6
        )   
    return transferInstruction;
  } catch (error: any) {
    throw new Error(`Transfer failed: ${error.message}`);
  }
}