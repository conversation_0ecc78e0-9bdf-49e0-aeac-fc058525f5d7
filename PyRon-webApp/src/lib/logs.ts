import { api } from "@/services/apiClient";

export async function getBotLogs(chatId: string) {
    try {
      console.log("chat id getbot", chatId);

      const data = await api.get(`/api/logs/get-all-by-chat-id?chatId=${chatId}`);
      return data;
    } catch (error) {
      console.error(error);
      return [];
    }
  }


  export async function updateBotLog(log: any, botId: string) {
    try {
      const response = await api.put(`/api/logs/${botId}`, log);
      console.log("Log updated successfully");
      return response;
    } catch (error: any) {
      console.error("Error updating log:", error.message);
      throw error;
    }
  }

