/** Helper: difference in whole days (rounded down) between two Dates. */
export function dayDifference(dateA, dateB) {
    const msPerDay = 1000 * 60 * 60 * 24;
    // We want B - A if we say “B is now, A is chat time”
    const diff = dateB.getTime() - dateA.getTime();
    return Math.floor(diff / msPerDay);
  }
  
  /** Helper: format a date as “Month Year”, e.g. “March 2025”. */
  export function formatMonthYear(date) {
    // toLocaleString with { month: 'long', year: 'numeric' } => "March 2025"
    return date.toLocaleString("en-US", {
      month: "long",
      year: "numeric",
    });
  }
  
  /**
   * Helper: parse “March 2025” back into { month: 3, year: 2025 }
   * so we can sort them properly.
   */
  export function parseMonthYear(label) {
    // label e.g. "March 2025"
    const [monthName, yearStr] = label.split(" ");
    const year = parseInt(yearStr, 10);
  
    const monthMap = {
      January: 1,
      February: 2,
      March: 3,
      April: 4,
      May: 5,
      June: 6,
      July: 7,
      August: 8,
      September: 9,
      October: 10,
      November: 11,
      December: 12,
    };
    const month = monthMap[monthName] ?? 1; // fallback to 1 if parse fails
  
    return { month, year };
  }