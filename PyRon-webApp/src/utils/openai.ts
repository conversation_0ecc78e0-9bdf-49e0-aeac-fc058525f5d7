import { toast } from "@/hooks/use-toast";

// Interface for the chat messages
export interface ChatMessage {
  type: 'user' | 'ai';
  content: string;
}

// Interface for web search results
interface SearchResult {
  link: string;
  title: string;
  snippet: string;
}

interface WebSearchResponse {
  results: SearchResult[];
  message: string;
}

// Check if the OpenAI API key is stored in localStorage
export const getOpenAIKey = (): string | null => {
  return localStorage.getItem('openai_api_key');
};

// Store the OpenAI API key in localStorage
export const setOpenAIKey = (key: string): void => {
  localStorage.setItem('openai_api_key', key);
};

// Perform web search using Perplexica API
export const searchPerplexica = async (query: string, count: number = 3): Promise<WebSearchResponse> => {
  try {
    const response = await fetch(import.meta.env.VITE_PERPLEXICA_URL+'/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chatModel: { provider: "gemini", model: "gemini-1.5-flash" },
        embeddingModel: { provider: "gemini", model: "text-embedding-004" },
        optimizationMode: "speed",
        focusMode: "webSearch",
        query: query,
        history: []
      })
    });

    if (!response.ok) {
      //throw new Error('Failed to fetch web search results');
      return {
        results: [],
        message: "Failed to fetch web search results"
      };
    }

    const data = await response.json();
    const results: SearchResult[] = data.sources.slice(0, count).map((source: any) => ({
      link: source.metadata?.url || "",
      title: source.metadata?.title || "",
      snippet: source.pageContent || ""
    }));

    return {
      results,
      message: data.message || ""
    };
  } catch (error) {
    console.error('Error performing web search:', error);
    return {
      results: [],
      message: "Failed to fetch web search results"
    };
  }
};

// Send a message to the OpenAI API with web search context
export const sendMessageToOpenAI = async (
  message: string,
  apiKey: string
): Promise<string> => {
  try {
    // First, perform web search
    const searchResults = await searchPerplexica(message);

    // Construct the context from web search results
    const webContext = searchResults.message

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `You are a helpful financial assistant specializing in cryptocurrency trading.
            Provide accurate advice and analysis on trading strategies, market trends, and technical analysis.
            Use the provided web search results to enhance your response with up-to-date information without omitting any important details from the web search results.
            You should use the web search results to provide more accurate and relevant information to the user's question.
            If the websearch contains any information about the user's question, you should use that information to answer the user's question.
            don't show anything like sorry I don't have websearch results. If there is no information in the websearch related to the user's question, you should answer the question based on your training data.
            If the user's message is a casual greeting (e.g., "hey", "hi", "hello"), respond with a short, warm welcome and ask how you can help
            `
          },
          {
            role: 'user',
            content: `Web Search Results:\n${webContext}\n\nUser Question: ${message}`
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Unknown error occurred');
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error('Error calling OpenAI API:', error);
    toast({
      title: "API Error",
      description: error.message || "Failed to get response from OpenAI",
      variant: "destructive"
    });
    throw error;
  }
};
