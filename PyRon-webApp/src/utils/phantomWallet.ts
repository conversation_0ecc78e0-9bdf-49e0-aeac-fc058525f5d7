// Remove the PhantomWindow interface and getPhantomProvider function
// as we will rely solely on the PhantomWalletAdapter
import { PhantomWalletAdapter } from '@solana/wallet-adapter-phantom';
// Check if Phantom is installed
export const isPhantomInstalled = (): boolean => {
  // Check if the PhantomWalletAdapter can be instantiated
  try {
    new PhantomWalletAdapter();
    return true;
  } catch {
    return false;
  }
};

// Connect to Phantom Wallet using the adapter
export const connectPhantomWallet = async (): Promise<{ address: string, display: string } | null> => {
  try {
    const walletAdapter = new PhantomWalletAdapter();
    
    if (!walletAdapter.connected) {
      await walletAdapter.connect();
    }
    
    const walletAddress = walletAdapter.publicKey?.toString();
    if (!walletAddress) {
      throw new Error('Failed to retrieve wallet address');
    }
    
    const displayAddress = `${walletAddress.substring(0, 4)}...${walletAddress.substring(walletAddress.length - 4)}`;
    
    return {
      address: walletAddress,
      display: displayAddress
    };
  } catch (error) {
    console.error('Error connecting to Phantom wallet:', error);
    return null;
  }
};

// Disconnect from Phantom Wallet using the adapter
export const disconnectPhantomWallet = async (): Promise<boolean> => {
  try {
    const walletAdapter = new PhantomWalletAdapter();
    
    if (walletAdapter.connected) {
      await walletAdapter.disconnect();
    }
    
    return true;
  } catch (error) {
    console.error('Error disconnecting from Phantom wallet:', error);
    return false;
  }
};
