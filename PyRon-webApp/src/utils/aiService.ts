import { toast } from "@/hooks/use-toast";
import { AIModelType } from "@/store/aiModelStore";

// System prompt for AI assistants
export const FINANCIAL_ASSISTANT_PROMPT = `You are a helpful financial assistant specializing in cryptocurrency trading.
Provide accurate advice and analysis on trading strategies, market trends, and technical analysis.
Use the provided web search results to enhance your response with up-to-date information without omitting any important details from the web search results.
If the websearch contains any information about the user's question, you should use that information to answer the user's question.
don't show anything like sorry I don't have websearch results. If there is no information in the websearch related to the user's question, you should answer the question based on your training data.
If the user's message is a casual greeting (e.g., "hey", "hi", "hello"), respond with a short answer without going through unnecessary details
give more humanlike responses
the user won't answer you so don't ask for clarification or context, answer the question as best you can.
don't tell the user things like "without additional context or info ...", "with this limited context or info". just use what you have from the websearch and your training data to answer the question.
`;

// Interface for the chat messages
export interface ChatMessage {
  type: 'user' | 'ai';
  content: string;
}

// Interface for web search results
interface SearchResult {
  link: string;
  title: string;
  snippet: string;
}

interface WebSearchResponse {
  results: SearchResult[];
  message: string;
}

// Legacy functions for backward compatibility
export const getOpenAIKey = (): string | null => {
  return localStorage.getItem('openai_api_key');
};

export const setOpenAIKey = (key: string): void => {
  localStorage.setItem('openai_api_key', key);
};

// Get Gemini API key
export const getGeminiKey = (): string | null => {
  return localStorage.getItem('gemini_api_key');
};

// Store the Gemini API key in localStorage
export const setGeminiKey = (key: string): void => {
  localStorage.setItem('gemini_api_key', key);
};

// Perform web search using Perplexica API
export const searchPerplexica = async (query: string, count: number = 3, deepSearch: boolean = false): Promise<WebSearchResponse> => {
  try {
    const response = await fetch(import.meta.env.VITE_PERPLEXICA_URL+'/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chatModel: { provider: "gemini", model: "gemini-1.5-flash" },
        embeddingModel: { provider: "gemini", model: "text-embedding-004" },
        optimizationMode: deepSearch ? "depth" : "speed",
        focusMode: "webSearch",
        query: query,
        history: []
      })
    });

    if (!response.ok) {
      return {
        results: [],
        message: "Failed to fetch web search results"
      };
    }

    const data = await response.json();
    const results: SearchResult[] = data.sources.slice(0, count).map((source: any) => ({
      link: source.metadata?.url || "",
      title: source.metadata?.title || "",
      snippet: source.pageContent || ""
    }));

    return {
      results,
      message: data.message || ""
    };
  } catch (error) {
    console.error('Error performing web search:', error);
    return {
      results: [],
      message: "Failed to fetch web search results"
    };
  }
};

// Send a message to the OpenAI API with web search context
export const sendMessageToOpenAI = async (
  message: string,
  apiKey: string,
  deepSearch: boolean = false
): Promise<string> => {
  try {
    // First, perform web search
    const searchResults = await searchPerplexica(message, 3, deepSearch);

    // Construct the context from web search results
    const webContext = searchResults.message;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: FINANCIAL_ASSISTANT_PROMPT
          },
          {
            role: 'user',
            content: `Web Search Results:\n${webContext}\n\nUser Question: ${message}`
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Unknown error occurred');
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error('Error calling OpenAI API:', error);
    toast({
      title: "API Error",
      description: error.message || "Failed to get response from OpenAI",
      variant: "destructive"
    });
    throw error;
  }
};

// Send a message to the Gemini API with web search context
export const sendMessageToGemini = async (
  message: string,
  apiKey: string,
  deepSearch: boolean = false
): Promise<string> => {
  try {
    // First, perform web search
    const searchResults = await searchPerplexica(message, 3, deepSearch);

    // Construct the context from web search results
    const webContext = searchResults.message;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: `${FINANCIAL_ASSISTANT_PROMPT}

                Web Search Results:
                ${webContext}

                User Question: ${message}`
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 1000,
        }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Unknown error occurred');
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    toast({
      title: "API Error",
      description: error.message || "Failed to get response from Gemini",
      variant: "destructive"
    });
    throw error;
  }
};

// Deep search function for OpenAI models (GPT-3o)
export const deepSearchOpenAI = async (
  message: string,
  apiKey: string
): Promise<string> => {
  try {
    console.log('Using direct OpenAI deep search with GPT-3o');

    // Full reasoning call (tier 4+)
const response = await fetch('https://api.openai.com/v1/responses', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`
  },
  body: JSON.stringify({
    model: 'o3',               // ✅ full model
    input: [                   // <-- NOTE the field name
      
        
      { role: 'user', content: message }
    ],
    max_output_tokens: 2000,   // renamed in this API
    reasoning: { effort: 'high' }   // optional; makes o3 “think harder”
  })
});


    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Unknown error occurred');
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error('Error calling OpenAI API for deep search:', error);
    toast({
      title: "API Error",
      description: error.message || "Failed to get deep search response from OpenAI",
      variant: "destructive"
    });
    throw error;
  }
};

// Deep search function for Gemini models (Gemini 2.5 Pro)
export const deepSearchGemini = async (
  message: string,
  apiKey: string
): Promise<string> => {
  try {
    console.log('Using direct Gemini deep search with Gemini 2.5 Pro');

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro-preview-05-06:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: message
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 2000
        }
      })
    });
    

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Unknown error occurred');
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
  } catch (error) {
    console.error('Error calling Gemini API for deep search:', error);
    toast({
      title: "API Error",
      description: error.message || "Failed to get deep search response from Gemini",
      variant: "destructive"
    });
    throw error;
  }
};

// Unified function to send a message to the selected AI model
export const sendMessageToAI = async (
  message: string,
  apiKey: string,
  modelType: AIModelType,
  deepSearch: boolean = false
): Promise<string> => {
  // If deep hypothesis is enabled, use the direct model-specific deep search functions
  // These functions skip web search and use more advanced models
  if (deepSearch) {
    if (modelType === 'gpt') {
      return deepSearchOpenAI(message, apiKey);
    } else {
      return deepSearchGemini(message, apiKey);
    }
  } else {
    // Otherwise, use the regular functions that use Perplexica API for web search
    if (modelType === 'gpt') {
      return sendMessageToOpenAI(message, apiKey, false);
    } else {
      return sendMessageToGemini(message, apiKey, false);
    }
  }
};
