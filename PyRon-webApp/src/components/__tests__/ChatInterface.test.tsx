import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ChatInterface from '../ChatInterface';
import { ChatProvider } from '../../context/ChatContext';

// Mock all the dependencies
vi.mock('../StockChart', () => ({
  default: () => <div data-testid="stock-chart">Stock Chart</div>
}));

vi.mock('../chat/ChatSection', () => ({
  default: () => <div data-testid="chat-section">Chat Section</div>
}));

vi.mock('../chat/ChatInputForm', () => ({
  default: () => <div data-testid="chat-input">Chat Input</div>
}));

vi.mock('../AIModelSelector', () => ({
  default: () => <div data-testid="ai-model-selector">AI Model Selector</div>
}));

vi.mock('../../store/walletStore', () => ({
  useWalletStore: () => ({
    isConnected: true,
    walletAddress: 'test-address'
  })
}));

vi.mock('../../store/aiModelStore', () => ({
  useAIModelStore: {
    getState: () => ({ currentModel: 'gpt' })
  }
}));

vi.mock('../../lib/chat', () => ({
  getUserChats: vi.fn().mockResolvedValue([]),
  createNewChat: vi.fn(),
  sendMessage: vi.fn(),
  getChatMessages: vi.fn(),
}));

vi.mock('../../context/ChatContext', () => ({
  ChatProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useChatContext: () => ({
    currentChatId: 'test-chat-id',
    messages: [],
    loading: false,
    setMessages: vi.fn(),
    setCurrentChatId: vi.fn(),
    chats: [],
    setChats: vi.fn(),
    agent: null,
  }),
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ChatProvider>
    {children}
  </ChatProvider>
);

describe('ChatInterface Layout Tests', () => {
  it('should have proper resizable panel structure', () => {
    render(
      <TestWrapper>
        <ChatInterface />
      </TestWrapper>
    );

    // Check that stock chart panel has proper flex classes
    const stockChart = screen.getByTestId('stock-chart');
    const stockChartPanel = stockChart.closest('.flex.flex-col.min-h-0');
    expect(stockChartPanel).toBeInTheDocument();
  });

  it('should have chat messages area with flex-1 and overflow-y-auto', () => {
    render(
      <TestWrapper>
        <ChatInterface />
      </TestWrapper>
    );

    // Look for the chat messages container
    const chatContainer = document.querySelector('.flex-1.overflow-y-auto');
    expect(chatContainer).toBeInTheDocument();
  });

  it('should have fixed chat input area with flex-shrink-0', () => {
    render(
      <TestWrapper>
        <ChatInterface />
      </TestWrapper>
    );

    const chatInput = screen.getByTestId('chat-input');
    const inputContainer = chatInput.closest('.flex-shrink-0');
    expect(inputContainer).toBeInTheDocument();
  });

  it('should have AI model selector in the input area', () => {
    render(
      <TestWrapper>
        <ChatInterface />
      </TestWrapper>
    );

    const aiModelSelector = screen.getByTestId('ai-model-selector');
    expect(aiModelSelector).toBeInTheDocument();

    // Should be in the same container as chat input
    const inputArea = aiModelSelector.closest('.flex-shrink-0');
    const chatInput = screen.getByTestId('chat-input');
    const chatInputArea = chatInput.closest('.flex-shrink-0');

    expect(inputArea).toBe(chatInputArea);
  });
});
