import { render, screen } from '@testing-library/react';
import { describe, it, expect, beforeEach } from 'vitest';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Index from '../../pages/Index';
import { ChatProvider } from '../../context/ChatContext';

// Mock external dependencies
vi.mock('../../store/walletStore', () => ({
  useWalletStore: () => ({
    isConnected: false,
    walletAddress: null,
  }),
}));

vi.mock('../../context/ChatContext', () => ({
  ChatProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useChatContext: () => ({
    currentChatId: null,
    messages: [],
    loading: false,
    setMessages: vi.fn(),
    setCurrentChatId: vi.fn(),
    chats: [],
    setChats: vi.fn(),
    agent: null,
  }),
}));

vi.mock('../../lib/chat', () => ({
  getUserChats: vi.fn().mockResolvedValue([]),
  createNewChat: vi.fn(),
  sendMessage: vi.fn(),
  getChatMessages: vi.fn(),
}));

vi.mock('../../services/authService', () => ({
  isAuthenticated: vi.fn().mockReturnValue(false),
  authenticateWallet: vi.fn(),
}));

// Mock components that have external dependencies
vi.mock('../../components/Header', () => ({
  default: () => <div data-testid="header">Header</div>
}));

vi.mock('../../components/ChatInterface', () => ({
  default: () => <div data-testid="chat-interface">Chat Interface</div>
}));

vi.mock('../../components/SidebarMenu', () => ({
  default: ({ isOpen }: { isOpen: boolean }) =>
    <div data-testid="sidebar" style={{ display: isOpen ? 'block' : 'none' }}>Sidebar</div>
}));

vi.mock('../../components/SidebarBackdrop', () => ({
  default: ({ isOpen }: { isOpen: boolean }) =>
    isOpen ? <div data-testid="sidebar-backdrop">Backdrop</div> : null
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <ChatProvider>
          {children}
        </ChatProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Layout Structure Tests', () => {
  beforeEach(() => {
    // Reset any mocks before each test
    vi.clearAllMocks();
  });

  it('should render main layout container with proper CSS classes', () => {
    render(
      <TestWrapper>
        <Index />
      </TestWrapper>
    );

    const mainContainer = screen.getByTestId('header').closest('.flex.flex-col.h-screen');
    expect(mainContainer).toBeInTheDocument();
    expect(mainContainer).toHaveClass('overflow-hidden');
  });

  it('should have header as flex-shrink-0 element', () => {
    render(
      <TestWrapper>
        <Index />
      </TestWrapper>
    );

    const headerContainer = screen.getByTestId('header').closest('.flex-shrink-0');
    expect(headerContainer).toBeInTheDocument();
  });

  it('should have main content area with proper flex classes', () => {
    render(
      <TestWrapper>
        <Index />
      </TestWrapper>
    );

    const chatInterface = screen.getByTestId('chat-interface');
    const mainContentArea = chatInterface.closest('.flex-1.flex.flex-col.min-h-0');
    expect(mainContentArea).toBeInTheDocument();
  });

  it('should prevent body overflow', () => {
    render(
      <TestWrapper>
        <Index />
      </TestWrapper>
    );

    // Check that the main container has overflow-hidden
    const container = document.querySelector('.overflow-hidden');
    expect(container).toBeInTheDocument();
  });
});
