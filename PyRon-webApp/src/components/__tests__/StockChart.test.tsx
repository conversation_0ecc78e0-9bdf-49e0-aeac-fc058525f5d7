import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import StockChart from '../StockChart';
import { ChatProvider } from '../../context/ChatContext';

// Mock dependencies
vi.mock('../chart/TabSelector', () => ({
  default: () => <div data-testid="tab-selector">Tab Selector</div>
}));

vi.mock('../chart/ChartsTab', () => ({
  default: () => <div data-testid="charts-tab">Charts Tab</div>
}));

vi.mock('../chart/TradeLog', () => ({
  default: () => <div data-testid="trade-log">Trade Log</div>
}));

vi.mock('../chart/InfoBar', () => ({
  default: () => <div data-testid="info-bar">Info Bar</div>
}));

vi.mock('../chart/BacktestingTab', () => ({
  default: () => <div data-testid="backtesting-tab">Backtesting Tab</div>
}));

vi.mock('../../store/walletStore', () => ({
  useWalletStore: () => ({
    isConnected: true
  })
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ChatProvider>
    {children}
  </ChatProvider>
);

describe('StockChart Layout Tests', () => {
  it('should have main container with proper flex layout', () => {
    render(
      <TestWrapper>
        <StockChart />
      </TestWrapper>
    );

    const container = document.querySelector('.h-full.bg-\\[\\#1a1a1a\\]\\/90.relative.text-sm.flex.flex-col');
    expect(container).toBeInTheDocument();
  });

  it('should have tab selector as flex-shrink-0', () => {
    render(
      <TestWrapper>
        <StockChart />
      </TestWrapper>
    );

    const tabSelector = screen.getByTestId('tab-selector');
    const tabContainer = tabSelector.closest('.flex-shrink-0');
    expect(tabContainer).toBeInTheDocument();
  });

  it('should have content area with flex-1 and min-h-0', () => {
    render(
      <TestWrapper>
        <StockChart />
      </TestWrapper>
    );

    const contentArea = document.querySelector('.flex-1.min-h-0');
    expect(contentArea).toBeInTheDocument();
  });

  it('should render charts tab by default', () => {
    render(
      <TestWrapper>
        <StockChart />
      </TestWrapper>
    );

    const chartsTab = screen.getByTestId('charts-tab');
    expect(chartsTab).toBeInTheDocument();
  });
});
