
import React, { useState, useEffect } from 'react';

interface NotificationProps {
  message: string;
  duration?: number;
  onClose?: () => void;
}

const Notification: React.FC<NotificationProps> = ({ 
  message, 
  duration = 5000, 
  onClose 
}) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(false);
      if (onClose) onClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  if (!visible) return null;

  return (
    <div className="fixed top-24 right-0 left-0 flex justify-center animate-fade-in z-50">
      <div className="backdrop-blur-md bg-black/60 border border-blue-500/20 py-2.5 px-8 rounded-full text-white shadow-lg shadow-blue-500/5 flex items-center gap-2 whitespace-nowrap">
        <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse"></div>
        {message}
      </div>
    </div>
  );
};

export default Notification;
