import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useChatContext } from '@/context/ChatContext';
import { addPromptsToAgent, updateHypothesisStatus } from '@/lib/hypothesis';
import { VisuallyHidden } from '@reach/visually-hidden';

interface HypothesisDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const HypothesisDialog = ({
  open,
  onOpenChange
}: HypothesisDialogProps) => {
  const { agent, setAgent } = useChatContext();
  const [selectedOption, setSelectedOption] = useState<'buy' | 'sell' | null>(null);
  const [buyPredictionText, setBuyPredictionText] = useState('');
  const [sellPredictionText, setSellPredictionText] = useState('');
  const [isHypothesisOn, setIsHypothesisOn] = useState(agent?.hypothesisStatus === 'on');

  useEffect(() => {
    if (agent) {
      setIsHypothesisOn(agent.hypothesisStatus === 'on');
      setBuyPredictionText(agent.buyReportPrompt || '');
      setSellPredictionText(agent.sellReportPrompt || '');
    }
  }, [agent]);

  const handleOptionSelect = (option: 'buy' | 'sell') => {
    setSelectedOption(option);
  };

  const handleSubmit = async () => {
    if (!agent || !agent.botId) {
      console.error('No agent or botId found');
      return;
    }

    try {
      // Update the agent with both prompts
      const updatedAgent = {
        ...agent,
        buyReportPrompt: buyPredictionText,
        sellReportPrompt: sellPredictionText,
      };

      // Save the prompts to the database
      await addPromptsToAgent(updatedAgent, agent.botId);

      // Toggle hypothesis status
      const newStatus = agent.hypothesisStatus === 'on' ? 'off' : 'on';
      await updateHypothesisStatus(newStatus, agent.botId);

      // Update local state
      setIsHypothesisOn(newStatus === 'on');
      setAgent({
        ...updatedAgent,
        hypothesisStatus: newStatus
      });

      // Close the dialog
     // onOpenChange(false);
    } catch (error) {
      console.error('Error updating hypothesis:', error);
    }
  };

  return <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gradient-to-b from-[#1a1a1a] to-[#151515] border border-gold-light/20 text-white p-0 w-full max-w-[600px] rounded-xl shadow-2xl overflow-hidden animate-fade-in" hideCloseButton>
        <VisuallyHidden>
          <DialogTitle>Auto Hypothesis Configuration</DialogTitle>
        </VisuallyHidden>
        
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJncmlkIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiPjxwYXRoIGQ9Ik0gMjAgMCBMIDAgMCAwIDIwIiBmaWxsPSJub25lIiBzdHJva2U9IiNBRDg2MDkxMCIgc3Ryb2tlLXdpZHRoPSIwLjUiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=')] opacity-5"></div>
        
        <div className="flex flex-col items-center p-6 space-y-6 relative z-10">
          <div className="flex items-center gap-2 mb-2">
            <h2 className="text-3xl font-medium">When my DeFi Agent Executes a...</h2>
          </div>
          
          <div className="flex items-center gap-4 justify-center w-full my-2">
            <button className={`py-3 px-8 rounded-md text-lg font-semibold transition-all ${selectedOption === 'buy' ? 'bg-[#FFD837] text-black shadow-[0_0_15px_rgba(255,216,55,0.4)]' : 'bg-transparent border border-gray-700 text-white hover:border-[#FFD837]/50 hover:bg-[#FFD837]/10'}`} onClick={() => handleOptionSelect('buy')}>
              Buy
            </button>
            
            <span className="text-lg font-semibold text-gold-light">OR</span>
            
            <button className={`py-3 px-8 rounded-md text-lg font-semibold transition-all ${selectedOption === 'sell' ? 'bg-[#FFD837] text-black shadow-[0_0_15px_rgba(255,216,55,0.4)]' : 'bg-transparent border border-gray-700 text-white hover:border-[#FFD837]/50 hover:bg-[#FFD837]/10'}`} onClick={() => handleOptionSelect('sell')}>
              Sell
            </button>
          </div>

          <h3 className="font-semibold text-center mt-2 text-slate-50 text-2xl">Produce a deep-hypothesis about...</h3>

          <div className="w-full relative">
            <Textarea 
              placeholder="Share your political, economic, social & tech ideas with PyRon. Receive a hypothesis on as to what is driving your trading strategy." 
              className="w-full min-h-[120px] bg-[#191919] border border-gold-light/20 rounded-md text-white resize-none p-4 focus:border-gold-light/30 focus:ring-1 focus:ring-gold-light/20 transition-all" 
              value={selectedOption === 'buy' ? buyPredictionText : sellPredictionText} 
              onChange={e => {
                if (selectedOption === 'buy') {
                  setBuyPredictionText(e.target.value);
                } else {
                  setSellPredictionText(e.target.value);
                }
              }} 
            />
            <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-gold-light/30 to-transparent"></div>
          </div>

          <div className="flex justify-center pt-1.5 pb-1">
            <button 
              onClick={handleSubmit}
              className="px-7 py-2 rounded-full bg-transparent border border-[#FFD433] text-white flex items-center gap-3 hover:bg-[#FFD433]/10 transition-colors"
            >
              <span className="text-base">Auto-Hypothesis</span>
              <div className={`w-14 h-6 rounded-full flex items-center justify-center text-base font-bold ${isHypothesisOn ? 'bg-[#FFD433] text-black' : 'bg-gray-600 text-white'}`}>
                {isHypothesisOn ? 'ON' : 'OFF'}
              </div>
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>;
};

export default HypothesisDialog;
