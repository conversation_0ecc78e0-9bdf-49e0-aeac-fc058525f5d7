
import React from 'react';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

const SearchBar: React.FC = () => {
  return (
    <div className="relative mb-7">
      <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
        <Search className="h-5 w-5 text-gold-light/70" />
      </div>
      <Input 
        type="search" 
        placeholder="Search"
        className="w-full pl-10 bg-gradient-to-r from-[#242424] to-[#2a2a2a] border-transparent focus:border-gold-light/30 placeholder-white/50 text-white rounded-lg py-2.5 h-[50px] transition-all shadow-md hover:shadow-gold-dark/10"
      />
      <div className="absolute inset-0 rounded-lg pointer-events-none overflow-hidden">
        <div className="absolute inset-x-0 bottom-0 h-[1px] bg-gradient-to-r from-transparent via-gold-light/30 to-transparent"></div>
      </div>
    </div>
  );
};

export default SearchBar;
