import React, { useState } from 'react';
import { ChevronDown, ChevronUp, MessageSquare, Trash2, Edit3 } from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Chat } from '@/types/chat';

interface AgentProps {
  name: string;
  status: string;
}

interface AgentGroupProps {
  title: string;
  statusColor: string;
  agents: AgentProps[];
  chats?: Chat[];
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  icon: React.ReactNode;
  onSelectChat: (chatId: string) => void;
  onDeleteChat: (chatId: string) => void;
  onRenameChat: (chatId: string, newName: string) => void;
}

const AgentGroup: React.FC<AgentGroupProps> = ({
  title,
  statusColor,
  agents,
  chats = [],
  isOpen,
  onOpenChange,
  icon,
  onSelectChat,
  onDeleteChat,
  onRenameChat
}) => {
  const [editingChatId, setEditingChatId] = useState<string | null>(null);
  const [newTitle, setNewTitle] = useState<string>('');

  const handleEditClick = (chatId: string, currentTitle: string) => {
    setEditingChatId(chatId);
    setNewTitle(currentTitle);
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewTitle(e.target.value);
  };

  const handleTitleSave = (chatId: string) => {
    onRenameChat(chatId, newTitle);
    setEditingChatId(null);
  };

  const handleBlur = (chatId: string) => {
    if (newTitle.trim() !== '') {
      handleTitleSave(chatId);
    } else {
      setEditingChatId(null);
    }
  };

  return (
    <div className="mb-6">
      <Collapsible
        open={isOpen}
        onOpenChange={onOpenChange}
        className="w-full"
      >
        <CollapsibleTrigger className="flex items-center w-full mb-3 hover:bg-gold-dark/10 rounded-md p-2 transition-colors">
          <div className="w-8 h-8 rounded-full bg-gold-DEFAULT flex items-center justify-center mr-3">
            {icon}
          </div>
          <span className="text-white font-medium">Agents</span>
          <span className={`${statusColor} font-medium ml-2`}>{title}</span>
          <div className="ml-auto">
            {isOpen ? (
              <ChevronUp className="h-5 w-5 text-gold-light" />
            ) : (
              <ChevronDown className="h-5 w-5 text-gold-light" />
            )}
          </div>
        </CollapsibleTrigger>
        
        <CollapsibleContent className="ml-11 space-y-2 mb-3">
          {agents.length > 0 ? (
            agents.map((agent, index) => (
              <div 
                key={index} 
                className="flex items-center text-sm text-white p-2 hover:bg-gold-dark/10 rounded-md transition-colors"
              >
                <span className={`h-2 w-2 rounded-full bg-${agent.status === 'active' ? 'green' : 'red'}-500 mr-2`}></span>
                {agent.name}
              </div>
            ))
          ) : chats.length > 0 ? (
            <div className="mt-2">
              {chats.map((chat) => (
                <div 
                  key={chat._id} 
                  className="flex items-center justify-between text-sm text-white p-2 hover:bg-gold-dark/10 rounded-md transition-colors cursor-pointer"
                  onClick={() => onSelectChat(chat._id)}
                >
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <div className="truncate flex-1">
                      {editingChatId === chat._id ? (
                        <input
                          type="text"
                          value={newTitle}
                          onChange={handleTitleChange}
                          onBlur={() => handleBlur(chat._id)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleTitleSave(chat._id);
                            }
                          }}
                          className="text-xs font-medium truncate bg-transparent border-b border-white focus:outline-none"
                          autoFocus
                        />
                      ) : (
                        <div
                          className="text-xs font-medium truncate"
                          onDoubleClick={() => handleEditClick(chat._id, chat.title)}
                        >
                          {chat.title}
                        </div>
                      )}
                      <div className="text-[10px] text-gray-500">
                        {new Date(chat.updatedAt).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-0.5">
                    <button
                      className="p-1 rounded hover:bg-[#333] transition-colors"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditClick(chat._id, chat.title);
                      }}
                    >
                      <Edit3 size={12} className="text-gray-400 hover:text-blue-400" />
                    </button>
                    <button
                      className="p-1 rounded hover:bg-[#333] transition-colors"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteChat(chat._id);
                      }}
                    >
                      <Trash2 size={12} className="text-gray-400 hover:text-red-400" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-white/60 text-sm">No {title.toLowerCase()} agents.</p>
          )}
        </CollapsibleContent>
        
        {!isOpen && agents.length === 0 && chats.length === 0 && (
          <p className="text-white/60 text-sm ml-11">No {title.toLowerCase()} agents.</p>
        )}
      </Collapsible>
    </div>
  );
};

export default AgentGroup;
