import React from 'react';
import { useChatContext } from '@/context/ChatContext';
import { createNewChat, getUserChats } from '@/lib/chat';
import { useWalletStore } from '@/store/walletStore';

const ActionButtons: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { setCurrentChatId, setChats, setAgent } = useChatContext();
  const { walletAddress } = useWalletStore();

  const handleCreateChat = async () => {
    const newChatId = await createNewChat('My New Chat');
    if (newChatId) {
      setCurrentChatId(newChatId);
      setAgent(null);
      // Fetch updated chats and set the new chat ID
      const updatedChats = await getUserChats(walletAddress);
      setChats(updatedChats);
      onClose();
    }
  };

  return (
    <div className="flex items-center gap-3 mb-7">
      <button 
        className="border border-gold-light/30 bg-black hover:bg-black/80 text-white px-5 py-2.5 rounded-lg font-medium flex-1 transition-all flex items-center justify-center"
        onClick={handleCreateChat}
      >
        <span>Create</span>
      </button>
      <button 
        className="border border-gold-light/30 bg-[#222222] hover:bg-black/80 text-white px-5 py-2.5 rounded-lg font-medium flex-1 transition-all flex items-center justify-center"
      >
        <span>Bond</span>
      </button>
    </div>
  );
};

export default ActionButtons;
