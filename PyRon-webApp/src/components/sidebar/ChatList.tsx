import React, { useEffect, useState } from 'react';
import { MessageSquare, Plus, Trash2 } from 'lucide-react';
import { createNewChat, getUserChats, deleteChat } from '@/lib/chat';
import { useWalletStore } from '@/store/walletStore';
import { formatDistanceToNow } from 'date-fns';
import { Chat } from '@/types/chat';
import { getUserAgentByChatId } from '@/lib/agents';
import { useChatContext } from '@/context/ChatContext';

interface ChatListProps {
  onClose?: () => void;
  setCurrentChatId: (chatId: string | null) => void;
  currentChatId: string | null;
  setTradeOnChats: React.Dispatch<React.SetStateAction<Chat[]>>;
  setTradeOffChats: React.Dispatch<React.SetStateAction<Chat[]>>;
}

const ChatList: React.FC<ChatListProps> = ({ onClose, setCurrentChatId, currentChatId, setTradeOnChats, setTradeOffChats }) => {
  const { walletAddress, isConnected } = useWalletStore();
  const { setAgent } = useChatContext();
  const [chats, setChats] = useState<Chat[]>([]);

  useEffect(() => {
    if (walletAddress) {
      getUserChats(walletAddress).then(async (chats) => {
        const tradeOn: Chat[] = [];
        const tradeOff: Chat[] = [];

        for (const chat of chats) {
          const agent = await getUserAgentByChatId(chat._id);
          if (agent?.tradingStatus === 'on') {
            tradeOn.push(chat);
          } else {
            tradeOff.push(chat);
          }
        }

        setTradeOnChats(tradeOn);
        setTradeOffChats(tradeOff);
      });
    }
  }, [walletAddress]);

  const handleSelectChat = async (chatId: string) => {
    console.log('Selected chatId:', chatId);
    setCurrentChatId(chatId);
    console.log('Updated currentChatId:', chatId);

    // Fetch the agent related to the selected chat
    try {
      const agent = await getUserAgentByChatId(chatId);
      setAgent(agent);
      console.log('Updated Agent:', agent);
    } catch (error) {
      console.error('Error fetching agent:', error);
    }

    if (onClose) {
      onClose();
    }
  };

  const handleNewChat = async () => {
    const newChatId = await createNewChat();
    if (newChatId) {
      getUserChats(walletAddress).then(setChats);
      setCurrentChatId(newChatId);
    }
    if (onClose) {
      onClose();
    }
  };

  const handleDeleteChat = async (chatId: string) => {
    await deleteChat(chatId);
    getUserChats(walletAddress).then(setChats);
  };

  if (!isConnected) {
    return (
      <div className="mt-4">
        <div className="text-xs text-gray-500 text-center py-2">
          Connect your wallet to see your chats
        </div>
      </div>
    );
  }

  return (
    <div className="mt-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium text-white/80">Chats</h3>
        <button 
          onClick={handleNewChat}
          className="p-1 rounded hover:bg-gold-dark/20 transition-colors"
        >
          <Plus size={16} className="text-gold-light" />
        </button>
      </div>

      {chats.length === 0 ? (
        <div className="text-xs text-gray-500 text-center py-2">
          No chats yet. Start a new conversation!
        </div>
      ) : (
        <div className="space-y-1">
          {chats.map((chat) => (
            <div 
              key={chat._id} 
              className={`flex items-center justify-between px-3 py-2 rounded-md cursor-pointer transition-colors ${
                chat._id === currentChatId
                  ? 'bg-gold-dark/20 text-gold-light'
                  : 'hover:bg-[#222222]'
              }`}
              onClick={() => handleSelectChat(chat._id)}
            >
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <MessageSquare size={14} className={chat._id === currentChatId ? 'text-gold-light' : 'text-gray-400'} />
                <div className="truncate flex-1">
                  <div className="text-xs font-medium truncate">{chat.title}</div>
                  <div className="text-[10px] text-gray-500">
                    {formatDistanceToNow(new Date(chat.updatedAt), { addSuffix: true })}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-0.5">
                <button 
                  className="p-1 rounded hover:bg-[#333] transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteChat(chat._id);
                  }}
                >
                  <Trash2 size={12} className="text-gray-400 hover:text-red-400" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ChatList;
