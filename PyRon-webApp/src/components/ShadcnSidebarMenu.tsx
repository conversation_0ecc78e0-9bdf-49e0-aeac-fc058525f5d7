
import React from "react";
import { 
  <PERSON>bar,
  <PERSON>bar<PERSON>ontent,
  <PERSON>bar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";

const ShadcnSidebarMenu = () => {
  const isMobile = useIsMobile();
  
  return (
    <Sidebar>
      <SidebarHeader>
        <h2 className="text-xl font-bold px-2">Trading App</h2>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          <div className="mb-2 px-2 text-xs font-medium text-muted-foreground uppercase tracking-wider">
            Navigation
          </div>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <a href="/">Dashboard</a>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <a href="/trade-log">Trade Log</a>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <a href="/backtest">Backtest</a>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <a href="/simulation">Simulation</a>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <a href="/charts">Charts</a>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <a href="/code">Code</a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <div className="px-2 text-sm text-muted-foreground">
          Version 1.0.0
        </div>
      </SidebarFooter>
    </Sidebar>
  );
};

export default ShadcnSidebarMenu;
