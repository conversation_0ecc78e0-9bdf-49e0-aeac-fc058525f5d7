
import React from 'react';

// Define trade data structure
export interface Trade {
  id: string;
  market: string;
  type: 'Long' | 'Short';
  size: string;
  price: string;
  notional: string;
  fee: string;
  date: string;
  time: string;
}

interface TradeLogSectionProps {
  trades: Trade[];
}

const TradeLogSection = ({ trades }: TradeLogSectionProps) => {
  return (
    <div className="p-2 space-y-1 h-full mt-0">
      <div className="text-xs text-gray-400 mb-2">Recent Trades</div>
      <div className="space-y-2">
        {trades.map(trade => (
          <div key={trade.id} className="bg-black/30 border border-gray-700/30 rounded p-2 text-xs">
            <div className="flex justify-between items-center">
              <span className="font-medium">{trade.market}</span>
              <span className={`${trade.type === 'Long' ? 'text-green-500' : 'text-red-500'}`}>
                {trade.type}
              </span>
            </div>
            <div className="grid grid-cols-2 gap-1 mt-1 text-[10px] text-gray-300">
              <div>Size: {trade.size}</div>
              <div>Price: {trade.price}</div>
              <div>Notional: {trade.notional}</div>
              <div>Fee: {trade.fee}</div>
              <div className="col-span-2 text-gray-500 mt-1">{trade.date} • {trade.time}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TradeLogSection;
