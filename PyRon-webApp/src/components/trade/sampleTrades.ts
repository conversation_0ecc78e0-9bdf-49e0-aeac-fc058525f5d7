
import { Trade } from './TradeLogSection';

// Sample trade data matching the screenshot
export const sampleTrades: Trade[] = [{
  id: '1',
  market: 'BTC-PERP',
  type: 'Long',
  size: '0.0001 BTC',
  price: '$83,341.3',
  notional: '$8.33',
  fee: '0.002084 USDC',
  date: '31/03/2025',
  time: '15:30:27'
}, {
  id: '2',
  market: 'BTC-PERP',
  type: 'Long',
  size: '0.0001 BTC',
  price: '$83,356.4',
  notional: '$8.33',
  fee: '0.002084 USDC',
  date: '31/03/2025',
  time: '15:30:19'
}, {
  id: '3',
  market: 'BTC-PERP',
  type: 'Short',
  size: '0.0001 BTC',
  price: '$83,125.6',
  notional: '$8.31',
  fee: '0.002079 USDC',
  date: '31/03/2025',
  time: '14:35:18'
}, {
  id: '4',
  market: 'BTC-PERP',
  type: 'Short',
  size: '0.0001 BTC',
  price: '$83,320.6',
  notional: '$8.33',
  fee: '0.002084 USDC',
  date: '31/03/2025',
  time: '14:30:27'
}, {
  id: '5',
  market: 'BTC-PERP',
  type: 'Long',
  size: '0.0001 BTC',
  price: '$83,285.7',
  notional: '$8.32',
  fee: '0.002083 USDC',
  date: '31/03/2025',
  time: '14:00:36'
}, {
  id: '6',
  market: 'BTC-PERP',
  type: 'Short',
  size: '0.0001 BTC',
  price: '$83,268.7',
  notional: '$8.32',
  fee: '0.002082 USDC',
  date: '31/03/2025',
  time: '14:00:21'
}];
