import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, MessageSquare } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import ActionButtons from './sidebar/ActionButtons';
import SearchBar from './sidebar/SearchBar';
import AgentGroup from './sidebar/AgentGroup';
import ChatList from './sidebar/ChatList';
import { Separator } from '@/components/ui/separator';
import { useChatContext } from '@/context/ChatContext';
import { Chat } from '@/types/chat';
import { getUserAgentByChatId, saveOrUpdateAgent, updateAgentName } from '@/lib/agents';
import { deleteChat, getUserChats } from '@/lib/chat';
import { useWalletStore } from '@/store/walletStore';
import { toast } from '@/hooks/use-toast';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const SidebarMenu: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const { currentChatId, setCurrentChatId, setAgent, chats, setChats } = useChatContext();
  const [agentsOnOpen, setAgentsOnOpen] = useState(true);
  const [agentsOffOpen, setAgentsOffOpen] = useState(true);
  const [chatsOpen, setChatsOpen] = useState(true);

  const [tradeOnChats, setTradeOnChats] = useState<Chat[]>([]);
  const [tradeOffChats, setTradeOffChats] = useState<Chat[]>([]);

  const { isConnected, walletAddress } = useWalletStore();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    console.log('SidebarMenu: currentChatId changed to:', currentChatId);
  }, [currentChatId]);

  useEffect(() => {
    const updateTradeChats = async () => {
      const agentPromises = chats.map(chat => getUserAgentByChatId(chat._id));
      const agents = await Promise.all(agentPromises);

      const onChats: Chat[] = [];
      const offChats: Chat[] = [];
      agents.forEach((agent, index) => {
        if (agent && agent.tradingStatus === 'on') {
          onChats.push(chats[index]);
        } else {
          offChats.push(chats[index]);
        }
      });

      setTradeOnChats(onChats);
      setTradeOffChats(offChats);
    };

    updateTradeChats();
  }, [chats]);

  useEffect(() => {
    console.log('Chats updated:', chats);
  }, [chats]);

  const handleSelectChat = async (chatId: string) => {
    setCurrentChatId(chatId);
    try {
      const agent = await getUserAgentByChatId(chatId);
      setAgent(agent);
    } catch (error) {
      console.error('Error fetching agent:', error);
    }
    onClose();
  };

  const handleDeleteChat = async (chatId: string) => {
    let agent = await getUserAgentByChatId(chatId);
    if (Object.keys(agent).length === 0 || !agent.number || agent.number < 0) {
      await deleteChat(chatId);
      setChats(prevChats => prevChats.filter(chat => chat._id !== chatId));
      //getUserChats(walletAddress).then(setChats);
    }
    else{
      toast({
        title: "cannot delete",
        description: "this agent have deposited funds",
        variant: "destructive"
      });
    }

  };

  const handleRenameChat = async (chatId: string, newName: string) => {
    try {
      // Update the chat title locally
      setChats(prevChats => prevChats.map(chat => chat._id === chatId ? { ...chat, title: newName } : chat));

      // Update the agent's name in the database using the chat ID directly
      await updateAgentName(chatId, newName);
      console.log('Agent name updated successfully');
    } catch (error) {
      console.error('Error updating agent name:', error);
    }
  };

  return (
    <div 
      className={`fixed inset-y-0 left-0 w-[300px] z-50 bg-[#1a1a1a] transform transition-transform duration-300 ease-in-out border-r border-gold-dark/20 ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}
    >
      <ScrollArea className="h-full">
        <div className="flex flex-col h-full p-5">
          {/* Top action buttons */}
          <ActionButtons onClose={onClose} />
          
          {/* Search bar */}
          <SearchBar />
          
          <Separator className="bg-gray-800/50 my-3" />
          
          {/* Loading indicator */}
          {isLoading && (
            <div className="flex justify-center items-center mb-3">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-white rounded-full animate-bounce delay-150"></div>
                <div className="w-2 h-2 bg-white rounded-full animate-bounce delay-300"></div>
              </div>
            </div>
          )}

          {/* Agents On section - with dropdown */}
          <AgentGroup
            title="On"
            statusColor="text-green-500"
            agents={[]}
            chats={tradeOnChats}
            isOpen={agentsOnOpen}
            onOpenChange={setAgentsOnOpen}
            icon={<CheckCircle className="h-4 w-4 text-white" />}
            onSelectChat={handleSelectChat}
            onDeleteChat={handleDeleteChat}
            onRenameChat={handleRenameChat}
          />
          
          {/* Agents Off section - with dropdown */}
          <AgentGroup
            title="Off"
            statusColor="text-red-500"
            agents={[]}
            chats={tradeOffChats}
            isOpen={agentsOffOpen}
            onOpenChange={setAgentsOffOpen}
            icon={<XCircle className="h-4 w-4 text-white" />}
            onSelectChat={handleSelectChat}
            onDeleteChat={handleDeleteChat}
            onRenameChat={handleRenameChat}
          />

        </div>
      </ScrollArea>
    </div>
  );
};

export default SidebarMenu;
