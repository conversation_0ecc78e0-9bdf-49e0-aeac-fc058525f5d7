import React, { useState, useEffect } from 'react';
import <PERSON><PERSON><PERSON> from './StockChart';
import ApiKeyModal from './ApiKeyModal';
import { toast } from '@/hooks/use-toast';
import ChatSection from './chat/ChatSection';
import ChatInputForm from './chat/ChatInputForm';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { createNewChat, sendMessage, getUserChats, getChatMessages } from '@/lib/chat';
import { useWalletStore } from '@/store/walletStore';
import { useAIModelStore } from '@/store/aiModelStore';
import { Chat } from '@/types/chat';
import { useParams } from 'react-router-dom';
import { useChatContext } from '@/context/ChatContext';
import { v4 as uuidv4 } from 'uuid';
import { ExtendedChatMessage } from '@/types/chat';
import AIModelSelector from './AIModelSelector';

const ChatInterface: React.FC = () => {
  const { currentChatId, messages, loading, setMessages, setCurrentChatId, chats, setChats } = useChatContext();
  const [isLoading, setIsLoading] = useState(false);

  console.log('ChatInterface rendering with currentChatId:', currentChatId);
  console.log('ChatInterface messages:', messages);

  const { isConnected, walletAddress } = useWalletStore();
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const { chatId } = useParams<{ chatId: string }>();

  // Fetch all chats when wallet address changes
  useEffect(() => {
    if (walletAddress) {
      getUserChats(walletAddress).then(setChats);
    }
  }, [walletAddress]);

  // Set current chat ID from URL parameter
  useEffect(() => {
    if (chatId) {
      setCurrentChatId(chatId);
    }
  }, [chatId, setCurrentChatId]);

  // Fetch messages when current chat ID changes
  useEffect(() => {
    console.log('useEffect triggered with currentChatId:', currentChatId);

    const fetchMessages = async () => {
      if (currentChatId) {
        try {
          console.log('Fetching messages for chatId:', currentChatId);
          const fetchedMessages = await getChatMessages(currentChatId);
          console.log('Fetched messages:', fetchedMessages);

          // Transform fetched messages to ExtendedChatMessage
          const extendedMessages = fetchedMessages.map(msg => ({
            ...msg,
            status: 'sent' as 'sent',
            sender: msg.type
          }));

          // Update the specific chat with the fetched messages
          setChats((prevChats: Chat[]) => prevChats.map(chat =>
            chat._id === currentChatId ? { ...chat, messages: extendedMessages } : chat
          ));

          // Also update the context messages if needed
          setMessages(extendedMessages);
        } catch (error) {
          console.error('Error fetching messages:', error);
        }
      }
    };

    fetchMessages();
  }, [currentChatId]);

  const handleSendMessage = async (message: string, deepHypothesis: boolean = false) => {
    if (!isConnected) {
      toast({
        title: "Wallet not connected",
        description: "Please connect your wallet to send messages",
        variant: "destructive"
      });
      return;
    }

    // Get the API key from the AI model store
    const { getCurrentApiKey } = useAIModelStore.getState();
    const apiKey = getCurrentApiKey();

    if (!apiKey) {
      setShowApiKeyModal(true);
      return;
    }

    // Create a new chat if there is no current chat ID
    let chatId = currentChatId;
    if (!chatId) {
      chatId = await createNewChat('My New Chat');
      if (chatId) {
        setCurrentChatId(chatId);
        // Fetch updated chats and set the new chat ID
        const updatedChats = await getUserChats(walletAddress);
        setChats(updatedChats);
      } else {
        console.error("Failed to create a new chat");
        return;
      }
    }

    const messageId = uuidv4();
    const newMessage: ExtendedChatMessage = {
      _id: messageId,
      content: message,
      status: 'pending',
      sender: 'user',
      type: 'user',
      timestamp: new Date()
    };

    setMessages([...messages, newMessage]);
    setIsLoading(true);

    try {
      await sendMessage(chatId, message, deepHypothesis);
      const fetchedMessages = await getChatMessages(chatId);

      const extendedMessages = fetchedMessages.map(msg => ({
        ...msg,
        status: 'sent' as 'sent',
        sender: msg.type
      }));

      setMessages(extendedMessages);
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages(messages.map(msg =>
        msg._id === messageId ? { ...msg, status: 'failed' } : msg
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const handleApiKeySaved = () => {
    setShowApiKeyModal(false);
    const { currentModel } = useAIModelStore.getState();
    toast({
      title: "API Key Saved",
      description: `Your ${currentModel === 'gpt' ? 'OpenAI' : 'Gemini'} API key has been saved successfully.`
    });
  };

  const currentChat = chats.find(chat => chat._id === currentChatId);
  console.log('Current chat:', currentChat);
  console.log('Current chat messages:', currentChat?.messages);

  return (
    <ResizablePanelGroup direction="horizontal" className="flex h-full w-full relative">
      <ResizablePanel defaultSize={65} minSize={30} className="flex flex-col min-h-0">
        <div className="flex-1 min-h-0">
          <StockChart />
        </div>
      </ResizablePanel>

      <ResizableHandle withHandle className="z-10 bg-gray-800/30" />

      <ResizablePanel defaultSize={35} minSize={20} className="flex flex-col min-h-0">
        {/* Chat Messages Area - takes remaining space */}
        <div className="flex-1 bg-[#1a1a1a] border-t border-gray-800/30 min-h-0 flex flex-col">
          <div className="flex-1 overflow-y-auto">
            {!isConnected ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-gray-400 text-sm px-6">
                  Connect your wallet to start chatting
                </div>
              </div>
            ) : currentChat && messages ? (
              <ChatSection chatHistory={messages} isLoading={isLoading} />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-gray-400 text-sm px-6">
                  Ask questions about Solana trading strategies, market analysis, or technical indicators
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Fixed Chat Input Area */}
        <div className="flex-shrink-0 bg-[#1a1a1a] border-t border-gray-800/30">
          <div className="flex justify-end p-2">
            <AIModelSelector />
          </div>
          <div className="p-4">
            <ChatInputForm
              onSendMessage={handleSendMessage}
              isLoading={loading}
              disabled={!isConnected}
            />
          </div>
        </div>
      </ResizablePanel>

      {showApiKeyModal && <ApiKeyModal onClose={() => setShowApiKeyModal(false)} onApiKeySaved={handleApiKeySaved} />}
    </ResizablePanelGroup>
  );
};

export default ChatInterface;
