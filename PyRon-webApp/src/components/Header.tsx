import React, { useState, useEffect } from 'react';
import { Menu } from 'lucide-react';
import { Input } from '@/components/ui/input';
import SidebarMenu from './SidebarMenu';
import SidebarBackdrop from './SidebarBackdrop';
import WalletConnect from './WalletConnect';
import { Switch } from '@/components/ui/switch';
import AutoTradeDialog from './AutoTradeDialog';
import ManageFundsDialog from './ManageFundsDialog';
import HypothesisDialog from './HypothesisDialog';
import { useChatContext } from '@/context/ChatContext';
import { useWalletStore } from '@/store/walletStore';
import { toast } from '@/hooks/use-toast';

const Header = () => {
  const { agent } = useChatContext();
  const { isConnected, walletAddress } = useWalletStore();
  const [autoTrade, setAutoTrade] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [agentName, setAgentName] = useState('');
  const [autoTradeDialogOpen, setAutoTradeDialogOpen] = useState(false);
  const [manageFundsDialogOpen, setManageFundsDialogOpen] = useState(false);
  const [hypothesisDialogOpen, setHypothesisDialogOpen] = useState(false);

  // Check if both wallet is connected and agent is selected
  const showAgentFeatures = isConnected && agent;

  useEffect(() => {
    if (agent && isConnected) {
      console.log('agent.tradingStatus:', agent.tradingStatus);
      setAutoTrade(agent.tradingStatus === 'on');
    } else {
      setAutoTrade(false);
    }
  }, [agent, isConnected]);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleAutoTradeToggle = () => {
    setAutoTradeDialogOpen(true);
  };

  const handleHypothesisToggle = () => {
    setHypothesisDialogOpen(true);
  };

  return <>
      <header className="w-full h-16 flex items-center justify-between bg-[#141414] border-b border-gray-800/30 z-10 px-[7px] flex-shrink-0">
        <div className="flex items-center gap-2 flex-shrink-0 mx-[10px]">
          <button className="p-0 text-white hover:text-gold-light flex-shrink-0" onClick={toggleSidebar}>
            <Menu size={23} />
          </button>

          <div className="relative w-[360px]">
            <Input
              type="text"
              value={agentName}
              onChange={e => setAgentName(e.target.value)}
              placeholder="Name your DeFi Agent"
              className="w-full h-9 text-sm bg-[#222222] border-0 rounded-lg text-white pl-4 focus:ring-0 focus:outline-none transition-all shadow-lg shadow-black/20"
            />
            <div className="absolute inset-x-0 bottom-0 h-[1px] bg-gradient-to-r from-transparent via-gold-light/30 to-transparent"></div>
          </div>
        </div>

        <div className="flex items-center gap-2 flex-nowrap">
          {showAgentFeatures && (
            <>
              <button
                className="border border-gold-light/30 bg-[#222222] hover:bg-black/80 text-white text-sm rounded-full whitespace-nowrap h-9 flex-shrink-0 px-[19px] mx-[4px]"
                onClick={() => setManageFundsDialogOpen(true)}
              >
                Manage Funds
              </button>

              <div className="border border-gold-light/30 bg-[#222222] px-4 rounded-full flex items-center h-9 gap-3 flex-shrink-0">
                <span className="text-white text-sm no-wrap">Auto-Hypothesis</span>
                <Switch
                  checked={agent?.hypothesisStatus === 'on'}
                  onCheckedChange={handleHypothesisToggle}
                  variant="success"
                  className="flex-shrink-0 scale-70"
                />
              </div>
            </>
          )}
          <div className="border border-gold-light/30 bg-[#222222] px-4 rounded-full flex items-center h-9 gap-3 flex-shrink-0 mx-[10px]">
                <span className="text-white text-sm no-wrap">Auto-Trade</span>
                <Switch
                  checked={autoTrade}
                  onCheckedChange={handleAutoTradeToggle}
                  variant="success"
                  className="flex-shrink-0 scale-70"
                />
              </div>
          <WalletConnect />
        </div>
      </header>

      <SidebarMenu isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      <SidebarBackdrop isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <AutoTradeDialog
        open={autoTradeDialogOpen}
        onOpenChange={setAutoTradeDialogOpen}
      />

      <ManageFundsDialog
        open={manageFundsDialogOpen}
        onOpenChange={setManageFundsDialogOpen}
      />

      <HypothesisDialog
        open={hypothesisDialogOpen}
        onOpenChange={(open) => {
          setHypothesisDialogOpen(open);
        }}
      />
    </>;
};

export default Header;
