import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { DollarSign } from 'lucide-react';
import { VisuallyHidden } from '@reach/visually-hidden';
import { depositFunction } from '@/lib/trade/drift';
import { useWalletStore } from '@/store/walletStore';
import { saveOrUpdateAgent } from '@/lib/agents';
import { toast } from '@/hooks/use-toast';


interface DepositDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  agent: any; // Adjust the type according to your agent structure
  onDepositSuccess: (updatedAgent: any) => void; // Callback to update agent after deposit
}

const DepositDialog: React.FC<DepositDialogProps> = ({
  open,
  onOpenChange,
  agent,
  onDepositSuccess
}) => {
  const [amount, setAmount] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Add a check for agent
  if (!agent) {
    return null; // or return a fallback UI
  }

  const { wallet, connection, driftClient } = useWalletStore.getState();

  const handleDeposit = () => {
    const depositAmount = parseFloat(amount);

    if (isNaN(depositAmount) || depositAmount <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid deposit amount greater than zero.",
        variant: "destructive"
      });
      return;
    }

    if (!wallet || !wallet.publicKey || !connection || !driftClient) {
      toast({
        title: "Wallet Not Connected",
        description: "Please reconnect your wallet to proceed with the deposit.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    depositFunction(
      wallet,
      depositAmount,
      connection,
      driftClient,
      true,
      undefined,
    ).then((res) => {
      console.log("res", res);
      if (res && res.subAccountId) {
        const updatedAgent = {
          ...agent,
          number: res.subAccountId,
          tradingStatus: "on",
          deposit: depositAmount,
        };
        console.log("setting agent", updatedAgent);
        saveOrUpdateAgent(updatedAgent, agent.botId).then((res) => {
          console.log("update agent res", res);
          onDepositSuccess(updatedAgent);
          toast({
            title: "Deposit successful",
            description: "Your deposit has been successfully processed.",
            variant: "default"
          });
        });
      }
      setIsLoading(false);
    }).catch((error) => {
      console.error("Deposit failed", error);
      toast({
        title: "Deposit failed",
        description: "Please try again.",
        variant: "destructive"
      });
      setIsLoading(false);
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent hideCloseButton={true} className="max-w-[750px] w-[90vw] bg-[#1a1a1a] text-white border border-[#333333] p-0 rounded-2xl overflow-hidden">
        <VisuallyHidden>
          <DialogTitle>Deposit Dialog</DialogTitle>
        </VisuallyHidden>
        <div className="flex flex-col space-y-3 p-6">
          <div className="flex flex-col gap-6">
            <div className="bg-[#FFD433] text-black font-medium py-3 px-5 rounded-xl w-full text-center text-lg">
              {agent.agentName}
            </div>
            <div className="relative">
              <div className="absolute left-5 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                <DollarSign className="h-5 w-5 text-[#FFD433]" />
                <span className="text-gray-400 text-lg">USDC</span>
              </div>
              <input
                type="text"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full bg-[#222222] border border-[#333333] rounded-full py-4 pl-24 pr-5 text-white text-center text-3xl focus-visible:caret-center"
                placeholder=""
              />
            </div>
            <button
              onClick={handleDeposit}
              className="bg-[#222222] hover:bg-[#2a2a2a] text-white py-3 px-4 rounded-full border border-[#333333] text-lg font-medium transition-colors"
              disabled={isLoading}
            >
              {isLoading ? "Processing ..." : "Deposit"}
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DepositDialog; 