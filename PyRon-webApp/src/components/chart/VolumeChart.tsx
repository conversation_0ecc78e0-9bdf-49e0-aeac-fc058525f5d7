
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, <PERSON>lt<PERSON>, ResponsiveContainer, Cell } from 'recharts';

interface ChartDataPoint {
  date: string;
  volume: number;
  isUp: boolean;
  [key: string]: any;
}

interface VolumeChartProps {
  data: ChartDataPoint[];
}

const VolumeChart: React.FC<VolumeChartProps> = ({ data }) => {
  // Brighter gold colors (2% brighter than the previous ones)
  const brightGoldUp = "#FFD635"; // Brightened from #FFD433
  const brightGoldDown = "#DEAE0C"; // Brightened from #DCAc0A
  
  return (
    <div className="h-[25%]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{
            top: 0,
            right: 35,
            left: 6,
            bottom: 25,
          }}
          style={{ backgroundColor: '#161616' }} // 2% brighter from #141414
        >
          <XAxis 
            dataKey="date" 
            tick={{ fill: '#575757', fontSize: 12 }} // 2% brighter from #555
            stroke="#353535" // 2% brighter from #333
            interval={20}
            axisLine={false}
          />
          <YAxis 
            domain={[0, 'dataMax + 1']} 
            orientation="right" 
            tick={{ fill: '#575757', fontSize: 12 }} // 2% brighter from #555
            stroke="#353535" // 2% brighter from #333
            axisLine={false}
            tickCount={3}
          />
          <Tooltip 
            contentStyle={{ 
              backgroundColor: 'rgba(2, 2, 2, 0.9)', // 2% brighter from rgba(0, 0, 0, 0.9)
              border: '1px solid #353535', // 2% brighter from #333
              borderRadius: '4px',
              color: '#fff',
              fontSize: 12
            }} 
            labelStyle={{ color: '#9B9B9B' }} // 2% brighter from #999
            formatter={(value: any) => [typeof value === 'number' ? value.toFixed(2) : value, 'Volume']}
          />
          <Bar dataKey="volume" fillOpacity={0.5}>
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.isUp ? brightGoldUp : brightGoldDown} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default VolumeChart;
