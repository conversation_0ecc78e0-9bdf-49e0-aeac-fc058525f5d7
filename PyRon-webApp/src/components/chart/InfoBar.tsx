
import React from 'react';

interface InfoBarProps {
  assetName: string;
  assetSymbol: string;
  isTradeLog?: boolean;
}

const InfoBar: React.FC<InfoBarProps> = ({
  assetName,
  assetSymbol,
  isTradeLog = false
}) => {
  // We've removed the content as previously requested
  // The component now serves as a placeholder that will be hidden behind the resizer
  return (
    <div className="fixed left-0 top-0 pointer-events-none">
      {/* Content removed as requested */}
    </div>
  );
};

export default InfoBar;
