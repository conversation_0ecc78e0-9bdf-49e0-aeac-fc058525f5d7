import { ReactNode } from "react";

export interface TabConfig {
  id: string;
  label: string;
  icon: ReactNode;
}

export interface TabData {
  id: string;
  label: string;
  icon: ReactNode;
  comparisonAssets: string[];
  activeTimeframe: string;
}

export interface ChartDataPoint {
  date: string;
  open: number;
  close: number;
  high: number;
  low: number;
  price: number;
  volume: number;
  isUp: boolean;
}

export interface Trade {
  _id: string;
  chatId: string;
  type: string;
  side: string;
  size: string;
  total: string;
  percentage: string;
  market: string;
  status: string;
  shown: number;
  timestamps: string; // Assuming this is a string in ISO format
}

export interface Hypothesis {
  _id: string;
  logId: string;
  hypothesis: string;
  date: string;
}
