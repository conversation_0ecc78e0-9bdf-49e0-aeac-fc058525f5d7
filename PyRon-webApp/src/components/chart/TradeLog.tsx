import React, { useEffect, useState } from 'react';
import { format, isToday, parseISO } from 'date-fns';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ExternalLink, User } from 'lucide-react';
import { Trade } from './types';
import { getBotLogs } from '@/lib/logs'; // Import the getBotLogs function

interface TradeLogProps {
  chatId: string; // Add chatId as a prop
  isWalletConnected: boolean; // Add wallet connection status as a prop
}

// Filter out failed trades
const filterValidTrades = (trades: Trade[]) => {
  return trades.filter(trade => trade.status.toLowerCase() !== 'failed');
};

const TradeLog: React.FC<TradeLogProps> = ({ chatId, isWalletConnected }) => {
  const [trades, setTrades] = useState<Trade[]>([]);

  useEffect(() => {
    if (isWalletConnected) {
      getBotLogs(chatId).then((data) => {
        console.log("trades", data);
        setTrades(data);
      }).catch((error) => {
        console.error("Failed to fetch trades:", error);
      });
    } else {
      setTrades([]); // Clear trades if wallet is disconnected
    }
  }, [chatId, isWalletConnected]); // Re-fetch trades when chatId or wallet connection status changes

  const validTrades = filterValidTrades(trades);

  // Sample crypto trades from the screenshot


  return (
    <div className="h-full bg-black/90 overflow-y-auto">
      <div className="w-full">
        <Table className="w-full">
          <TableHeader className="bg-[#12151f] sticky top-0 z-10">
            <TableRow className="border-b border-gray-800">
              <TableHead className="text-gray-400 font-normal py-4 px-4">Market</TableHead>
              <TableHead className="text-gray-400 font-normal py-4">Size</TableHead>
              <TableHead className="text-gray-400 font-normal py-4">Total</TableHead>
              <TableHead className="text-gray-400 font-normal py-4">Percentage</TableHead>
              <TableHead className="text-gray-400 font-normal py-4">Type</TableHead>
              <TableHead className="text-gray-400 font-normal py-4">Side</TableHead>
              <TableHead className="text-gray-400 font-normal py-4">Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {validTrades.map((trade) => (
              <TableRow key={trade._id} className="border-b border-gray-800/30 hover:bg-[#1a1f27]">
                <TableCell className="py-4 px-4">{trade.market}</TableCell>
                <TableCell className="text-white py-4">{trade.size}</TableCell>
                <TableCell className="text-white py-4">{trade.total}</TableCell>
                <TableCell className="text-white py-4">{trade.percentage}</TableCell>
                <TableCell className="text-white py-4">{trade.type}</TableCell>
                <TableCell className="text-white py-4">{trade.side}</TableCell>
                <TableCell className="text-white py-4">
                  <div className="flex flex-col">
                    <span>{trade.timestamps.split('T')[0]}</span>
                    <span className="text-sm text-gray-400">{trade.timestamps.split('T')[1]}</span>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default TradeLog;
