import React, { useEffect, useState } from 'react';
import InfoBar from './InfoBar';
import ComparisonTools from './ComparisonTools';
import MainChart from './MainChart';
import TimeHorizon from './TimeHorizon';
import { generateStockData } from './utils';
import { Time } from 'lightweight-charts';
import { ChartDataPoint } from './utils';
import { useChatContext } from '@/context/ChatContext';

interface ChartsTabProps {
  assetName: string;
  assetSymbol: string;
  availableAssets: string[];
  comparisonAssets: string[];
  activeTimeframe: string;
  onAddComparison: (asset: string) => void;
  onRemoveComparison: (asset: string) => void;
  onTimeframeChange: (value: string) => void;
}

const ChartsTab: React.FC<ChartsTabProps> = ({
  assetName,
  assetSymbol,
  availableAssets,
  comparisonAssets,
  activeTimeframe,
  onAddComparison,
  onRemoveComparison,
  onTimeframeChange
}) => {
  const { agent } = useChatContext();
  const [mainData, setMainData] = useState<ChartDataPoint[]>([]);
  const [comparisonData, setComparisonData] = useState<Record<string, { time: Time; value: number }[]>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Use agent's asset pair if available, otherwise fallback to assetSymbol
        const mainAssetSymbol = agent?.assetPair || assetSymbol;
        
        // Fetch main asset data
        const mainAssetData = await generateStockData(activeTimeframe, `${mainAssetSymbol}USDT`);
        setMainData(mainAssetData);

        // Fetch comparison assets data
        const comparisonDataPromises = comparisonAssets.map(async (asset) => {
          const data = await generateStockData(activeTimeframe, `${asset}USDT`);
          return [asset, data.map(d => ({ time: d.time, value: d.value }))];
        });

        const comparisonResults = await Promise.all(comparisonDataPromises);
        const newComparisonData = Object.fromEntries(comparisonResults);
        setComparisonData(newComparisonData);
      } catch (error) {
        console.error('Error fetching chart data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
    // Set up interval to refresh data every 10 seconds
    const interval = setInterval(fetchData, 10000);

    return () => clearInterval(interval);
  }, [agent?.assetPair, assetSymbol, comparisonAssets, activeTimeframe]);

  return (
    <div className="flex flex-col h-full relative">
      <InfoBar 
        assetName={agent?.assetPair || assetName} 
        assetSymbol={agent?.assetPair || assetSymbol} 
      />
      <ComparisonTools
        assetSymbol={agent?.assetPair || assetSymbol}
        availableAssets={availableAssets}
        comparisonAssets={comparisonAssets}
        onAddComparison={onAddComparison}
        onRemoveComparison={onRemoveComparison}
      />
      <div className="flex-1 relative flex flex-col">
        <div className="flex-1 relative">
          {loading ? (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <div className="text-white">Loading chart data...</div>
            </div>
          ) : (
            <MainChart 
              data={mainData}
              comparisons={comparisonData}
              onVisibleRangeChange={(from, to) => {
                // Optional: Load more data when user scrolls
              //  console.log('Visible range changed:', from, to);
              }}
            />
          )}
          <TimeHorizon 
            activeTimeframe={activeTimeframe} 
            onTimeframeChange={onTimeframeChange} 
          />
        </div>
      </div>
    </div>
  );
};

export default ChartsTab;
