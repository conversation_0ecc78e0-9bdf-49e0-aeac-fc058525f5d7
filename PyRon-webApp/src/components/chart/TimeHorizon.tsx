import React, { useEffect, useState } from 'react';
import { getPosition } from '@/lib/position';
import { useChatContext } from '@/context/ChatContext';

interface TimeHorizonProps {
  activeTimeframe: string;
  onTimeframeChange: (value: string) => void;
}

interface PositionData {
  positionSize: string;
  pnl: string;
  positionValueUsd: string;
  portfolioValue: string;
}

const TimeHorizon: React.FC<TimeHorizonProps> = ({
  activeTimeframe,
  onTimeframeChange
}) => {
  const { agent } = useChatContext();
  const [positionData, setPositionData] = useState<PositionData>({
    positionSize: "0",
    pnl: "0",
    positionValueUsd: "0",
    portfolioValue: "0"
  });

  useEffect(() => {
    const fetchPosition = async () => {
      if (agent?.botId) {
        try {
          const data = await getPosition(agent.botId);
          // Ensure all values are strings
          setPositionData({
            positionSize: data.positionSize,
            pnl: data.pnl,
            positionValueUsd: data.positionValueUsd.toString(),
            portfolioValue: data.portfolioValue
          });
        } catch (error) {
          console.error('Error fetching position:', error);
          // Reset position data on error
          setPositionData({
            positionSize: "0",
            pnl: "0",
            positionValueUsd: "0",
            portfolioValue: "0"
          });
        }
      } else {
        // Reset position data when no agent is selected
        setPositionData({
          positionSize: "0",
          pnl: "0",
          positionValueUsd: "0",
          portfolioValue: "0"
        });
      }
    };

    fetchPosition();
    // Set up interval to refresh position data every 10 seconds
    const interval = setInterval(fetchPosition, 10000);

    return () => clearInterval(interval);
  }, [agent?.botId]); // Update when agent.botId changes

  const timeframes = ["30m", "1h", "1d", "1w", "1M", "3M"];
  const assetPair = agent?.assetPair ? `${agent.assetPair}/USD` : 'SOL/USD';

  return (
    <div className="absolute top-2 left-0 right-0 z-10 flex items-center justify-between px-4 my-[9px]">
      <div className="flex items-center gap-2">
        <div className="flex items-center bg-[#222222] border border-gold-light/30 rounded-md px-2 py-1">
          <span className="text-gold-light font-medium text-xs">{assetPair}</span>
        </div>
        
        <div className="flex">
          {timeframes.map(frame => (
            <button 
              key={frame} 
              onClick={() => onTimeframeChange(frame)} 
              className={`px-3 py-1 text-xs ${activeTimeframe === frame ? 'bg-[#222222] text-gold-light' : 'bg-[#1a1a1a] text-gray-400'} border border-gray-700/30`}
            >
              {frame}
            </button>
          ))}
        </div>
      </div>
      
      <div className="flex">
        <div className="bg-[#222222] border border-gold-light/30 border-r-0 rounded-l-md px-3 py-1">
          <span className="text-gold-light text-xs">
            Position: {positionData.positionSize} {agent?.assetPair || 'SOL'}
          </span>
        </div>
        
        <div className="bg-[#222222] border-t border-b border-gold-light/30 px-3 py-1">
          <span className={`text-xs ${Number(positionData.pnl) >= 0 ? 'text-teal' : 'text-red-500'}`}>
            P&L: {positionData.pnl}%
          </span>
        </div>
        
        <div className="bg-[#222222] border border-gold-light/30 border-l-0 rounded-r-md px-3 py-1">
          <span className="text-gold-light text-xs">
            Value: ${positionData.positionValueUsd}
          </span>
        </div>
      </div>
    </div>
  );
};

export default TimeHorizon;