
import React from 'react';
import { FlaskConical, TestTube, Code, BookText } from "lucide-react";
import { TabConfig, TabData } from './types';

interface TabSelectorProps {
  tabs: TabData[];
  tabConfigs: TabConfig[];
  activeTab: string;
  setActiveTab: (tabId: string) => void;
  addTab: (tabId: string) => void;
  removeTab: (tabId: string, event: React.MouseEvent) => void;
}

const TabSelector: React.FC<TabSelectorProps> = ({
  tabs,
  tabConfigs,
  activeTab,
  setActiveTab,
  addTab,
  removeTab
}) => {
  // Brighter gold color (2% brighter than the previous one)
  const brightGoldColor = "#FFD635"; // Brightened from #FFD433
  
  return (
    <div className="absolute top-0 left-0 right-0 z-20 bg-[#1c1c1c] border-b border-gray-800/30">
      <div className="flex items-center">
        <button 
          onClick={() => addTab("charts")}
          className={`flex items-center gap-3 px-8 py-4 text-base transition-colors hover:bg-black/50 ${activeTab === "charts" ? 'text-white border-b-2 border-[#FFD635]' : 'text-white/60'}`}
        >
          <FlaskConical className="h-6 w-6" />
          Chart
        </button>
        
        <button 
          onClick={() => addTab("backtesting")}
          className={`flex items-center gap-3 px-8 py-4 text-base transition-colors hover:bg-black/50 ${activeTab === "backtesting" ? 'text-white border-b-2 border-[#FFD635]' : 'text-white/60'}`}
        >
          <TestTube className="h-6 w-6" />
          Hypothesise
        </button>
        
        <button 
          onClick={() => addTab("github")}
          className={`flex items-center gap-3 px-8 py-4 text-base transition-colors hover:bg-black/50 ${activeTab === "github" ? 'text-white border-b-2 border-[#FFD635]' : 'text-white/60'}`}
        >
          <Code className="h-6 w-6" />
          Code
        </button>
        
        <button 
          onClick={() => addTab("trade-log")}
          className={`flex items-center gap-3 px-8 py-4 text-base transition-colors hover:bg-black/50 ${activeTab === "trade-log" ? 'text-white border-b-2 border-[#FFD635]' : 'text-white/60'}`}
        >
          <BookText className="h-6 w-6" />
          Trade Log
        </button>
      </div>
    </div>
  );
};

export default TabSelector;
