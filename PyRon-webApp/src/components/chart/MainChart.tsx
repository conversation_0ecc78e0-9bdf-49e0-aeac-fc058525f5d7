import React, { useEffect, useRef } from 'react';
import {
  createChart,
  CandlestickSeries,
  LineSeries,
  ISeriesApi,
  IChartApi,
  Time,
  CandlestickData,
  LineData,
  WhitespaceData,
  ColorType,
} from 'lightweight-charts';

interface Candle {
  time: Time;
  open: number;
  high: number;
  low: number;
  close: number;
}

interface MainChartProps {
  /** Primary asset candles */
  data: Candle[];
  /** Map <symbol, candle array> for comparison lines */
  comparisons?: Record<string, { time: Time; value: number }[]>;
  /** Called when user scrolls or zooms (optional) */
  onVisibleRangeChange?: (from: number, to: number) => void;
}

const MainChart: React.FC<MainChartProps> = ({
  data,
  comparisons = {},
  onVisibleRangeChange,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const primarySeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const comparisonSeriesRef = useRef<Record<string, ISeriesApi<'Line'> | null>>(
    {},
  );

  // Create chart instance
  useEffect(() => {
    if (!containerRef.current) return;
    const chart = createChart(containerRef.current, {
      autoSize: true,
      layout: {
        background: { type: ColorType.Solid, color: 'transparent' },
        textColor: '#ccc',
        fontFamily: 'Inter, sans-serif',
      },
      grid: {
        vertLines: { color: 'rgba(255, 255, 255, 0.1)' },
        horzLines: { color: 'rgba(255, 255, 255, 0.1)' },
      },
      crosshair: {
        mode: 1,
        vertLine: {
          color: '#758696',
          style: 1,
          width: 1,
        },
        horzLine: {
          color: '#758696',
          style: 1,
          width: 1,
        },
      },
      timeScale: {
        rightOffset: 0,
        fixLeftEdge: true,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        tickMarkFormatter: (time: number) => {
          const date = new Date(time * 1000);
          const isToday = new Date().toDateString() === date.toDateString();
          const isThisYear = new Date().getFullYear() === date.getFullYear();
          if (isToday) {
            return date.toLocaleTimeString('en-US', { 
              hour: '2-digit', 
              minute: '2-digit',
              hour12: false 
            });
          }
          if (isThisYear) {
            return date.toLocaleDateString('en-US', { 
              month: 'short', 
              day: 'numeric' 
            });
          }
          return date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric',
            year: 'numeric'
          });
        },
      },
      rightPriceScale: {
        borderColor: 'rgba(255, 255, 255, 0.1)',
      },
      localization: {
        timeFormatter: (time: number) => {
          const date = new Date(time * 1000);
          if (date.getHours() !== 0 || date.getMinutes() !== 0) {
            return date.toLocaleString('en-US', {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            });
          }
          return date.toLocaleDateString('en-US', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
          });
        }
      }
    });
    chartRef.current = chart;

    const candlestick = chart.addSeries(CandlestickSeries, {
      upColor: '#26a69a',
      downColor: '#ef5350',
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
      borderVisible: false,
    });
    primarySeriesRef.current = candlestick;

    if (onVisibleRangeChange) {
      chart.timeScale().subscribeVisibleLogicalRangeChange(range => {
        if (range) onVisibleRangeChange(range.from, range.to);
      });
    }

    return () => chart.remove();
  }, []);

  // Update primary series data
  useEffect(() => {
    primarySeriesRef.current?.setData(data as CandlestickData[]);
  }, [data]);

  // Handle comparison series
  useEffect(() => {
    if (!chartRef.current) return;
    
    // Remove deleted comparisons
    Object.keys(comparisonSeriesRef.current).forEach(sym => {
      if (!(sym in comparisons)) {
        chartRef.current?.removeSeries(comparisonSeriesRef.current[sym]!);
        delete comparisonSeriesRef.current[sym];
      }
    });

    // Add or update active comparisons
    Object.entries(comparisons).forEach(([sym, seriesData], idx) => {
      if (!comparisonSeriesRef.current[sym]) {
        comparisonSeriesRef.current[sym] = chartRef.current!.addSeries(LineSeries, {
          color: `hsl(${(idx * 57) % 360} 70% 50%)`,
          lineWidth: 2,
          priceLineVisible: false,
        });
      }
      comparisonSeriesRef.current[sym]!.setData(seriesData as LineData[]);
    });
  }, [comparisons]);

  return <div ref={containerRef} className="w-full h-full" />;
};

export default MainChart;
