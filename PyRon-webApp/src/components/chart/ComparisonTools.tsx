import React, { useState } from 'react';
interface ComparisonToolsProps {
  assetSymbol: string;
  availableAssets: string[];
  comparisonAssets: string[];
  onAddComparison: (asset: string) => void;
  onRemoveComparison: (asset: string) => void;
}
const ComparisonTools: React.FC<ComparisonToolsProps> = ({
  assetSymbol,
  availableAssets,
  comparisonAssets,
  onAddComparison,
  onRemoveComparison
}) => {
  const [inputValue, setInputValue] = useState('');
  const handleAddComparison = () => {
    if (inputValue && !comparisonAssets.includes(inputValue) && availableAssets.includes(inputValue)) {
      onAddComparison(inputValue);
      setInputValue('');
    }
  };
  return <>
      <div className="absolute top-16 left-4 z-10">
        
      </div>
      
      {comparisonAssets.length > 0 && <div className="absolute top-16 right-4 flex flex-wrap gap-1 max-w-[180px] justify-end z-10">
          {comparisonAssets.map(asset => <div key={asset} className="bg-[#222] border border-gray-700/30 px-2 py-1 rounded-md flex items-center gap-1">
              <span className="text-xs text-gray-300">{asset}</span>
              <button onClick={() => onRemoveComparison(asset)} className="text-gray-400 hover:text-gray-200">
                ×
              </button>
            </div>)}
        </div>}
    </>;
};
export default ComparisonTools;