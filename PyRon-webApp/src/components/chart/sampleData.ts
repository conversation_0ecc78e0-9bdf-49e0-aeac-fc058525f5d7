
import { Trade } from './types';

export const sampleTrades: Trade[] = [
  {
    id: '1',
    date: '2023-05-15 14:32',
    type: 'BUY',
    asset: 'SOL',
    amount: 10,
    price: 135.45,
    hypothesis: "Solana's network metrics show increased adoption with daily active addresses up 15% over the last week. Combined with improved developer activity and reduced network outages, this indicates a potential bull run. Moving average convergence suggests price strength in the mid-term.",
    market: 'SOL-PERP',
    size: '10 SOL',
    notional: '$1354.50',
    fee: '0.003386 USDC'
  },
  {
    id: '2',
    date: '2023-05-18 09:15',
    type: 'SELL',
    asset: 'SOL',
    amount: 5,
    price: 142.30,
    profitLoss: 34.25,
    hypothesis: "Taking partial profits after hitting resistance at $142. Volume profile shows significant selling pressure at this level. RSI reaching overbought territory on 4H chart.",
    market: 'SOL-PERP',
    size: '5 SOL',
    notional: '$711.50',
    fee: '0.001779 USDC'
  },
  {
    id: '3',
    date: '2023-05-25 11:05',
    type: 'BUY',
    asset: 'SOL',
    amount: 7.5,
    price: 138.20,
    hypothesis: "Price found support at 200-day moving average. Accumulation pattern forming with declining volume during consolidation phase. Market sentiment analysis shows extreme fear, historically a good contrarian buy signal.",
    market: 'SOL-PERP',
    size: '7.5 SOL',
    notional: '$1036.50',
    fee: '0.002591 USDC'
  },
  {
    id: '4',
    date: '2023-06-02 15:45',
    type: 'SELL',
    asset: 'SOL',
    amount: 12.5,
    price: 149.75,
    profitLoss: 152.80,
    hypothesis: "Bearish divergence appearing on daily chart with lower highs in RSI while price makes higher highs. Profit taking at key Fibonacci extension level. Current macro environment suggests potential short-term market correction.",
    market: 'SOL-PERP',
    size: '12.5 SOL',
    notional: '$1871.88',
    fee: '0.004680 USDC'
  }
];
