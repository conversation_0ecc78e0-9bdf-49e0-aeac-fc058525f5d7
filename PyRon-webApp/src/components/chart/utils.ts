import { Time } from 'lightweight-charts';
import axios from 'axios';

export interface ChartDataPoint {
  time: Time;
  open: number;
  close: number;
  high: number;
  low: number;
  value: number;
  volume: number;
  isUp: boolean;
}

/**
 * Generic Binance fetcher
 */
async function fetchBinanceKlines(
  symbol: string,
  interval: string,
  limit: number
): Promise<ChartDataPoint[]> {
  try {
    const { data } = await axios.get('https://api.binance.com/api/v3/klines', {
      params: { symbol: symbol.toUpperCase(), interval, limit }
    });

    // de-duplicate by open-time and sort
    const unique = new Map<number, ChartDataPoint>();
    data.forEach((k: any[]) => {
      const ts = k[0];                     // open-time in ms
      if (!unique.has(ts)) {
        unique.set(ts, {
          time: Math.floor(ts / 1000) as Time, // convert to seconds
          open: +k[1],
          high: +k[2],
          low: +k[3],
          close: +k[4],
          value: +k[4],
          volume: +k[5],
          isUp: +k[4] > +k[1]
        });
      }
    });

    return [...unique.values()].sort((a, b) => (a.time as number) - (b.time as number));
  } catch (err) {
    console.error('Binance fetch failed:', err);
    return []; // fall back handled by caller
  }
}

/**
 * Public helper that the chart component calls.
 * @param tf   user-facing time-frame string
 * @param sym  trading pair (default SOLUSDT)
 */
export async function generateStockData(
  tf: string = '1d',
  sym: string = 'SOLUSDT'
): Promise<ChartDataPoint[]> {
  // unified mapping
  const settings: Record<string, { interval: string; limit: number }> = {
    '30m': { interval: '30m', limit: 300 },  // ≈ 6 days of 30-minute bars
    '1h': { interval: '1h', limit: 200 },    // ≈ 8 days of hourly bars
    '1d': { interval: '1d', limit: 365 },    // 1 year of daily bars
    '1day': { interval: '1d', limit: 365 },  // alias for 1d
    '1w': { interval: '1w', limit: 52 },     // 1 year of weekly bars
    '1week': { interval: '1w', limit: 52 },  // alias for 1w
    '1M': { interval: '1M', limit: 36 },     // 3 years of monthly bars
    '1month': { interval: '1M', limit: 36 }, // alias for 1M
    '3M': { interval: '1d', limit: 90 },     // 3 months of daily bars
    '3months': { interval: '1d', limit: 90 } // alias for 3M
  };

  const key = tf.trim();
  const fallback = await generateFallbackData(tf);

  if (!settings[key]) return fallback;

  const { interval, limit } = settings[key];
  const data = await fetchBinanceKlines(sym, interval, limit);
  return data.length ? data : fallback;
}

// Keep the existing generateFallbackData function
function generateFallbackData(timeframe: string = '1D'): ChartDataPoint[] {
  const data: ChartDataPoint[] = [];
  const now = new Date();
  let price = 125; // Starting price for SOL
  
  // Generate data points based on timeframe
  const points = timeframe === '1D' ? 100 : timeframe === '1W' ? 52 : timeframe === '1M' ? 30 : 365;
  
  for (let i = 0; i < points; i++) {
    // Create realistic price movements
    const change = (Math.random() - 0.5) * 8;
    price = Math.max(105, price + change);
    
    const open = price;
    const close = price + (Math.random() - 0.5) * 4;
    const high = Math.max(open, close) + Math.random() * 3;
    const low = Math.min(open, close) - Math.random() * 3;
    const volume = Math.random() * 10;
    
    // Calculate timestamp based on timeframe
    const timestamp = new Date(now);
    if (timeframe === '1D') {
      timestamp.setDate(timestamp.getDate() - (points - i));
    } else if (timeframe === '1W') {
      timestamp.setDate(timestamp.getDate() - (points - i) * 7);
    } else if (timeframe === '1M') {
      timestamp.setDate(timestamp.getDate() - (points - i));
    } else {
      timestamp.setDate(timestamp.getDate() - (points - i));
    }
    
    // Format time as YYYY-MM-DD
    const timeStr = timestamp.toISOString().split('T')[0];
    
    data.push({
      time: timeStr as Time,
      open,
      close,
      high,
      low,
      value: close, // For line series
      volume,
      isUp: close > open
    });
  }
  
  return data;
}
