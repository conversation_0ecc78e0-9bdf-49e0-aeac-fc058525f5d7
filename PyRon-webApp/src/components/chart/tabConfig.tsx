
import React from 'react';
import { Github, Terminal, BarChart3, GitGraph, TestTube, FlaskConical, BookText, Activity, Code } from "lucide-react";
import { TabConfig } from './types';

// Tab configuration with icons and labels
export const tabConfigs: TabConfig[] = [
  { id: "github", label: "Code", icon: <Code className="h-4 w-4" /> },
  { id: "ssh", label: "SSH", icon: <Terminal className="h-4 w-4" /> },
  { id: "charts", label: "Chart", icon: <BarChart3 className="h-4 w-4" /> },
  { id: "causal-factor-graphs", label: "Causal Factor Graphs", icon: <GitGraph className="h-4 w-4" /> },
  { id: "backtesting", label: "Backtest", icon: <TestTube className="h-4 w-4" /> },
  { id: "simulation", label: "Simulate", icon: <FlaskConical className="h-4 w-4" /> },
  { id: "trade-log", label: "Trade Logs", icon: <BookText className="h-4 w-4" /> },
  { id: "performance", label: "Performance", icon: <Activity className="h-4 w-4" /> },
];
