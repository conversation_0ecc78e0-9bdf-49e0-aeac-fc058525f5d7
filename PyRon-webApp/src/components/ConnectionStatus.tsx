
import React from 'react';
import { useNetworkStatus } from '@/context/NetworkStatusProvider';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const ConnectionStatus: React.FC = () => {
  const { isOnline, retryConnection } = useNetworkStatus();

  if (isOnline) {
    return null; // Don't show anything when online
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="fixed bottom-4 right-4 flex items-center gap-2 bg-black/80 text-white text-xs py-2 px-3 rounded-md border border-red-500/50 z-50">
            <AlertCircle size={14} className="text-red-500" />
            <span>Server connection offline</span>
            <button 
              onClick={retryConnection}
              className="ml-2 p-1 hover:bg-gray-700 rounded-full"
            >
              <RefreshCw size={12} className="text-white" />
            </button>
          </div>
        </TooltipTrigger>
        <TooltipContent side="top">
          <p>Network connection issue. Click to retry.</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default ConnectionStatus;
