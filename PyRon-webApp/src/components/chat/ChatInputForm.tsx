
import React, { useState } from 'react';
import { Send } from 'lucide-react';

interface ChatInputFormProps {
  onSendMessage: (message: string, deepHypothesis: boolean) => void;
  isLoading: boolean;
  disabled?: boolean;
}

const ChatInputForm = ({
  onSendMessage,
  isLoading,
  disabled = false
}: ChatInputFormProps) => {
  const [message, setMessage] = useState('');
  const [wideSearch, setWideSearch] = useState(false);
  const [deepHypothesis, setDeepHypothesis] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || disabled) return;
    onSendMessage(message.trim(), deepHypothesis);
    setMessage('');
  };

  const isInputDisabled = isLoading || disabled;

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <input
        type="text"
        value={message}
        onChange={e => setMessage(e.target.value)}
        placeholder={disabled ? "Connect wallet to chat..." : "Type your VibeTrade..."}
        disabled={isInputDisabled}
        className="w-full bg-[#1a1a1a] border border-gray-800/30 rounded-md px-4 py-2.5 outline-none text-white text-sm"
      />

      <div className="flex gap-2">
        <button
          type="button"
          onClick={() => setWideSearch(!wideSearch)}
          disabled={isInputDisabled}
          className={`flex-1 py-2 text-sm rounded-md ${
            wideSearch
              ? 'bg-black/50 border border-[#FFD433] text-[#FFD433]'
              : isInputDisabled
                ? 'border border-gray-800/30 bg-[#1a1a1a] text-gray-600 cursor-not-allowed'
                : 'border border-gold-light/30 bg-[#222222] text-white hover:border-[#FFD433]/50 hover:text-[#FFD433]'
          }`}
        >
          Wide-Search
        </button>

        <button
          type="button"
          onClick={() => setDeepHypothesis(!deepHypothesis)}
          disabled={isInputDisabled}
          className={`flex-1 py-2 text-sm rounded-md ${
            deepHypothesis
              ? 'bg-black/50 border border-[#FFD433] text-[#FFD433]'
              : isInputDisabled
                ? 'border border-gray-800/30 bg-[#1a1a1a] text-gray-600 cursor-not-allowed'
                : 'border border-gold-light/30 bg-[#222222] text-white hover:border-[#FFD433]/50 hover:text-[#FFD433]'
          }`}
        >
          Deep-Hypothesis
        </button>

        <button
          type="submit"
          disabled={isInputDisabled || !message.trim()}
          className={`px-4 rounded-md border ${
            isInputDisabled || !message.trim()
              ? 'text-gray-500 border-gray-800/30 cursor-not-allowed'
              : 'text-gold-light border-gold-light/30 hover:bg-black/40'
          }`}
        >
          <Send size={18} />
        </button>
      </div>

      <div className="text-[10px] text-white/60 text-center">
        AI may provide inaccurate information. Verify before making financial decisions
      </div>
    </form>
  );
};

export default ChatInputForm;
