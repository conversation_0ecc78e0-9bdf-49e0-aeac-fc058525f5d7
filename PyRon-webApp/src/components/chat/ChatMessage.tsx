import React from 'react';
import { User, Bo<PERSON> } from 'lucide-react';

interface ChatMessageProps {
  message: {
    type: 'user' | 'ai';
    content: string;
  };
}

const ChatMessage = ({ message }: ChatMessageProps) => {
  const isUser = message.type === 'user';
  
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} group`}>
      <div className={`
        flex gap-2 max-w-[85%] rounded-lg px-3 py-2.5 
        ${isUser 
          ? 'bg-gold-dark/20 text-white border border-gold-light/20' 
          : 'bg-[#222222] border border-[#333333]'
        }
      `}>
        {!isUser && (
          <div className="flex-shrink-0 pt-0.5">
            <Bot size={14} className="text-gold-light" />
          </div>
        )}
        
        <div className="text-xs">
          {message.content}
        </div>
        
        {isUser && (
          <div className="flex-shrink-0 pt-0.5">
            <User size={14} className="text-gold-light" />
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
