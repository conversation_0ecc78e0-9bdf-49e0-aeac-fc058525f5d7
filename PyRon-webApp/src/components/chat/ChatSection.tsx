import React, { useRef, useEffect } from 'react';
import { ChatMessage as ChatMessageType } from '@/types/chat';
import ChatMessage from './ChatMessage';

interface ChatSectionProps {
  chatHistory: ChatMessageType[];
  isLoading: boolean;
}

const ChatSection = ({ chatHistory = [], isLoading }: ChatSectionProps) => {
  const chatEndRef = useRef<HTMLDivElement>(null);
  console.log('chatHistory', chatHistory);

  useEffect(() => {
    // Scroll to bottom of chat when messages change
    chatEndRef.current?.scrollIntoView({
      behavior: 'smooth'
    });
  }, [chatHistory]);

  const loadingDotStyle = {
    display: 'inline-block',
    width: '3px',
    height: '3px',
    margin: '0 1px',
    backgroundColor: 'rgb(209 166 43 / var(--tw-text-opacity, 1))',
    borderRadius: '50%',
    animation: 'bounce 0.6s infinite alternate',
  };

  const loadingContainerStyle = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '40px',
  };

  const keyframes = `
    @keyframes bounce {
      from {
        transform: translateY(0);
      }
      to {
        transform: translateY(-10px);
      }
    }
  `;

  return (
    <>
      <style>{keyframes}</style>
      <div className="py-4 px-4 space-y-4">
        {chatHistory.length === 0 ? (
          <div className="flex items-center justify-center h-full opacity-60">
            <p className="text-gray-400 text-center text-sm">
              Ask questions about Solana trading strategies, market analysis, or technical indicators.
            </p>
          </div>
        ) : (
          chatHistory.map((chat, index) => (
            <ChatMessage key={index} message={chat} />
          ))
        )}
        {isLoading && (
          <div style={loadingContainerStyle}>
            <div>
              <span style={{ ...loadingDotStyle, animationDelay: '0s' }}></span>
              <span style={{ ...loadingDotStyle, animationDelay: '0.2s' }}></span>
              <span style={{ ...loadingDotStyle, animationDelay: '0.4s' }}></span>
            </div>
          </div>
        )}
        <div ref={chatEndRef} />
      </div>
    </>
  );
};

export default ChatSection;
