
import React from "react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Plus } from "lucide-react";

const menuItems = [
  { id: "trade-log", label: "Trade Log" },
  { id: "backtest", label: "Backtest" },
  { id: "simulation", label: "Simulation" },
  { id: "charts", label: "Charts" },
  { id: "code", label: "Code" },
];

export function NavDropdown() {
  const handleMenuItemClick = (id: string) => {
    // This function will handle opening a new tab with the relevant information
    console.log(`Opening ${id}`);
    // For now, we're just logging the action
    // In a real implementation, you would add navigation or tab creation logic here
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <div className="flex h-10 w-10 items-center justify-center rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground cursor-pointer">
          <Plus className="h-4 w-4" />
          <span className="sr-only">Add new item</span>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {menuItems.map((item) => (
          <DropdownMenuItem 
            key={item.id}
            onClick={() => handleMenuItemClick(item.id)}
            className="cursor-pointer"
          >
            {item.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
