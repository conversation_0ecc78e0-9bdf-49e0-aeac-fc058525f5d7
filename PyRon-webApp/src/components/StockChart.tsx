import React, { useState } from 'react';
import { BarChart3, TestTube, Code, BookText } from "lucide-react";
import TradeLog from './chart/TradeLog';
import ChartsTab from './chart/ChartsTab';
import TabSelector from './chart/TabSelector';
import { TabData } from './chart/types';
import { tabConfigs } from './chart/tabConfig';
import { sampleTrades } from './chart/sampleData';
import InfoBar from './chart/InfoBar';
import BacktestingTab from './chart/BacktestingTab';
import { useWalletStore } from '@/store/walletStore';
import { useChatContext } from '@/context/ChatContext';

const StockChart = () => {
  // Example: Get wallet connection status from a store or context
  const isWalletConnected = useWalletStore(state => state.isConnected);

  // Get the current chat ID from the chat context
  const { currentChatId } = useChatContext();

  // Asset is fixed to Solana
  const assetName = "Solana";
  const assetSymbol = "SOL";

  // State for tabs
  const [tabs, setTabs] = useState<TabData[]>([{
    id: "charts",
    label: "Chart",
    icon: <BarChart3 className="h-5 w-5" />,
    comparisonAssets: [],
    activeTimeframe: "1D"
  }]);
  const [activeTab, setActiveTab] = useState("charts");

  // Available comparison assets
  const availableAssets = ["BTC", "ETH", "ADA", "DOT", "AVAX"];

  // Get current tab data
  const currentTab = tabs.find(tab => tab.id === activeTab) || tabs[0];

  // Handle adding comparison asset
  const handleAddComparison = (asset: string) => {
    if (!currentTab.comparisonAssets.includes(asset) && availableAssets.includes(asset)) {
      setTabs(prevTabs => prevTabs.map(tab => tab.id === activeTab ? {
        ...tab,
        comparisonAssets: [...tab.comparisonAssets, asset]
      } : tab));
    }
  };

  // Handle removing comparison asset
  const handleRemoveComparison = (asset: string) => {
    setTabs(prevTabs => prevTabs.map(tab => tab.id === activeTab ? {
      ...tab,
      comparisonAssets: tab.comparisonAssets.filter(a => a !== asset)
    } : tab));
  };

  // Handle timeframe change
  const handleTimeframeChange = (value: string) => {
    setTabs(prevTabs => prevTabs.map(tab => tab.id === activeTab ? {
      ...tab,
      activeTimeframe: value
    } : tab));
  };

  // Add a new tab
  const addTab = (tabId: string) => {
    const tabConfig = tabConfigs.find(config => config.id === tabId);
    if (tabConfig && !tabs.some(tab => tab.id === tabId)) {
      const newTab: TabData = {
        id: tabId,
        label: tabConfig.label,
        icon: tabConfig.icon,
        comparisonAssets: [],
        activeTimeframe: "1D"
      };
      setTabs(prevTabs => [...prevTabs, newTab]);
      setActiveTab(tabId);
    } else if (tabs.some(tab => tab.id === tabId)) {
      // If tab already exists, just switch to it
      setActiveTab(tabId);
    }
  };

  // Remove a tab
  const removeTab = (tabId: string, event: React.MouseEvent) => {
    event.stopPropagation();

    // Don't remove the last tab
    if (tabs.length <= 1) return;
    const newTabs = tabs.filter(tab => tab.id !== tabId);
    setTabs(newTabs);

    // If removing the active tab, set another one as active
    if (activeTab === tabId) {
      setActiveTab(newTabs[0].id);
    }
  };

  // Render the appropriate content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case "trade-log":
        return <div className="h-full flex flex-col">
            <div className="flex-shrink-0">
              <InfoBar assetName={assetName} assetSymbol={assetSymbol} isTradeLog={true} />
            </div>
            <div className="flex-1 min-h-0">
              <TradeLog chatId={currentChatId} isWalletConnected={isWalletConnected} />
            </div>
          </div>;
      case "github":
        return <div className="flex items-center justify-center h-full bg-black/50">
            <p className="text-gray-400 text-base">Coming Soon</p>
          </div>;
      case "backtesting":
        return <BacktestingTab chatId={currentChatId} isWalletConnected={isWalletConnected} />;
      case "simulation":
        return <div className="flex items-center justify-center h-full bg-black/50">
            <p className="text-gray-400 text-base">Simulation Environment</p>
          </div>;
      case "performance":
        return <div className="flex items-center justify-center h-full bg-black/50">
            <p className="text-gray-400 text-base">Performance Metrics</p>
          </div>;
      case "charts":
      default:
        return <ChartsTab assetName={assetName} assetSymbol={assetSymbol} availableAssets={availableAssets} comparisonAssets={currentTab.comparisonAssets} activeTimeframe={currentTab.activeTimeframe} onAddComparison={handleAddComparison} onRemoveComparison={handleRemoveComparison} onTimeframeChange={handleTimeframeChange} />;
    }
  };
  return <div className="h-full bg-[#1a1a1a]/90 relative text-sm flex flex-col">
      {/* Tab bar - fixed height */}
      <div className="flex-shrink-0">
        <TabSelector tabs={tabs} tabConfigs={tabConfigs} activeTab={activeTab} setActiveTab={setActiveTab} addTab={addTab} removeTab={removeTab} />
      </div>

      {/* Render the appropriate content based on active tab - takes remaining space */}
      <div className="flex-1 min-h-0">
        {renderTabContent()}
      </div>
    </div>;
};
export default StockChart;