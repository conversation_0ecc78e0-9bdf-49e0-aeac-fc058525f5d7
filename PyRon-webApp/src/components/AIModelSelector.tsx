import React from 'react';
import { useAIModelStore, AIModelType } from '@/store/aiModelStore';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Settings } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';

const AIModelSelector: React.FC = () => {
  const {
    currentModel,
    setCurrentModel,
    openaiApiKey,
    geminiApiKey,
    setOpenAIKey,
    setGeminiKey
  } = useAIModelStore();

  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [openaiKeyInput, setOpenaiKeyInput] = React.useState(openaiApiKey || '');
  const [geminiKeyInput, setGeminiKeyInput] = React.useState(geminiApiKey || '');

  const handleModelToggle = (checked: boolean) => {
    setCurrentModel(checked ? 'gemini' : 'gpt');
  };

  const handleSaveKeys = () => {
    if (openaiKeyInput.trim()) {
      setOpenAIKey(openaiKeyInput.trim());
    }

    if (geminiKeyInput.trim()) {
      setGeminiKey(geminiKeyInput.trim());
    }

    setIsDialogOpen(false);
  };

  return (
    <>
      <div className="flex items-center space-x-2 bg-[#222222] p-2 rounded-md">
        <Label htmlFor="model-toggle" className="text-sm text-gray-400">
          GPT
        </Label>
        <Switch
          id="model-toggle"
          checked={currentModel === 'gemini'}
          onCheckedChange={handleModelToggle}
        />
        <Label htmlFor="model-toggle" className="text-sm text-gray-400">
          Gemini
        </Label>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="ghost" size="icon" className="ml-2">
              <Settings size={16} />
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-[#222222] text-white border-[#333333]">
            <DialogHeader>
              <DialogTitle>AI Model Settings</DialogTitle>
              <DialogDescription className="text-gray-400">
                Configure your API keys for different AI models
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="openai-key" className="text-white">OpenAI API Key</Label>
                <Input
                  id="openai-key"
                  type="password"
                  placeholder="sk-..."
                  value={openaiKeyInput}
                  onChange={(e) => setOpenaiKeyInput(e.target.value)}
                  className="bg-[#333333] border-[#444444] text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gemini-key" className="text-white">Gemini API Key</Label>
                <Input
                  id="gemini-key"
                  type="password"
                  placeholder="AI..."
                  value={geminiKeyInput}
                  onChange={(e) => setGeminiKeyInput(e.target.value)}
                  className="bg-[#333333] border-[#444444] text-white"
                />
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                onClick={handleSaveKeys}
                className="bg-[#FFD433] text-black hover:bg-[#FFD433]/90"
              >
                Save
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};

export default AIModelSelector;
