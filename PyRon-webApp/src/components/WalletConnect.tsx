import React, { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Wallet, Plus } from 'lucide-react';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger, 
} from '@/components/ui/dropdown-menu';
import { useWalletStore } from '@/store/walletStore';
import { 
  isPhantomInstalled, 
  connectPhantomWallet, 
  disconnectPhantomWallet 
} from '@/utils/phantomWallet';
import PhantomPrompt from './PhantomPrompt';
import { fetchUserPositions, fetchUserActivities } from '@/lib/trade/drift';

const WalletConnect = () => {
  const [activeTab, setActiveTab] = useState<'portfolio' | 'activity'>('activity');
  const [isPhantomAvailable, setIsPhantomAvailable] = useState(false);
  const [showPhantomPrompt, setShowPhantomPrompt] = useState(false);
  const { toast } = useToast();
  const { isConnected, walletAddress, connectWallet, disconnectWallet } = useWalletStore();
  const [positions, setPositions] = useState<{ market: string; position: number }[]>([]);
  const [activities, setActivities] = useState<Record<string, any>>({});
    const [expandedAgents, setExpandedAgents] = useState<Set<string>>(new Set());

  useEffect(() => {
    // Check if Phantom wallet is installed
    setIsPhantomAvailable(isPhantomInstalled());
  }, []);

  useEffect(() => {
    const updateData = async () => {
      if (isConnected && walletAddress) {
        const fetchedPositions = await fetchUserPositions(walletAddress);
        setPositions(fetchedPositions);

        const fetchedActivities = await fetchUserActivities(walletAddress);
        setActivities(fetchedActivities);
      }
    };

    updateData();
  }, [isConnected, walletAddress]);

  const handleConnect = async () => {
    if (!isConnected) {
      if (!isPhantomAvailable) {
        // Show Phantom installation prompt
        setShowPhantomPrompt(true);
        return;
      }
      
      // Connect to Phantom wallet
      const walletData = await connectPhantomWallet();
      
      if (walletData) {
        // Store wallet address in the store
        connectWallet(walletData.address);
        console.log('Wallet connected:', walletData.address, walletAddress);
        // Store the displayed value for a consistent UX
        localStorage.setItem('wallet_display', walletData.display);
        
        toast({
          title: "Wallet connected",
          description: "Your Phantom wallet has been connected successfully. You can now access your chats."
        });
      } else {
        toast({
          title: "Connection failed",
          description: "Failed to connect to Phantom wallet",
          variant: "destructive"
        });
      }
    }
  };

  const handleDisconnect = async () => {
    // Disconnect from Phantom wallet
    const success = await disconnectPhantomWallet();
    
    if (success) {
      // Update wallet store
      disconnectWallet();
      localStorage.removeItem('wallet_display');
      
      toast({
        title: "Wallet disconnected",
        description: "Your Phantom wallet has been disconnected. Connect a different wallet to access its chats."
      });
    } else {
      toast({
        title: "Disconnection failed",
        description: "Failed to disconnect from Phantom wallet",
        variant: "destructive"
      });
    }
  };

  // Get the display address from localStorage for a consistent UX
  const displayWalletAddress = localStorage.getItem('wallet_display') || 'Wallet';

  const toggleAgentExpansion = (agentName: string) => {
    setExpandedAgents((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(agentName)) {
        newSet.delete(agentName);
      } else {
        newSet.add(agentName);
      }
      return newSet;
    });
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button 
            onClick={!isConnected ? handleConnect : undefined} 
            className="border border-gray-700/30 bg-[#262626] hover:bg-[#373737] px-5 text-white text-sm rounded-full h-9 flex-shrink-0 whitespace-nowrap flex items-center gap-2"
          >
            <Wallet className="h-4 w-4" />
            {isConnected  ? displayWalletAddress : isPhantomAvailable ? "Connect Phantom" : "Install Phantom"}
          </button>
        </DropdownMenuTrigger>
        
        {isConnected  && (
          <DropdownMenuContent 
            align="end" 
            className="w-[360px] p-0 bg-[#1e1e1e] border border-gold-light/50 rounded-xl overflow-hidden"
          >
            {/* Tabs */}
            <div className="flex w-full">
              <button 
                onClick={() => setActiveTab('portfolio')}
                className={`flex-1 py-3 px-4 text-base font-medium ${activeTab === 'portfolio' ? 'bg-[#FFD837] text-black' : 'bg-transparent text-gray-400'}`}
              >
                Portfolio
              </button>
              <button 
                onClick={() => setActiveTab('activity')}
                className={`flex-1 py-3 px-4 text-base font-medium ${activeTab === 'activity' ? 'bg-[#FFD837] text-black' : 'bg-transparent text-gray-400'}`}
              >
                Activity
              </button>
            </div>
            
            {/* Tab content with scroll */}
            <div className="p-4 max-h-64 overflow-y-auto">
              {activeTab === 'portfolio' ? (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-white">Your Portfolio</h3>
                  
                  {/* Dynamic Portfolio content */}
                  {positions.map((position) => (
                    <div key={position.market} className="flex items-center justify-between bg-[#262626] p-3 rounded-lg mb-3">
                      <span className="text-white text-base font-medium">{position.market}</span>
                      <span className="text-white text-base font-medium">{position.position}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-white">Activity History</h3>
                  
                  {/* Dynamic Activity content */}
                  {Object.entries(activities).map(([agentId, agentData]) => (
                    <div key={agentId} className="bg-[#262626] p-3 rounded-lg mb-3">
                      <div className="flex justify-between items-center">
                        <span className="text-white text-base font-medium">{agentData.agentName}</span>
                        <button onClick={() => toggleAgentExpansion(agentData.agentName)} className="text-white">
                          <Plus size={18} />
                        </button>
                      </div>
                      {expandedAgents.has(agentData.agentName) && (
                        <div className="mt-2 space-y-2">
                          {agentData.orders.records.map((record: any) => (
                            <div key={record.txSig} className="bg-[#1e1e1e] p-2 rounded-md flex flex-col">
                              <div className="flex justify-between items-center">
                                <span className="text-white text-sm font-semibold">{record.symbol}</span>
                                <span className="text-white text-sm">{record.takerOrderDirection}</span>
                                <span className="text-white text-sm">{parseFloat(record.baseAssetAmountFilled).toFixed(4)}</span>
                              </div>
                              <div className="flex justify-between items-center mt-1">
                                <span className="text-gray-400 text-xs">{new Date(record.ts * 1000).toLocaleString()}</span>
                                <a href={`https://explorer.solana.com/tx/${record.txSig}`} target="_blank" rel="noopener noreferrer" className="text-blue-500 text-xs">
                                  {record.txSig.slice(0, 8)}...
                                </a>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* Disconnect button */}
            <div className="border-t border-gray-700/30 p-3">
              <button 
                onClick={handleDisconnect}
                className="w-full bg-[#262626] hover:bg-[#373737] text-white py-2 rounded-lg transition-colors text-sm"
              >
                Disconnect Wallet
              </button>
            </div>
          </DropdownMenuContent>
        )}
      </DropdownMenu>
      
      {/* Phantom Wallet Install Prompt */}
      {showPhantomPrompt && (
        <PhantomPrompt onClose={() => setShowPhantomPrompt(false)} />
      )}
    </>
  );
};

export default WalletConnect;
