
import React from 'react';
import { Wallet } from 'lucide-react';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

interface PhantomPromptProps {
  onClose: () => void;
}

const PhantomPrompt: React.FC<PhantomPromptProps> = ({ onClose }) => {
  const handleInstallClick = () => {
    window.open('https://phantom.app/download', '_blank');
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-[#1a1a1a] border border-gold-light/30 rounded-xl max-w-md w-full p-6 relative">
        <button 
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white"
        >
          &times;
        </button>
        
        <div className="flex flex-col items-center text-center mb-6">
          <div className="bg-gold-dark/20 p-3 rounded-full mb-4">
            <Wallet className="h-8 w-8 text-gold-light" />
          </div>
          <h2 className="text-xl font-semibold text-white mb-2">Phantom Wallet Required</h2>
          <p className="text-gray-400 text-sm">
            Connect with Phantom wallet to access your personal chats and trading features.
          </p>
        </div>
        
        <Alert className="mb-6 bg-[#262626] border-gold-light/20">
          <AlertTitle className="text-gold-light">Why Phantom?</AlertTitle>
          <AlertDescription className="text-sm text-gray-300">
            Phantom is a secure crypto wallet built for Solana. It allows you to safely store assets, 
            interact with dApps, and access your personal data.
          </AlertDescription>
        </Alert>
        
        <div className="flex flex-col gap-3">
          <Button 
            className="w-full bg-gold-light hover:bg-gold-light/90 text-black" 
            onClick={handleInstallClick}
          >
            Install Phantom Wallet
          </Button>
          <Button 
            variant="outline" 
            className="w-full border-gray-700" 
            onClick={onClose}
          >
            Maybe Later
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PhantomPrompt;
