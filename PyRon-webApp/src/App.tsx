import { Toaster } from "@/components/ui/toaster";

import { getUserChats } from "./lib/chat";
import ChatInterface from './components/ChatInterface';
import SidebarMenu from './components/SidebarMenu';
import Header from './components/Header';

import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import { useState, useEffect } from "react";
import { useWalletStore, initializeWallet } from "./store/walletStore";
import { NetworkStatusProvider, useNetworkStatus } from "./context/NetworkStatusProvider";
import ConnectionStatus from "./components/ConnectionStatus";
import { isAuthenticated, authenticateWallet } from './services/authService';

const AppInitializer = () => {
  const { isConnected, walletAddress } = useWalletStore();

  // Initialize wallet and authentication
  useEffect(() => {
    const init = async () => {
      try {
        console.log('Starting app initialization');

        // First initialize wallet with a delay to ensure browser is ready
        await new Promise(resolve => setTimeout(resolve, 500));
        await initializeWallet();

        // Then check authentication status with retries
        const { isConnected, walletAddress } = useWalletStore.getState();
        console.log('Wallet state after initialization:', { isConnected, walletAddress });

        if (isConnected && walletAddress) {
          if (!isAuthenticated()) {
            console.log('Wallet connected but not authenticated, getting new token');

            // Try authentication with multiple attempts
            let authSuccess = false;
            for (let i = 0; i < 3; i++) {
              console.log(`Authentication attempt ${i + 1}/3`);
              authSuccess = await authenticateWallet();
              if (authSuccess) {
                console.log('Authentication successful');
                break;
              }
              // Wait before retrying
              await new Promise(resolve => setTimeout(resolve, 1000));
            }

            if (!authSuccess) {
              console.error('Failed to authenticate after multiple attempts');
            }
          } else {
            console.log('Already authenticated');
          }
        } else {
          console.log('Wallet not connected, skipping authentication');
        }
      } catch (error) {
        console.error("Failed to initialize:", error);
      }
    };

    init();
  }, []);

  const [chats, setChats] = useState([]);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);

  // When wallet changes or on initial load, ensure chats are properly assigned
  useEffect(() => {
    if (!walletAddress) return;

    let isMounted = true;

    const setupChats = async () => {
      try {
        if (!isMounted) return;

        const userChats = await getUserChats(walletAddress);
        setChats(userChats);

        if (userChats.length > 0 && !currentChatId) {
          setCurrentChatId(userChats[0]._id);
        } else if (currentChatId && !userChats.some(chat => chat._id === currentChatId)) {
          if (userChats.length > 0) {
            setCurrentChatId(userChats[0]._id);
          }
        }
      } catch (error) {
        console.error("Error setting up chats:", error);
      }
    };

    setupChats();

    return () => {
      isMounted = false;
    };
  }, [walletAddress, currentChatId]);

  return null;
};

const App = () => {
  const [queryClient] = useState(() => new QueryClient());
  const [hasError, setHasError] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error("Global error caught:", error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('error', handleError);
    };
  }, []);

  const handleRetry = () => {
    setHasError(false);
    window.location.reload();
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(prev => !prev);
  };

  if (hasError) {
    return (
      <div className="h-full w-full bg-background flex items-center justify-center">
        <div className="bg-[#1a1a1a] p-6 rounded-lg border border-gold-light/30 max-w-md w-full text-center">
          <h2 className="text-xl font-semibold text-white mb-4">Something went wrong</h2>
          <p className="text-gray-400 mb-6">
            We encountered an error loading the application. This might be due to network issues or service unavailability.
          </p>
          <button
            onClick={handleRetry}
            className="bg-gold-light hover:bg-gold-light/90 text-black px-4 py-2 rounded-md transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <NetworkStatusProvider>
        <TooltipProvider>
          <div className="h-full w-full bg-background">
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <AppInitializer />
              <Routes>
                <Route path="/" element={<Index />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
              {/* Add connection status indicator */}
              <ConnectionStatus />
            </BrowserRouter>
          </div>
        </TooltipProvider>
      </NetworkStatusProvider>
    </QueryClientProvider>
  );
};

export default App;
