// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://tkcfgqhksfqaprprrmcj.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRrY2ZncWhrc2ZxYXBycHJybWNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0NzE0MDEsImV4cCI6MjA1OTA0NzQwMX0._XNAZoBK5uuzQgyOOMUp0VQzuQMGyoRAXIXCVjS8iwQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);