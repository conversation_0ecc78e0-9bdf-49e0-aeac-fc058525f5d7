export interface Chat {
  _id: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  messages: ChatMessage[];
  walletAddress: string | null;
}

export interface ChatMessage {
  _id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

// Extend ChatMessage to include additional properties
export interface ExtendedChatMessage extends ChatMessage {
  status: 'pending' | 'sent' | 'failed';
  sender: 'user' | 'ai';
}
