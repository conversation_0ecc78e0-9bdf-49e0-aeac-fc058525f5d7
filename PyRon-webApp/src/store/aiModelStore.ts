import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type AIModelType = 'gpt' | 'gemini';

interface AIModelState {
  // Current model selection
  currentModel: AIModelType;
  
  // API keys
  openaiApiKey: string | null;
  geminiApiKey: string | null;
  
  // Actions
  setCurrentModel: (model: AIModelType) => void;
  setOpenAIKey: (key: string) => void;
  setGeminiKey: (key: string) => void;
  
  // Helper methods
  getCurrentApiKey: () => string | null;
}

export const useAIModelStore = create<AIModelState>()(
  persist(
    (set, get) => ({
      currentModel: 'gpt',
      openaiApiKey: localStorage.getItem('openai_api_key'),
      geminiApiKey: localStorage.getItem('gemini_api_key'),
      
      setCurrentModel: (model: AIModelType) => {
        set({ currentModel: model });
      },
      
      setOpenAIKey: (key: string) => {
        localStorage.setItem('openai_api_key', key);
        set({ openaiApiKey: key });
      },
      
      setGeminiKey: (key: string) => {
        localStorage.setItem('gemini_api_key', key);
        set({ geminiApiKey: key });
      },
      
      getCurrentApiKey: () => {
        const state = get();
        return state.currentModel === 'gpt' 
          ? state.openaiApiKey 
          : state.geminiApiKey;
      },
    }),
    {
      name: 'ai-model-storage',
      partialize: (state) => ({
        currentModel: state.currentModel,
        // We don't store API keys in persist storage as they're already in localStorage
      }),
    }
  )
);
