import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Connection } from '@solana/web3.js';
import { DriftClient } from '@drift-labs/sdk';
import { PhantomWalletAdapter } from '@solana/wallet-adapter-phantom';
import { createClient } from '@/lib/trade/drift';
import { Chat } from '@/types/chat';
import { authenticateWallet, logout } from '@/services/authService';

interface WalletState {
  isConnected: boolean;
  wallet: any | null; // Store the entire wallet object
  walletType: 'phantom' | null;
  connection: Connection | null;
  driftClient: DriftClient | null;
  walletAddress: string | null;

  // Add these properties to the WalletState interface
  currentChatId: string | null;
  agent: any | null;
  chats: Chat[];

  // Actions
  connectWallet: (address: string) => void;
  disconnectWallet: () => void;
  setWalletType: (type: 'phantom' | null) => void;
  resetChatState: () => void;
  setDriftClient: (client: DriftClient) => void;
}

export const useWalletStore = create<WalletState>()(
  persist(
    (set, get) => ({
      isConnected: false,
      wallet: null,
      walletType: null,
      connection: null,
      driftClient: null,
      walletAddress: null,
      currentChatId: null,
      agent: null,
      chats: [],

      connectWallet: async (address: string) => {
        try {
          const connection = new Connection(import.meta.env.VITE_RPC_URL, "confirmed");
          console.log("Connection:", connection);

          // Create a new PhantomWalletAdapter instance
          const walletAdapter = new PhantomWalletAdapter();

          // Check if already connected
          if (!walletAdapter.connected) {
            // Connect to the wallet
            await walletAdapter.connect();
          }

          if (!walletAdapter.publicKey) {
            console.error("Failed to connect wallet: No public key available");
            return;
          }

          // Set the wallet connection state immediately
          set({
            isConnected: true,
            wallet: walletAdapter,
            walletType: 'phantom',
            connection: connection,
            walletAddress: walletAdapter.publicKey.toString(),
          });

          // Add a small delay to ensure wallet is fully initialized before authentication
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Get JWT token after wallet connection with retries
          let authSuccess = false;
          for (let i = 0; i < 3; i++) {
            console.log(`Authentication attempt ${i + 1}/3`);
            authSuccess = await authenticateWallet();
            if (authSuccess) {
              console.log('Authentication successful');
              break;
            }
            // Wait before retrying
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

          if (!authSuccess) {
            console.error('Failed to authenticate after multiple attempts');
          }

          // Create the Drift client separately
          const client = await createClient(connection, walletAdapter);
          if (client) {
            set({ driftClient: client });
          }

          // Add event listeners for connection status changes
          walletAdapter.on('connect', () => {
            set(state => ({
              ...state,
              isConnected: true,
              walletAddress: walletAdapter.publicKey?.toString() || null,
              wallet: walletAdapter
            }));
            // Use a timeout to ensure wallet is fully connected before authentication
            setTimeout(() => authenticateWallet(), 500);
          });

          walletAdapter.on('disconnect', () => {
            set(state => ({
              ...state,
              isConnected: false,
              walletAddress: null,
              wallet: null
            }));
            logout();
          });
        } catch (error) {
          console.error("Error connecting wallet:", error);
        }
      },

      resetChatState: () => {
        set({
          currentChatId: null,
          agent: null,
          chats: [],
        });
      },

      disconnectWallet: async () => {
        const { wallet, resetChatState } = get();
        if (wallet && typeof wallet.disconnect === 'function') {
          try {
            // Remove event listeners before disconnecting
            if (wallet.removeAllListeners) {
              wallet.removeAllListeners('connect');
              wallet.removeAllListeners('disconnect');
            }

            await wallet.disconnect();
          } catch (error) {
            console.error("Error disconnecting wallet:", error);
          }
        }

        // Logout from backend
        await logout();

        set({
          isConnected: false,
          wallet: null,
          walletType: null,
          connection: null,
          driftClient: null,
          walletAddress: null,
        });

        // Reset chat-related states
        resetChatState();
      },

      setWalletType: (type: 'phantom' | null) => {
        set({ walletType: type });
      },

      setDriftClient: (client: DriftClient) => {
        set({ driftClient: client });
      }
    }),
    {
      name: 'wallet-storage',
      partialize: (state) => ({
        isConnected: state.isConnected,
        walletType: state.walletType,
        walletAddress: state.walletAddress,
      }),
    }
  )
);

// Check if wallet is connected on page load and reconnect if needed
export const initializeWallet = async () => {
  const { isConnected, walletType, walletAddress, connectWallet } = useWalletStore.getState();
  console.log('Initializing wallet:', { isConnected, walletType, walletAddress });

  // If we already have a wallet address stored but are not connected (e.g. after page refresh)
  if (!isConnected && walletType === 'phantom' && walletAddress) {
    try {
      console.log('Attempting to reconnect wallet');

      // Try to reconnect using the wallet adapter
      const walletAdapter = new PhantomWalletAdapter();

      // Add a small delay to ensure the wallet adapter is ready
      await new Promise(resolve => setTimeout(resolve, 500));

      if (walletAdapter.connected) {
        // Already connected
        console.log('Wallet already connected, calling connectWallet');
        connectWallet(walletAddress);
      } else {
        try {
          console.log('Wallet not connected, attempting to connect');
          // Try to connect
          await walletAdapter.connect();
          const newAddress = walletAdapter.publicKey?.toString();
          console.log('New wallet address:', newAddress);

          if (newAddress && newAddress === walletAddress) {
            console.log('Wallet address matches, calling connectWallet');
            connectWallet(newAddress);
          } else if (newAddress) {
            // Address changed but still valid
            console.log('Wallet address changed, calling connectWallet with new address');
            connectWallet(newAddress);
          }
        } catch (walletError) {
          console.error('Error connecting to wallet:', walletError);
          // Don't show alert to avoid disrupting user experience
          console.warn('Failed to connect to Phantom wallet. Please ensure it is open and try again.');
        }
      }
    } catch (error) {
      console.error('Error initializing wallet:', error);
      // Return cleanly without throwing to avoid crashing the app
      return;
    }
  } else {
    console.log('No stored wallet to reconnect');
  }
};
