import React, { useState } from 'react';
import { ChatProvider } from '@/context/ChatContext';
import ChatInterface from '@/components/ChatInterface';
import SidebarMenu from '@/components/SidebarMenu';
import SidebarBackdrop from '@/components/SidebarBackdrop';
import { Menu } from 'lucide-react';
import { Toaster } from 'sonner';
import Header from '@/components/Header';

const Index = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const closeSidebar = () => {
    setSidebarOpen(false);
  };

  return (
    <ChatProvider>
      <div className="flex flex-col h-screen w-full text-white bg-[#161616] overflow-hidden">
        <Toaster position="bottom-right" />

        {/* Fixed Header */}
        <div className="flex-shrink-0">
          <Header />
        </div>

        <button
          onClick={toggleSidebar}
          className="md:hidden fixed top-4 left-1 z-50 p-2 rounded-md bg-gold-dark/20 text-white"
        >
          <Menu className="h-6 w-6" />
        </button>

        <SidebarMenu isOpen={sidebarOpen} onClose={closeSidebar} />
        {sidebarOpen && <SidebarBackdrop isOpen={sidebarOpen} onClose={closeSidebar} />}

        {/* Main Content Area - takes remaining height */}
        <div className="flex-1 flex flex-col min-h-0">
          <main className="flex-1 flex min-h-0">
            <ChatInterface />
          </main>
        </div>
      </div>
    </ChatProvider>
  );
};

export default Index;
