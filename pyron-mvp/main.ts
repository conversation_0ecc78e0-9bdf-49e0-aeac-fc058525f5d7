import express, { Application } from 'express';
import bodyParser from 'body-parser';
import cookieParser from 'cookie-parser';
import dotenv from 'dotenv';
import mongoose from 'mongoose';
import agentRouter from './routers/agentRouter';
import userRouter from './routers/userRouter';
import authRouter from './routers/authRouter';
import tradeRouter from './routers/tradeRouter';
import logRouter from './routers/logRouter';
import chatRouter from './routers/chatRouter';
import hypothesisRouter from './routers/hypothesisRouter';

import cors from 'cors';

// Configure dotenv to read the .env file
dotenv.config();

const port = process.env.PORT || 3000; // You can choose any port that is free on your system, defaulting to 3000 if not specified in .env
// Express Application Setup
const app: Application = express();
app.use(bodyParser.json());
app.use(cookieParser());

// Check required environment variables
if (!process.env.JWT_SECRET) {
  throw new Error("JWT_SECRET must be set in the environment variables");
}

// MongoDB URI
if (!process.env.MONGO_USER || !process.env.MONGO_PASSWORD || !process.env.MONGO_HOST || !process.env.MONGO_DB || !process.env.MONGO_PORT) {
  throw new Error("MONGO_USER, MONGO_PASSWORD, and MONGO_HOST must be set in the environment variables");
}
const username = encodeURIComponent(process.env.MONGO_USER);
const password = encodeURIComponent(process.env.MONGO_PASSWORD);
const dbName = process.env.MONGO_DB;
const host = process.env.MONGO_HOST;
const portDb = process.env.MONGO_PORT;

const mongoUri = `mongodb://${username}:${password}@${host}:${portDb}/${dbName}?authSource=admin`;

// MongoDB Connection
mongoose.connect(mongoUri, {
  connectTimeoutMS: 20000, //  connection timeout
  socketTimeoutMS: 45000,  //  socket timeout
  maxPoolSize: 50,         // Maximum number of connections
  retryWrites: true,
}).then(() => console.log('MongoDB connected'))
  .catch(err => console.log(err));

const corsOptions = {
  methods: ['GET', 'POST', 'DELETE', 'PATCH', 'PUT', 'HEAD', 'OPTIONS'], // Allow these HTTP methods
  allowedHeaders: ['Content-Type', 'Authorization', 'Cache-Control'],
  exposedHeaders: ['Content-Type', 'Authorization'],
  origin: ['https://www.pyron.net', 'http://localhost:3004','http://localhost:3003', 'https://pyron.net', 'http://localhost:8080', 'https://api.pyron.net', 'http://localhost:3000', 'https://bot.pyron.net'],  // Specify the allowed origin
  credentials: true,
  maxAge: 86400 // 24 hours
};
app.use(cors(corsOptions));


// Health check endpoint
app.head('/api/health', (_, res) => {
  res.status(200).send();
});

// Handle OPTIONS preflight requests for the health endpoint
app.options('/api/health', (_, res) => {
  res.status(200).send();
});

app.use('/api/auth', authRouter);
app.use('/api/users', userRouter);
app.use('/api/agents', agentRouter);
app.use('/api/trade', tradeRouter);
app.use('/api/logs', logRouter);
app.use('/api/chats', chatRouter);
app.use('/api/hypothesis', hypothesisRouter);


app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});

// Export the app for testing
export default app;
