import { AMM_RESERVE_PRECISION_EXP, DriftClient, OrderType, PositionDirection, User } from "@drift-labs/sdk";
import { BN } from "bn.js"; // Import BN for Big Number operations
import { getCurrentPrice } from "./getPerpPrice";
import { PERP_MARKETS } from "../../constant";
import { ComputeBudgetProgram, Connection, PublicKey, sendAndConfirmTransaction } from "@solana/web3.js";
import { Transaction } from "@solana/web3.js";
import { getPriorityFee } from "../../utils/getPriorityFees";
import Log, { ILog } from "../../databaseModels/log";
import { Rpc, sendAndConfirmTx } from "@lightprotocol/stateless.js";
import { ixsToVtx } from "../jito/prepareVersionedTransaction";
import { TransactionMessage } from "@solana/web3.js";
import { VersionedTransaction } from "@solana/web3.js";
import { getMarketId } from "../../utils/getMarketId";
import { createClient } from "./getClient";

export async function placeOrder(
  action: string,
  percentage: number,
  subAccountId: number,
  ticker: string,
  signer: any,
  chatId: string,
  webhookSignal: string,
  authority: PublicKey
) {
  try {
    
    console.log("Ticker before determining marketIndex:", ticker);
    let time = Date.now();
    const marketIndex = getMarketId(ticker)

    if (marketIndex === undefined) {
      throw new Error(`marketIndex not found for ${ticker}`);
    }

    const minAmount: number = PERP_MARKETS[ticker as keyof typeof PERP_MARKETS].minAmount;

    let rpcUrl = process.env.RPC_URL;
    if (!rpcUrl) {  
      throw new Error("RPC_URL is not set");
    }
    const connection = new Rpc(rpcUrl, rpcUrl, rpcUrl);
   
    const driftClient = await createClient(connection, signer, authority);
    if (!driftClient) {
      console.log("Drift client is not initialized");
      return;
    }
    const sub = await driftClient.subscribe()
    console.log("driftClient subscribed" + sub);

    const user = driftClient.getUser(subAccountId, authority);
    // fetch perp position
    const perpPos = user?.getPerpPosition(marketIndex);
    const baseAssetAmount = perpPos ? perpPos.baseAssetAmount.toNumber() : 0;

    const currentPrice = await getCurrentPrice(ticker); // Assuming this is how you get the current price, adjust based on actual client setup

    const positionSize = baseAssetAmount / 10 ** AMM_RESERVE_PRECISION_EXP.toNumber();
    console.log("positionSizeDecimal", baseAssetAmount);
    const totalCollateral =
      user.getTotalCollateral().toNumber() / currentPrice / 10 ** 6;

    const currentPositionSide = positionSize > 0 ? "LONG" : "SHORT";

    let newOrderSize = (totalCollateral * percentage) / 100;
    let direction = action === 'buy' ? 'LONG' : 'SHORT'
    let newDirection = direction;
    let orderSize = newOrderSize;
    console.log("baseAssetAmount", baseAssetAmount);
    console.log("subAccountId", subAccountId);
    console.log("orderSize", totalCollateral);
    console.log("positionSize", positionSize);
    if (action === 'close') {
      direction = positionSize > 0 ? "SHORT" : "LONG";
      newDirection = direction;
      orderSize = Math.abs(positionSize)
    }
    else if (action === 'closeBuy') {
      if (positionSize < 0) return;
      direction = "LONG";
      newDirection = direction;
      orderSize = Math.abs(positionSize)
    }
    else if (action === 'closeSell') {
      if (positionSize > 0) return;
      direction = "SHORT";
      newDirection = direction;
      orderSize = Math.abs(positionSize)
    }
    else if (action === 'buy' || action === 'sell') {

      if (direction == "SHORT" && currentPositionSide == "LONG") {
        orderSize = Math.abs(positionSize) + orderSize;
      }
      else if (direction == "LONG" && currentPositionSide == "SHORT") {
        orderSize = Math.abs(positionSize) + orderSize;
      }
      else if (direction == "LONG" && currentPositionSide == "LONG") {
        orderSize = orderSize - positionSize;
        newDirection = orderSize > 0 ? "LONG" : "SHORT";
      }
      else if (direction == "SHORT" && currentPositionSide == "SHORT") {
        orderSize = orderSize - Math.abs(positionSize);
        newDirection = orderSize > 0 ? "SHORT" : "LONG";
      }
    }

    orderSize = Math.abs(orderSize);
    let res = undefined;
    if (orderSize < minAmount) {
      console.log("orderSize is too small ", orderSize, " < ", minAmount);
    }
    else {
      let time2 = Date.now();
      console.log("tx prepare time", time2- time);

      // Create order params
      const orderParamsMarket = {
        orderType: OrderType.MARKET,
      marketIndex: marketIndex,
      direction:
        newDirection === "SHORT" ? PositionDirection.SHORT : PositionDirection.LONG,
      baseAssetAmount: driftClient.convertToPerpPrecision(orderSize),
      //price: driftClient.convertToPricePrecision(currentPrice),
      auctionStartPrice: driftClient.convertToPricePrecision(currentPrice),
      auctionEndPrice: driftClient.convertToPricePrecision(newDirection === "SHORT" ? currentPrice - 0.1 : currentPrice + 0.1),
      auctionDuration: 3,
      //immediateOrCancel: true,
      maxTs: new BN(Date.now() + 100), //new BN(Number.MAX_SAFE_INTEGER);
    };
    console.log("orderParamsMarket", orderParamsMarket);
    const ix = await driftClient.getPlacePerpOrderIx(orderParamsMarket, subAccountId);
    let tradeTx = new Transaction().add(ix);
    let blockhash = await connection.getLatestBlockhash();
    tradeTx.lastValidBlockHeight = blockhash.lastValidBlockHeight;
    tradeTx.recentBlockhash = blockhash.blockhash;
    tradeTx.feePayer = signer.publicKey;
    tradeTx.sign(signer);
    // Set compute unit limit and price
    let microLamports = await getPriorityFee(tradeTx)
    console.log("priority fee", microLamports);
    if (microLamports > 1_000_000) microLamports = 1_000_000;
    let units = 90_000;
    const cuRequestIx = ComputeBudgetProgram.setComputeUnitLimit({ units });
    const cuPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports,
    })

    const message = new TransactionMessage({
      payerKey: signer.publicKey,
      recentBlockhash: blockhash.blockhash,
      instructions: [cuRequestIx, cuPriceIx, ix],
    }).compileToV0Message();
    const versionedTransaction:VersionedTransaction = new VersionedTransaction(message);
    versionedTransaction.sign([signer]);    // Send transaction
    //let transaction = new Transaction().add(cuRequestIx, cuPriceIx, ix);
    //transaction.lastValidBlockHeight = blockhash.lastValidBlockHeight;
    //transaction.recentBlockhash = blockhash.blockhash;
    //transaction.feePayer = signer.publicKey;
    res = await sendAndConfirmTx(connection, versionedTransaction, {
      commitment: 'confirmed',
      skipPreflight: true,
      maxRetries: 0
    }).catch(async e => {
      console.log("error", e);
      // Retry up to 3 times
      for (let i = 0; i < 3; i++) {
        try {
          console.log(`Retrying transaction attempt ${i + 1}`);
          // Get new blockhash for retry
          blockhash = await connection.getLatestBlockhash();
          const message = new TransactionMessage({
            payerKey: signer.publicKey,
            recentBlockhash: blockhash.blockhash,
            instructions: [cuRequestIx, cuPriceIx, ix],
          }).compileToV0Message();
          const versionedTransaction:VersionedTransaction = new VersionedTransaction(message);
          versionedTransaction.sign([signer]);    // Send transaction

          let retryResult = await sendAndConfirmTx(connection, versionedTransaction, {
            commitment: 'confirmed',
            skipPreflight: true,
            maxRetries: 0
            }
          );
          return retryResult;
        } catch (retryError) {
          console.log(`Retry ${i + 1} failed:`, retryError);
          if (i === 2) return undefined; // Return undefined after all retries fail
          await new Promise(resolve => setTimeout(resolve, 100)); // Wait 1s between retries
        }
      }
      return undefined;
    })
    console.log("tx execution time", Date.now() - time2);

    // Wait for position to update & confirm the change
    if (res) {
      let positionChanged = false;

      for (let i = 0; i < 30; i++) {
        // Wait ~2 seconds before each check
        await new Promise((r) => setTimeout(r, 1000));

        // Re-fetch user data from the blockchain
        await user.fetchAccounts();

        // Compare the new baseAssetAmount to the old
        const newBaseAssetAmount =
          user.getPerpPosition(marketIndex)?.baseAssetAmount.toNumber() || 0;
        const newPositionSize = newBaseAssetAmount / 10 ** AMM_RESERVE_PRECISION_EXP.toNumber();

        if (Math.abs(newPositionSize - positionSize) > 1e-6) {
          console.log("Position successfully updated on-chain!");
          positionChanged = true;
          break;
        } else {
          console.log("Position has NOT changed yet; retrying...");
        }
      }

      if (!positionChanged) {
        console.log("Position did NOT change after 3 checks!");
      }
    }
  }

    let log = new Log({
      chatId,
      type: "Market",
      side: newDirection,
      size: orderSize.toFixed(5),
      total: totalCollateral.toFixed(5),
      percentage: "100",
      market: ticker + "-PERP",
      status: res || "failed",
      shown: 0,
      webhookSignal,
      price: currentPrice,
      timestamps: new Date().toISOString()
    })
    console.log({ log })

    const savedLog = await log.save();

    console.log("finish time", Date.now() - time);
    await driftClient.unsubscribe();
    return res;
  }
  catch (e) {
    console.log("error", e);
    return undefined;
  }
}

