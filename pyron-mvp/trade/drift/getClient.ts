import {Connection} from "@solana/web3.js";
import {Wallet, DriftClient, DRIFT_PROGRAM_ID, BulkAccountLoader} from "@drift-labs/sdk";
import { PublicKey } from "@solana/web3.js";

export async function createClient(connection:Connection, keypair:any, authority:PublicKey) {  
  try {
const wallet = new Wallet(keypair)
/* const bulkAccountLoader = new BulkAccountLoader(
  connection,
  'confirmed',
  1
); */

const driftClient = new DriftClient({
  connection,
  wallet,
  env: 'mainnet-beta',
  programID: new PublicKey(DRIFT_PROGRAM_ID),

  /* opts: {
    commitment: 'confirmed',
    preflightCommitment: 'confirmed',
    skipPreflight: true,
    maxRetries: 10,
    
    }, */
  //includeDelegates: true,
  /*txHandlerConfig:{ 
    blockhashCachingEnabled:false,
    blockhashCachingConfig: {
        retryCount: 0,
        retrySleepTimeMs: 0,
        staleCacheTimeMs: 0,
    }

  },*/
			opts: {
				commitment: 'confirmed',
			},
			//activeSubAccountId: 0,
			//perpMarketIndexes: [0,1,2,3,4],
			//spotMarketIndexes: [0,1,2,3,4],
      authority: authority,
      includeDelegates: true,
			/*accountSubscription: {
				type: 'polling',
				accountLoader: bulkAccountLoader,
			}*/
      //authority:userPK
      
});
//console.log("driftClient", driftClient);
///await driftClient.subscribe();
  //activeSubAccountId: subAccountId,
//console.log("driftClient", driftClient);
return driftClient;
} catch (error:any) {
  console.error("Error creating drift client:", error?.message);
  //throw error;
}
}