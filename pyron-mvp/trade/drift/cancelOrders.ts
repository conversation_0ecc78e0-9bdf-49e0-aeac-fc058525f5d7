import { Connection, PublicKey } from "@solana/web3.js";
import { createClient } from "./getClient";

export async function cancelOrders(agent: any, marketIndex: number, connection: Connection, signer: any, authority: PublicKey,side:string) {
    const driftClient = await createClient(connection, signer, authority);
    if (!driftClient) {
      console.log("Drift client is not initialized");
      return;
    }
    const sub = await driftClient.subscribe();
    console.log("driftClient subscribed" + sub);
    // fetch agent's driftuser
    const user = driftClient.getUser(agent.number, new PublicKey(agent.pubkey))
    if (!user) {
      console.log("cannot fetch user");
      return;
    }
  
    // Gather open orders
    const placedOrders = user.getOpenOrders();
    const openOrdersShort = placedOrders.filter(
      (order) => order.direction === "short" && order.status === "open" && order.marketType === "perp" && order.marketIndex === marketIndex
    );
    const openOrdersLong = placedOrders.filter(
      (order) => order.direction === "long" && order.status === "open" && order.marketType === "perp" && order.marketIndex === marketIndex
    );
    let openOrders = [];
    if (side === "short") {
        openOrders = openOrdersShort;
    } else if (side === "long") {
        openOrders = openOrdersLong;
    }
    else {
        openOrders = [...openOrdersShort, ...openOrdersLong];
    }
    // Cancel all open orders
    for (const order of openOrders) {
        await driftClient.cancelOrder(order.orderId, {}, agent.number);
    }
    await driftClient.unsubscribe();
    return;

}