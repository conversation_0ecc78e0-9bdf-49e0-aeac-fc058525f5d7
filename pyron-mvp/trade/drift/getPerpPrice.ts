import axios from 'axios';
// Helper function to fetch the current price from the market's order book
export async function getCurrentPrice(marketName: string): Promise<number> {
  const url = `https://dlob.drift.trade/l2?marketName=${marketName}-PERP&depth=10&includeOracle=true&includeVamm=true`;
  try {
    const response = await axios.get(url);
    const bids = response.data.bids;
    const asks = response.data.asks;
    const lastBidPrice = parseFloat(bids[0].price);
    const lastAskPrice = parseFloat(asks[0].price);

    // Calculate the mean price between the last bid and last ask
    return (lastBidPrice + lastAskPrice) / 2/10**6;
  } catch (error) {
    console.error('Failed to fetch market price:', error);
    throw new Error('Failed to fetch market price');
  }
}

