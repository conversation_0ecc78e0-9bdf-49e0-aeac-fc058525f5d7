import { Transaction, SystemProgram, Keypair, PublicKey, Connection, TransactionMessage, VersionedTransaction, Account } from '@solana/web3.js';
import * as jito from 'jito-ts'
import { getBundleStateAndUpdateFees } from './bundleState';

// Function to prepare and send a bundle of transactions
export async function prepareAndSendBundle(tipSender: any, versionedTransactions: VersionedTransaction[], connection: any, maxTxs: number, networkFees: number, swapFees: number) {
  try {
    // Retrieve the tip amount from the environment variables
    const tipAmount = parseFloat(process.env.TIP || "1000");
    console.log("preparing the bundle ...");
    const JITO_BLOCK="frankfurt.mainnet.block-engine.jito.wtf"

    // Initialize the Jito searcher client
    let sc = jito.searcher.searcherClient(JITO_BLOCK);

    // Get the list of tip accounts from the Jito searcher client
    let tipAccounts = await sc.getTipAccounts();

    let tipAccount = tipAccounts.ok ? tipAccounts.value[0] : undefined;
    if (!tipAccount) throw new Error("No tip accounts found");
    // Create a transfer instruction to send the tip
    const tipIx = SystemProgram.transfer({
      fromPubkey: tipSender.publicKey, // Sender's public key
      toPubkey: new PublicKey(tipAccount), // Recipient's public key (first tip account)
      lamports: tipAmount, // Amount to tip in lamports
    });

    // Create a message for the tip transaction
    const tipMessage = new TransactionMessage({
      payerKey: tipSender.publicKey, // Payer's public key
      recentBlockhash: (await connection.getLatestBlockhash()).blockhash, // Latest blockhash
      instructions: [tipIx], // Instructions to include in the transaction
    }).compileToV0Message();

    // Create a versioned transaction from the message and sign it
    const tipTx = new VersionedTransaction(tipMessage);
    tipTx.sign([tipSender]);

    // Create a bundle of transactions including the tip transaction
    let bundle = new jito.bundle.Bundle([...versionedTransactions, tipTx], maxTxs);

    // Send the bundle using the Jito searcher client
    let res = await sc.sendBundle(bundle);
    console.log({ res });
    let bundleState;
    if(res.ok)
    // get the bundle state and update fees
    bundleState = await getBundleStateAndUpdateFees(res.value,JITO_BLOCK);
    return bundleState;


  } catch (error) {
    // Log and rethrow any errors that occur
    console.error('Error:', error);
    throw error;
  }
}