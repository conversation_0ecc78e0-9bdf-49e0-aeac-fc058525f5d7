import { Connection, TransactionInstruction, TransactionMessage, VersionedTransaction } from "@solana/web3.js";

export async function ixsToVtx(instructions:TransactionInstruction[],sender:any,signers:any[],connection:any) {
  //console.log("instructions",instructions.length)
    const { blockhash } = await connection.getLatestBlockhash();
    console.log("sender",sender.toString())
    const message = new TransactionMessage({
      payerKey: sender,
      recentBlockhash: blockhash,
      instructions,
    }).compileToV0Message();
//console.log("message",message)
    const versionedTransaction:VersionedTransaction = new VersionedTransaction(message);
    versionedTransaction.sign(signers);
    //console.log("versionedTransaction",versionedTransaction)
    return versionedTransaction
  }
