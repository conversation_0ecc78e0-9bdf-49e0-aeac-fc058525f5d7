import axios from "axios";
export async function getBundleStateAndUpdateFees(bundle:string,block:string){
    // Create the JSON-RPC request body for the bundle
    const requestBody = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getBundleStatuses",
        "params": [
          [
            bundle
          ]
        ]
      };
  
      await new Promise(resolve => setTimeout(resolve, 30000));
  
      // Send the request to Jito's Block Engine
      const response = await axios.post("https://"+block+"/api/v1/bundles", requestBody, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
  
      console.log('Response:', JSON.stringify(response.data));
    }