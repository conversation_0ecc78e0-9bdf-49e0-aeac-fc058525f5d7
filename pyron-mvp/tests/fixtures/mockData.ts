// Mock data for testing
import { Types } from 'mongoose';
import sinon from 'sinon';

// Mock Agent Data
export const mockAgent = {
  _id: new Types.ObjectId('507f1f77bcf86cd799439011'),
  agentName: 'Test Agent',
  assetPair: 'SOL-PERP',
  pubkey: 'test-pubkey-123',
  buySignalMessage: 'Buy signal message',
  sellSignalMessage: 'Sell signal message',
  timestamps: Date.now().toString(),
  tradingStatus: 'on',
  number: 1,
  botId: 'test-bot-id-123',
  chatId: 'test-chat-id-123',
  requiredBuyConfirmationsOpen: 2,
  requiredSellConfirmationsOpen: 2,
  requiredBuyConfirmationsOverride: 1,
  requiredSellConfirmationsOverride: 1,
  requiredBuyConfirmationsClose: 1,
  requiredSellConfirmationsClose: 1,
  requiredBuyConfirmationsResetCounter: 3,
  requiredSellConfirmationsResetCounter: 3,
  requiredBuyConfirmationsOpenBar: 1,
  requiredSellConfirmationsOpenBar: 1,
  signals: {
    sell: 0,
    buy: 0,
    sellOpenBar: 0,
    buyOpenBar: 0
  },
  deposit: 1000,
  sellReportPrompt: 'Sell report prompt',
  buyReportPrompt: 'Buy report prompt',
  hypothesisStatus: 'off',
  save: sinon.stub().resolves()
};

export const mockAgentArray = [
  mockAgent,
  {
    ...mockAgent,
    _id: new Types.ObjectId('507f1f77bcf86cd799439012'),
    agentName: 'Test Agent 2',
    botId: 'test-bot-id-456',
    chatId: 'test-chat-id-456',
    assetPair: 'BTC-PERP',
    pubkey: 'test-pubkey-456'
  }
];

// Mock Chat Data
export const mockChat = {
  _id: new Types.ObjectId('507f1f77bcf86cd799439013'),
  title: 'Test Chat',
  walletAddress: 'test-wallet-address',
  createdAt: new Date(),
  updatedAt: new Date(),
  chatType: 'general',
  metadata: { testKey: 'testValue' },
  save: sinon.stub().resolves()
};

export const mockChatArray = [
  mockChat,
  {
    ...mockChat,
    _id: new Types.ObjectId('507f1f77bcf86cd799439019'),
    title: 'Test Chat 2',
    walletAddress: 'test-wallet-address-2',
    chatType: 'trading',
    metadata: { agentId: 'test-agent-123' }
  }
];

// Mock ChatMessage Data
export const mockChatMessage = {
  _id: new Types.ObjectId('507f1f77bcf86cd799439020'),
  chatId: new Types.ObjectId('507f1f77bcf86cd799439013'),
  type: 'user',
  content: 'Test message content',
  timestamp: new Date(),
  metadata: { sender: 'user' },
  save: sinon.stub().resolves()
};

export const mockChatMessageArray = [
  mockChatMessage,
  {
    ...mockChatMessage,
    _id: new Types.ObjectId('507f1f77bcf86cd799439021'),
    type: 'bot',
    content: 'Bot response message',
    metadata: { sender: 'bot', agentId: 'test-agent-123' }
  }
];

// Mock User Data
export const mockUser = {
  _id: new Types.ObjectId('507f1f77bcf86cd799439014'),
  walletAddress: 'test-wallet-address',
  lastLoginAt: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  save: sinon.stub().resolves()
};

// Mock Log Data
export const mockLog = {
  _id: new Types.ObjectId('507f1f77bcf86cd799439015'),
  chatId: 'test-chat-id-123',
  type: 'trade',
  side: 'buy',
  size: '10.5',
  total: '1050.25',
  percentage: '5.2',
  market: 'SOL-PERP',
  status: 'completed',
  shown: 1,
  timestamps: Date.now().toString(),
  webhookSignal: 'buy_signal_webhook',
  save: sinon.stub().resolves()
};

export const mockLogArray = [
  mockLog,
  {
    ...mockLog,
    _id: new Types.ObjectId('507f1f77bcf86cd799439016'),
    chatId: 'test-chat-id-456',
    type: 'position',
    side: 'sell',
    size: '5.0',
    total: '500.00',
    percentage: '2.5',
    market: 'BTC-PERP',
    status: 'pending',
    shown: 0,
    webhookSignal: 'sell_signal_webhook'
  }
];

// Mock Hypothesis Data
export const mockHypothesis = {
  _id: new Types.ObjectId('507f1f77bcf86cd799439017'),
  logId: new Types.ObjectId('507f1f77bcf86cd799439015'),
  hypothesis: 'Test hypothesis',
  date: new Date(),
  save: sinon.stub().resolves(),
  createdAt: new Date(),
  updatedAt: new Date()
};

export const mockHypothesisArray = [
  mockHypothesis,
  {
    _id: new Types.ObjectId('507f1f77bcf86cd799439018'),
    logId: new Types.ObjectId('507f1f77bcf86cd799439016'),
    hypothesis: 'Second test hypothesis',
    date: new Date(Date.now() - 86400000), // 1 day ago
    save: sinon.stub().resolves(),
    createdAt: new Date(Date.now() - 86400000),
    updatedAt: new Date(Date.now() - 86400000)
  }
];

export const validHypothesisData = {
  logId: '507f1f77bcf86cd799439015',
  hypothesis: 'Valid test hypothesis for trading analysis'
};

export const invalidHypothesisData = {
  missingLogId: {
    hypothesis: 'Test hypothesis without logId'
  },
  missingHypothesis: {
    logId: '507f1f77bcf86cd799439015'
  },
  emptyHypothesis: {
    logId: '507f1f77bcf86cd799439015',
    hypothesis: ''
  },
  invalidLogId: {
    logId: 'invalid-object-id',
    hypothesis: 'Test hypothesis with invalid logId'
  }
};

// Mock JWT Tokens
export const mockTokens = {
  accessToken: 'mock-access-token',
  refreshToken: 'mock-refresh-token',
  tokenPayload: {
    walletAddress: 'test-wallet-address',
    type: 'access',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600
  },
  refreshTokenPayload: {
    walletAddress: 'test-wallet-address',
    type: 'refresh',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (7 * 24 * 3600) // 7 days
  }
};

// Mock Request/Response objects
export const mockRequest = (body: any = {}, params: any = {}, headers: any = {}, cookies: any = {}) => ({
  body,
  params,
  headers,
  cookies,
  user: undefined
});

export const mockResponse = () => {
  const res: any = {};
  res.status = sinon.stub().returnsThis();
  res.json = sinon.stub().returnsThis();
  res.cookie = sinon.stub().returnsThis();
  res.clearCookie = sinon.stub().returnsThis();
  res.send = sinon.stub().returnsThis();
  return res;
};

// Mock Drift Client Data
export const mockDriftClient = {
  getUser: sinon.stub(),
  subscribe: sinon.stub().resolves(true),
  getTotalAssetValue: sinon.stub().resolves(1000),
  getPerpPosition: sinon.stub().resolves({
    baseAssetAmount: 100,
    quoteAssetAmount: 1000,
    unrealizedPnl: 50
  })
};

// Mock Price Data
export const mockPriceData = {
  price: 100.50,
  timestamp: Date.now(),
  market: 'SOL-PERP'
};

// Mock Trading Data
export const mockTradeData = {
  orderId: 'test-order-123',
  status: 'filled',
  size: 10,
  price: 100.50,
  side: 'buy',
  timestamp: Date.now()
};

// Error Messages
export const errorMessages = {
  AGENT_NOT_FOUND: 'Agent not found',
  INVALID_TRADING_STATUS: 'Invalid trading status. Must be either "on" or "off".',
  INVALID_HYPOTHESIS_STATUS: 'Invalid hypothesis status. Must be either "on" or "off".',
  MISSING_REQUIRED_FIELDS: 'Missing required fields',
  WALLET_ADDRESS_REQUIRED: 'Wallet address is required',
  AGENT_NAME_REQUIRED: 'Agent name is required',
  CHAT_NOT_FOUND: 'Chat not found',
  INVALID_OBJECT_ID: 'Invalid ObjectId',
  SERVER_ERROR: 'Server error',
  DATABASE_ERROR: 'Database error',
  NO_TOKEN_PROVIDED: 'No token provided',
  ACCESS_DENIED: 'Access denied'
};

// Valid test data for creation
export const validAgentData = {
  agentName: 'Valid Test Agent',
  assetPair: 'SOL-PERP',
  pubkey: 'valid-pubkey-123',
  buySignalMessage: 'Valid buy signal',
  sellSignalMessage: 'Valid sell signal',
  tradingStatus: 'on',
  number: 1,
  botId: 'valid-bot-id-123',
  chatId: 'valid-chat-id-123',
  signals: {
    sell: 0,
    buy: 0,
    sellOpenBar: 0,
    buyOpenBar: 0
  },
  deposit: 1000,
  requiredConfirmationsOpen: 2
};

export const validLogData = {
  chatId: 'valid-chat-id-123',
  type: 'trade',
  side: 'buy',
  size: '15.0',
  total: '1500.00',
  percentage: '7.5',
  market: 'SOL-PERP',
  status: 'completed',
  shown: 1,
  timestamps: Date.now().toString(),
  webhookSignal: 'valid_webhook_signal'
};

export const validChatData = {
  walletAddress: 'valid-wallet-address-123',
  title: 'Valid Test Chat',
  chatType: 'general',
  metadata: { testKey: 'testValue' }
};

export const validChatMessageData = {
  chatId: '507f1f77bcf86cd799439013',
  type: 'user',
  content: 'Valid test message content',
  metadata: { sender: 'user' }
};

export const validUserData = {
  walletAddress: 'valid-wallet-address-123',
  displayName: 'Test User',
  settings: { theme: 'dark' },
  apiKeys: { driftApi: 'test-key' }
};

// Invalid test data for validation testing
export const invalidAgentData = {
  // Missing required fields
  incomplete: {
    agentName: 'Test Agent'
    // Missing assetPair, pubkey, botId, deposit
  },
  invalidTradingStatus: {
    ...validAgentData,
    tradingStatus: 'invalid-status'
  },
  invalidHypothesisStatus: {
    ...validAgentData,
    hypothesisStatus: 'invalid-status'
  },
  invalidDeposit: {
    ...validAgentData,
    deposit: 'not-a-number'
  }
};

export const invalidLogData = {
  // Missing required fields
  incomplete: {
    chatId: 'test-chat-id'
    // Missing type, side, size, market, status, shown, timestamps
  },
  missingChatId: {
    ...validLogData,
    chatId: undefined
  },
  missingType: {
    ...validLogData,
    type: undefined
  },
  missingRequiredFields: {
    chatId: 'test-chat-id'
    // Missing all other required fields
  }
};

export const invalidChatData = {
  missingWalletAddress: {
    title: 'Test Chat'
    // Missing walletAddress
  },
  missingTitle: {
    walletAddress: 'test-wallet-address'
    // Missing title
  },
  emptyWalletAddress: {
    walletAddress: '',
    title: 'Test Chat'
  },
  emptyTitle: {
    walletAddress: 'test-wallet-address',
    title: ''
  }
};

export const invalidChatMessageData = {
  missingChatId: {
    type: 'user',
    content: 'Test message'
    // Missing chatId
  },
  missingType: {
    chatId: '507f1f77bcf86cd799439013',
    content: 'Test message'
    // Missing type
  },
  missingContent: {
    chatId: '507f1f77bcf86cd799439013',
    type: 'user'
    // Missing content
  },
  invalidChatId: {
    chatId: 'invalid-object-id',
    type: 'user',
    content: 'Test message'
  },
  emptyContent: {
    chatId: '507f1f77bcf86cd799439013',
    type: 'user',
    content: ''
  }
};

export const invalidUserData = {
  missingWalletAddress: {
    displayName: 'Test User'
    // Missing walletAddress
  },
  emptyWalletAddress: {
    walletAddress: '',
    displayName: 'Test User'
  },
  duplicateWalletAddress: {
    walletAddress: 'existing-wallet-address',
    displayName: 'Test User'
  }
};
