import request from 'supertest';
import app from '../../../main';
import { expect } from 'chai';
import User from '../../../databaseModels/user';
import jwt from 'jsonwebtoken';
import { TokenPayload } from '../../../types/auth';

// Using function() instead of arrow function to access the Mocha context
describe('Auth API', function(this: any) {
  this.timeout(15000); // Set timeout to 15 seconds for this suite

  const testWalletAddress = 'testWalletForAuth';
  let authToken: string | undefined;
  let refreshToken: string | undefined;

  before(async () => {
    // Ensure the test user exists
    await User.findOneAndUpdate(
      { walletAddress: testWalletAddress },
      { walletAddress: testWalletAddress },
      { upsert: true, new: true }
    );
  });

  it('should generate a token for a valid wallet address', async () => {
    const response = await request(app)
      .post('/api/auth/token')
      .send({ walletAddress: testWalletAddress });

    expect(response.status).to.equal(200);
    expect(response.body).to.have.property('token');
    expect(response.body).to.have.property('expiresIn');

    // Save token for later tests
    authToken = response.body.token;

    // Check for refresh token cookie
    expect(response.headers['set-cookie']).to.exist;
    const cookies = response.headers['set-cookie'][0].split(';');
    const refreshTokenCookie = cookies.find((cookie: string) => cookie.trim().startsWith('refresh_token='));
    expect(refreshTokenCookie).to.exist;

    refreshToken = refreshTokenCookie?.split('=')[1];
  });

  it('should create a new user if wallet address does not exist', async () => {
    const uniqueWalletAddress = 'uniqueTestWalletForAuth';

    // Delete the user if it exists
    await User.deleteOne({ walletAddress: uniqueWalletAddress });

    const response = await request(app)
      .post('/api/auth/token')
      .send({ walletAddress: uniqueWalletAddress });

    expect(response.status).to.equal(200);
    expect(response.body).to.have.property('token');

    // Check if user was created
    const user = await User.findOne({ walletAddress: uniqueWalletAddress });
    expect(user).to.exist;
    expect(user?.walletAddress).to.equal(uniqueWalletAddress);
  });

  it('should return 400 if wallet address is missing', async () => {
    const response = await request(app)
      .post('/api/auth/token')
      .send({});

    expect(response.status).to.equal(400);
    expect(response.body).to.have.property('message', 'Wallet address is required');
  });

  it('should refresh a token with a valid refresh token', async function(this: any) {
    // Skip if no refresh token was obtained
    if (!refreshToken) {
      return this.skip();
    }

    // Set the refresh token cookie
    const response = await request(app)
      .post('/api/auth/refresh')
      .set('Cookie', [`refresh_token=${refreshToken}`]);

    expect(response.status).to.equal(200);
    expect(response.body).to.have.property('token');
    expect(response.body).to.have.property('expiresIn');

    // Verify the new token
    const newToken = response.body.token;
    const JWT_SECRET = process.env.JWT_SECRET;
    if (!JWT_SECRET) {
      throw new Error('JWT_SECRET is not defined in environment variables');
    }

    const decoded = jwt.verify(newToken, JWT_SECRET) as TokenPayload;
    expect(decoded).to.have.property('walletAddress', testWalletAddress);
  });

  it('should logout a user by clearing the refresh token cookie', async () => {
    const response = await request(app)
      .post('/api/auth/logout')
      .set('Cookie', [`refresh_token=${refreshToken}`]);

    expect(response.status).to.equal(200);
    expect(response.body).to.have.property('success', true);

    // Check that the cookie was cleared
    expect(response.headers['set-cookie']).to.exist;
    const cookies = response.headers['set-cookie'][0].split(';');
    const refreshTokenCookie = cookies.find((cookie: string) => cookie.trim().startsWith('refresh_token='));
    expect(refreshTokenCookie).to.exist;
    expect(refreshTokenCookie?.split('=')[1]).to.be.empty;
  });

  it('should protect routes with the auth middleware', async () => {
    // Try to access a protected route without a token
    const response = await request(app)
      .get('/api/chats');

    expect(response.status).to.equal(401);
    expect(response.body).to.have.property('message', 'No token provided');

    // Try to access with a valid token
    const authResponse = await request(app)
      .get('/api/chats')
      .set('Authorization', `Bearer ${authToken}`);

    // This should now pass the auth middleware
    // The actual response depends on the implementation of the chat controller
    expect(authResponse.status).to.not.equal(401);
  });

  after(async () => {
    // Clean up test user
    await User.deleteOne({ walletAddress: testWalletAddress });
  });
});
