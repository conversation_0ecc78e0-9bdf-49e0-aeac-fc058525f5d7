import { expect } from 'chai';
import sinon from 'sinon';
import { Request, Response } from 'express';
import {
  createAgent,
  getAgentById,
  getAgentByPubkeyAndAssetPair,
  getAgentBybotId,
  getAgentBychatId,
  deleteAgent,
  updateTradingStatus,
  updateHypothesisStatus,
  saveOrUpdateAgent,
  getAllAgents,
  addPromptsToAgent,
  updateAgentName
} from '../../../controller/agentController';
import Agent from '../../../databaseModels/agent';
import Chat from '../../../databaseModels/chat';
import {
  mockAgent,
  mockAgentArray,
  mockChat,
  mockRequest,
  mockResponse,
  validAgentData,
  invalidAgentData,
  errorMessages
} from '../../fixtures/mockData';

describe('Agent Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let statusStub: sinon.SinonStub;
  let jsonStub: sinon.SinonStub;
  let sendStub: sinon.SinonStub;

  beforeEach(() => {
    req = mockRequest();
    res = mockResponse();
    statusStub = res.status as sinon.SinonStub;
    jsonStub = res.json as sinon.SinonStub;
    sendStub = res.send as sinon.SinonStub;
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('createAgent', () => {
    it('should create agent with valid data', async () => {
      req.body = validAgentData;
      const saveStub = sinon.stub().resolves(mockAgent);
      sinon.stub(Agent.prototype, 'save').callsFake(saveStub);

      await createAgent(req as Request, res as Response);

      expect(statusStub.calledWith(201)).to.be.true;
      expect(jsonStub.calledWith(mockAgent)).to.be.true;
      expect(saveStub.calledOnce).to.be.true;
    });

    it('should return 400 for missing required fields', async () => {
      req.body = invalidAgentData.incomplete;
      const saveStub = sinon.stub().rejects(new Error('Validation error'));
      sinon.stub(Agent.prototype, 'save').callsFake(saveStub);

      await createAgent(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
    });

    it('should set default values for optional fields', async () => {
      const minimalData = {
        agentName: 'Test Agent',
        assetPair: 'SOL-PERP',
        pubkey: 'test-pubkey',
        botId: 'test-bot-id',
        deposit: 1000
      };
      req.body = minimalData;

      const saveStub = sinon.stub().resolves(mockAgent);
      sinon.stub(Agent.prototype, 'save').callsFake(saveStub);

      await createAgent(req as Request, res as Response);

      expect(statusStub.calledWith(201)).to.be.true;
    });

    it('should handle database errors', async () => {
      req.body = validAgentData;
      const saveStub = sinon.stub().rejects(new Error('Database error'));
      sinon.stub(Agent.prototype, 'save').callsFake(saveStub);

      await createAgent(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
    });
  });

  describe('getAgentById', () => {
    it('should return agent for valid ID', async () => {
      req.params = { id: mockAgent._id.toString() };
      sinon.stub(Agent, 'findById').resolves(mockAgent);

      await getAgentById(req as Request, res as Response);

      expect(jsonStub.calledWith(mockAgent)).to.be.true;
    });

    it('should return 404 for non-existent agent', async () => {
      req.params = { id: '507f1f77bcf86cd799439999' };
      sinon.stub(Agent, 'findById').resolves(null);

      await getAgentById(req as Request, res as Response);

      expect(statusStub.calledWith(404)).to.be.true;
      expect(jsonStub.calledWith({ message: errorMessages.AGENT_NOT_FOUND })).to.be.true;
    });

    it('should return 500 for invalid ObjectId', async () => {
      req.params = { id: 'invalid-id' };
      sinon.stub(Agent, 'findById').rejects(new Error('Cast to ObjectId failed'));

      await getAgentById(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
    });
  });

  describe('getAgentByPubkeyAndAssetPair', () => {
    it('should return agent for valid pubkey and asset pair', async () => {
      req.params = { pubkey: 'test-pubkey', assetPair: 'SOL-PERP' };
      sinon.stub(Agent, 'findOne').resolves(mockAgent);

      await getAgentByPubkeyAndAssetPair(req as Request, res as Response);

      expect(jsonStub.calledWith(mockAgent)).to.be.true;
    });

    it('should return null for non-existent agent', async () => {
      req.params = { pubkey: 'non-existent', assetPair: 'SOL-PERP' };
      sinon.stub(Agent, 'findOne').resolves(null);

      await getAgentByPubkeyAndAssetPair(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(null)).to.be.true;
    });

    it('should handle database errors', async () => {
      req.params = { pubkey: 'test-pubkey', assetPair: 'SOL-PERP' };
      sinon.stub(Agent, 'findOne').rejects(new Error('Database error'));

      await getAgentByPubkeyAndAssetPair(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
    });
  });

  describe('getAgentBybotId', () => {
    it('should return agent for valid bot ID', async () => {
      req.params = { botId: 'test-bot-id' };
      sinon.stub(Agent, 'findOne').resolves(mockAgent);

      await getAgentBybotId(req as Request, res as Response);

      expect(jsonStub.calledWith(mockAgent)).to.be.true;
    });

    it('should return null for non-existent agent', async () => {
      req.params = { botId: 'non-existent' };
      sinon.stub(Agent, 'findOne').resolves(null);

      await getAgentBybotId(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(null)).to.be.true;
    });

    it('should handle database errors', async () => {
      req.params = { botId: 'test-bot-id' };
      sinon.stub(Agent, 'findOne').rejects(new Error('Database error'));

      await getAgentBybotId(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
    });
  });

  describe('getAgentBychatId', () => {
    it('should return agent for valid chat ID', async () => {
      req.params = { chatId: 'test-chat-id' };
      sinon.stub(Agent, 'findOne').resolves(mockAgent);

      await getAgentBychatId(req as Request, res as Response);

      expect(jsonStub.calledWith(mockAgent)).to.be.true;
    });

    it('should return null for non-existent agent', async () => {
      req.params = { chatId: 'non-existent' };
      sinon.stub(Agent, 'findOne').resolves(null);

      await getAgentBychatId(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(null)).to.be.true;
    });

    it('should handle database errors', async () => {
      req.params = { chatId: 'test-chat-id' };
      sinon.stub(Agent, 'findOne').rejects(new Error('Database error'));

      await getAgentBychatId(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
    });
  });

  describe('updateTradingStatus', () => {
    it('should update trading status to "on"', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { tradingStatus: 'on' };
      const mockAgentWithSave = { ...mockAgent, save: sinon.stub().resolves(mockAgent) };
      sinon.stub(Agent, 'findOne').resolves(mockAgentWithSave);

      await updateTradingStatus(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockAgent)).to.be.true;
      expect(mockAgentWithSave.save.calledOnce).to.be.true;
    });

    it('should update trading status to "off"', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { tradingStatus: 'off' };
      const mockAgentWithSave = { ...mockAgent, save: sinon.stub().resolves(mockAgent) };
      sinon.stub(Agent, 'findOne').resolves(mockAgentWithSave);

      await updateTradingStatus(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockAgent)).to.be.true;
    });

    it('should return 400 for invalid status', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { tradingStatus: 'invalid' };

      await updateTradingStatus(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: errorMessages.INVALID_TRADING_STATUS })).to.be.true;
    });

    it('should return 403 for non-existent agent', async () => {
      req.params = { botId: 'non-existent' };
      req.body = { tradingStatus: 'on' };
      sinon.stub(Agent, 'findOne').resolves(null);

      await updateTradingStatus(req as Request, res as Response);

      expect(statusStub.calledWith(403)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Agent not found.' })).to.be.true;
    });
  });

  describe('updateHypothesisStatus', () => {
    it('should update hypothesis status to "on"', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { hypothesisStatus: 'on' };
      const mockAgentWithSave = { ...mockAgent, save: sinon.stub().resolves(mockAgent) };
      sinon.stub(Agent, 'findOne').resolves(mockAgentWithSave);

      await updateHypothesisStatus(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockAgent)).to.be.true;
    });

    it('should update hypothesis status to "off"', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { hypothesisStatus: 'off' };
      const mockAgentWithSave = { ...mockAgent, save: sinon.stub().resolves(mockAgent) };
      sinon.stub(Agent, 'findOne').resolves(mockAgentWithSave);

      await updateHypothesisStatus(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockAgent)).to.be.true;
    });

    it('should return 400 for invalid status', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { hypothesisStatus: 'invalid' };

      await updateHypothesisStatus(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Invalid trading status. Must be either "on" or "off".' })).to.be.true;
    });

    it('should return 403 for non-existent agent', async () => {
      req.params = { botId: 'non-existent' };
      req.body = { hypothesisStatus: 'on' };
      sinon.stub(Agent, 'findOne').resolves(null);

      await updateHypothesisStatus(req as Request, res as Response);

      expect(statusStub.calledWith(403)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Agent not found.' })).to.be.true;
    });
  });

  describe('saveOrUpdateAgent', () => {
    it('should update existing agent', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { agent: validAgentData };
      sinon.stub(Agent, 'findOneAndUpdate').resolves(mockAgent);

      await saveOrUpdateAgent(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockAgent)).to.be.true;
    });

    it('should create new agent if not exists', async () => {
      req.params = { botId: 'new-bot-id' };
      req.body = { agent: validAgentData };
      sinon.stub(Agent, 'findOneAndUpdate').resolves(mockAgent);

      await saveOrUpdateAgent(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockAgent)).to.be.true;
    });

    it('should handle partial updates', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { agent: { agentName: 'Updated Name' } };
      sinon.stub(Agent, 'findOneAndUpdate').resolves(mockAgent);

      await saveOrUpdateAgent(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
    });

    it('should handle database errors', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { agent: validAgentData };
      sinon.stub(Agent, 'findOneAndUpdate').rejects(new Error('Database error'));

      await saveOrUpdateAgent(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
    });
  });

  describe('addPromptsToAgent', () => {
    it('should add buy and sell prompts', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = {
        agent: {
          buyReportPrompt: 'Buy prompt',
          sellReportPrompt: 'Sell prompt'
        }
      };
      sinon.stub(Agent, 'findOneAndUpdate').resolves(mockAgent);

      await addPromptsToAgent(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockAgent)).to.be.true;
    });

    it('should update existing prompts', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = {
        agent: {
          buyReportPrompt: 'Updated buy prompt',
          sellReportPrompt: 'Updated sell prompt'
        }
      };
      sinon.stub(Agent, 'findOneAndUpdate').resolves(mockAgent);

      await addPromptsToAgent(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
    });

    it('should return 404 for non-existent agent', async () => {
      req.params = { botId: 'non-existent' };
      req.body = { agent: { buyReportPrompt: 'Buy prompt' } };
      sinon.stub(Agent, 'findOneAndUpdate').resolves(null);

      await addPromptsToAgent(req as Request, res as Response);

      expect(statusStub.calledWith(404)).to.be.true;
      expect(sendStub.calledWith(errorMessages.AGENT_NOT_FOUND)).to.be.true;
    });

    it('should handle missing prompts gracefully', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { agent: {} };
      sinon.stub(Agent, 'findOneAndUpdate').resolves(mockAgent);

      await addPromptsToAgent(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
    });
  });

  describe('getAllAgents', () => {
    it('should return all agents', async () => {
      sinon.stub(Agent, 'find').resolves(mockAgentArray);

      await getAllAgents(req as Request, res as Response);

      expect(jsonStub.calledWith(mockAgentArray)).to.be.true;
    });

    it('should return empty array when no agents exist', async () => {
      sinon.stub(Agent, 'find').resolves([]);

      await getAllAgents(req as Request, res as Response);

      expect(jsonStub.calledWith([])).to.be.true;
    });

    it('should handle database errors', async () => {
      sinon.stub(Agent, 'find').rejects(new Error('Database error'));

      await getAllAgents(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
    });
  });

  describe('deleteAgent', () => {
    it('should delete existing agent', async () => {
      req.params = { id: mockAgent._id.toString() };
      sinon.stub(Agent, 'findByIdAndDelete').resolves(mockAgent);

      await deleteAgent(req as Request, res as Response);

      expect(jsonStub.calledWith({ message: 'Agent deleted successfully' })).to.be.true;
    });

    it('should return 404 for non-existent agent', async () => {
      req.params = { id: '507f1f77bcf86cd799439999' };
      sinon.stub(Agent, 'findByIdAndDelete').resolves(null);

      await deleteAgent(req as Request, res as Response);

      expect(statusStub.calledWith(404)).to.be.true;
      expect(jsonStub.calledWith({ message: errorMessages.AGENT_NOT_FOUND })).to.be.true;
    });

    it('should handle database errors', async () => {
      req.params = { id: mockAgent._id.toString() };
      sinon.stub(Agent, 'findByIdAndDelete').rejects(new Error('Database error'));

      await deleteAgent(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
    });
  });

  describe('updateAgentName', () => {
    it('should update agent name and chat title', async () => {
      req.params = { id: 'test-chat-id' };
      req.body = { name: 'Updated Agent Name' };

      sinon.stub(Chat, 'findByIdAndUpdate').resolves(mockChat);
      sinon.stub(Agent, 'findOne').resolves(mockAgent);
      sinon.stub(Agent, 'findByIdAndUpdate').resolves(mockAgent);

      await updateAgentName(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
    });

    it('should return 400 for missing name', async () => {
      req.params = { id: 'test-chat-id' };
      req.body = {};

      await updateAgentName(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: errorMessages.AGENT_NAME_REQUIRED })).to.be.true;
    });

    it('should return 404 for non-existent chat', async () => {
      req.params = { id: 'non-existent-chat' };
      req.body = { name: 'Updated Name' };

      sinon.stub(Chat, 'findByIdAndUpdate').resolves(null);

      await updateAgentName(req as Request, res as Response);

      expect(statusStub.calledWith(404)).to.be.true;
      expect(jsonStub.calledWith({ message: errorMessages.CHAT_NOT_FOUND })).to.be.true;
    });

    it('should handle agent without associated chat', async () => {
      req.params = { id: 'test-chat-id' };
      req.body = { name: 'Updated Name' };

      sinon.stub(Chat, 'findByIdAndUpdate').resolves(mockChat);
      sinon.stub(Agent, 'findOne').resolves(null);

      await updateAgentName(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
    });

    it('should handle database errors', async () => {
      req.params = { id: 'test-chat-id' };
      req.body = { name: 'Updated Name' };

      sinon.stub(Chat, 'findByIdAndUpdate').rejects(new Error('Database error'));

      await updateAgentName(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
    });
  });
});
