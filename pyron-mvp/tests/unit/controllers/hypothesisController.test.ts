import { expect } from 'chai';
import sinon from 'sinon';
import { Request, Response } from 'express';
import {
  createHypothesis,
  getHypothesesByLogId,
  getAllHypotheses,
  getHypothesesByChatId
} from '../../../controller/hypothesisController';
import Hypothesis from '../../../databaseModels/hypothesis';
import Log from '../../../databaseModels/log';
import {
  mockHypothesis,
  mockHypothesisArray,
  mockLog,
  mockLogArray,
  mockRequest,
  mockResponse,
  validHypothesisData,
  invalidHypothesisData,
  errorMessages
} from '../../fixtures/mockData';

describe('Hypothesis Controller', () => {
  let req: any;
  let res: any;
  let statusStub: sinon.SinonStub;
  let jsonStub: sinon.SinonStub;

  beforeEach(() => {
    req = mockRequest();
    res = mockResponse();
    statusStub = res.status;
    jsonStub = res.json;
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('createHypothesis', () => {
    it('should create hypothesis with valid data', async () => {
      req.body = validHypothesisData;
      const mockHypothesisWithSave = {
        ...mockHypothesis,
        save: sinon.stub().resolves(mockHypothesis)
      };
      sinon.stub(Hypothesis.prototype, 'save').resolves(mockHypothesis);

      await createHypothesis(req as Request, res as Response);

      expect(statusStub.calledWith(201)).to.be.true;
      expect(jsonStub.calledWith(mockHypothesis)).to.be.true;
    });

    it('should return 400 for missing logId', async () => {
      req.body = invalidHypothesisData.missingLogId;

      await createHypothesis(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: 'LogId and hypothesis are required' })).to.be.true;
    });

    it('should return 400 for missing hypothesis text', async () => {
      req.body = invalidHypothesisData.missingHypothesis;

      await createHypothesis(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: 'LogId and hypothesis are required' })).to.be.true;
    });

    it('should return 400 for empty hypothesis text', async () => {
      req.body = invalidHypothesisData.emptyHypothesis;

      await createHypothesis(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: 'LogId and hypothesis are required' })).to.be.true;
    });

    it('should set creation date automatically', async () => {
      req.body = validHypothesisData;
      const saveStub = sinon.stub(Hypothesis.prototype, 'save').resolves(mockHypothesis);

      await createHypothesis(req as Request, res as Response);

      expect(saveStub.calledOnce).to.be.true;
      expect(statusStub.calledWith(201)).to.be.true;
    });

    it('should handle database errors', async () => {
      req.body = validHypothesisData;
      const error = new Error('Database connection failed');
      sinon.stub(Hypothesis.prototype, 'save').rejects(error);

      await createHypothesis(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: error.message })).to.be.true;
    });
  });

  describe('getHypothesesByLogId', () => {
    it('should return hypotheses for valid log ID', async () => {
      req.params = { logId: mockLog._id.toString() };
      const sortStub = sinon.stub().resolves(mockHypothesisArray);
      sinon.stub(Hypothesis, 'find').returns({ sort: sortStub } as any);

      await getHypothesesByLogId(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockHypothesisArray)).to.be.true;
      expect(sortStub.calledWith({ date: -1 })).to.be.true;
    });

    it('should sort by date descending', async () => {
      req.params = { logId: mockLog._id.toString() };
      const sortStub = sinon.stub().resolves(mockHypothesisArray);
      sinon.stub(Hypothesis, 'find').returns({ sort: sortStub } as any);

      await getHypothesesByLogId(req as Request, res as Response);

      expect(sortStub.calledWith({ date: -1 })).to.be.true;
    });

    it('should return empty array for no hypotheses', async () => {
      req.params = { logId: mockLog._id.toString() };
      const sortStub = sinon.stub().resolves([]);
      sinon.stub(Hypothesis, 'find').returns({ sort: sortStub } as any);

      await getHypothesesByLogId(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith([])).to.be.true;
    });

    it('should return 400 for missing logId', async () => {
      req.params = {};

      await getHypothesesByLogId(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: 'LogId is required' })).to.be.true;
    });

    it('should handle database errors', async () => {
      req.params = { logId: mockLog._id.toString() };
      const error = new Error('Database query failed');
      sinon.stub(Hypothesis, 'find').throws(error);

      await getHypothesesByLogId(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ message: error.message })).to.be.true;
    });
  });

  describe('getAllHypotheses', () => {
    it('should return all hypotheses', async () => {
      const sortStub = sinon.stub().resolves(mockHypothesisArray);
      sinon.stub(Hypothesis, 'find').returns({ sort: sortStub } as any);

      await getAllHypotheses(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockHypothesisArray)).to.be.true;
    });

    it('should sort by date descending', async () => {
      const sortStub = sinon.stub().resolves(mockHypothesisArray);
      sinon.stub(Hypothesis, 'find').returns({ sort: sortStub } as any);

      await getAllHypotheses(req as Request, res as Response);

      expect(sortStub.calledWith({ date: -1 })).to.be.true;
    });

    it('should handle empty collection', async () => {
      const sortStub = sinon.stub().resolves([]);
      sinon.stub(Hypothesis, 'find').returns({ sort: sortStub } as any);

      await getAllHypotheses(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith([])).to.be.true;
    });

    it('should handle database errors', async () => {
      const error = new Error('Database connection failed');
      sinon.stub(Hypothesis, 'find').throws(error);

      await getAllHypotheses(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ message: error.message })).to.be.true;
    });
  });

  describe('getHypothesesByChatId', () => {
    it('should return hypotheses for all logs in chat', async () => {
      req.params = { chatId: 'test-chat-id' };
      sinon.stub(Log, 'find').resolves(mockLogArray);
      const sortStub = sinon.stub().resolves(mockHypothesisArray);
      sinon.stub(Hypothesis, 'find').returns({ sort: sortStub } as any);

      await getHypothesesByChatId(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockHypothesisArray)).to.be.true;
    });

    it('should handle chats with no logs', async () => {
      req.params = { chatId: 'test-chat-id' };
      sinon.stub(Log, 'find').resolves([]);

      await getHypothesesByChatId(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith([])).to.be.true;
    });

    it('should handle chats with null logs', async () => {
      req.params = { chatId: 'test-chat-id' };
      sinon.stub(Log, 'find').resolves(undefined);

      await getHypothesesByChatId(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith([])).to.be.true;
    });

    it('should maintain proper sorting', async () => {
      req.params = { chatId: 'test-chat-id' };
      sinon.stub(Log, 'find').resolves(mockLogArray);
      const sortStub = sinon.stub().resolves(mockHypothesisArray);
      sinon.stub(Hypothesis, 'find').returns({ sort: sortStub } as any);

      await getHypothesesByChatId(req as Request, res as Response);

      expect(sortStub.calledWith({ date: -1 })).to.be.true;
    });

    it('should return empty array when no logs found', async () => {
      req.params = { chatId: 'non-existent-chat' };
      sinon.stub(Log, 'find').resolves([]);

      await getHypothesesByChatId(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith([])).to.be.true;
    });

    it('should validate chatId parameter', async () => {
      req.params = {};

      await getHypothesesByChatId(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: 'ChatId is required' })).to.be.true;
    });

    it('should handle database errors when fetching logs', async () => {
      req.params = { chatId: 'test-chat-id' };
      const error = new Error('Log database error');
      sinon.stub(Log, 'find').rejects(error);

      await getHypothesesByChatId(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ message: error.message })).to.be.true;
    });

    it('should handle database errors when fetching hypotheses', async () => {
      req.params = { chatId: 'test-chat-id' };
      sinon.stub(Log, 'find').resolves(mockLogArray);
      const error = new Error('Hypothesis database error');
      sinon.stub(Hypothesis, 'find').throws(error);

      await getHypothesesByChatId(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ message: error.message })).to.be.true;
    });

    it('should query hypotheses with correct log IDs', async () => {
      req.params = { chatId: 'test-chat-id' };
      sinon.stub(Log, 'find').resolves(mockLogArray);
      const hypothesisFindStub = sinon.stub(Hypothesis, 'find');
      const sortStub = sinon.stub().resolves(mockHypothesisArray);
      hypothesisFindStub.returns({ sort: sortStub } as any);

      await getHypothesesByChatId(req as Request, res as Response);

      expect(hypothesisFindStub.calledOnce).to.be.true;
      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockHypothesisArray)).to.be.true;
    });
  });
});
