import { expect } from 'chai';
import sinon from 'sinon';
import { Request, Response } from 'express';
import {
  createLog,
  getLogs,
  getLog,
  updateLog,
  deleteLog,
  getLogByChatId
} from '../../../controller/logController';
import Log from '../../../databaseModels/log';
import {
  mockLog,
  mockLogArray,
  validLogData,
  invalidLogData,
  errorMessages
} from '../../fixtures/mockData';

describe('Log Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let statusStub: sinon.SinonStub;
  let jsonStub: sinon.SinonStub;

  beforeEach(() => {
    req = {
      body: {},
      params: {},
      query: {}
    };
    jsonStub = sinon.stub();
    statusStub = sinon.stub().returns({ json: jsonStub });
    res = {
      status: statusStub
    };
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('createLog', () => {
    it('should create log with valid data', async () => {
      req.body = validLogData;
      const saveStub = sinon.stub().resolves(mockLog);
      sinon.stub(Log.prototype, 'save').callsFake(saveStub);

      await createLog(req as Request, res as Response);

      expect(statusStub.calledWith(201)).to.be.true;
      expect(jsonStub.calledWith(mockLog)).to.be.true;
      expect(saveStub.calledOnce).to.be.true;
    });

    it('should validate required fields', async () => {
      req.body = invalidLogData.incomplete;
      const saveStub = sinon.stub().rejects(new Error('Validation error'));
      sinon.stub(Log.prototype, 'save').callsFake(saveStub);

      await createLog(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Validation error' })).to.be.true;
    });

    it('should set timestamps correctly', async () => {
      req.body = { ...validLogData };
      const saveStub = sinon.stub().resolves(mockLog);
      sinon.stub(Log.prototype, 'save').callsFake(saveStub);

      await createLog(req as Request, res as Response);

      expect(statusStub.calledWith(201)).to.be.true;
      expect(saveStub.calledOnce).to.be.true;
    });

    it('should handle database errors', async () => {
      req.body = validLogData;
      const saveStub = sinon.stub().rejects(new Error('Database connection failed'));
      sinon.stub(Log.prototype, 'save').callsFake(saveStub);

      await createLog(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Database connection failed' })).to.be.true;
    });
  });

  describe('getLogs', () => {
    it('should return all logs', async () => {
      sinon.stub(Log, 'find').resolves(mockLogArray);

      await getLogs(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockLogArray)).to.be.true;
    });

    it('should handle empty log collection', async () => {
      sinon.stub(Log, 'find').resolves([]);

      await getLogs(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith([])).to.be.true;
    });

    it('should handle database errors', async () => {
      sinon.stub(Log, 'find').rejects(new Error('Database error'));

      await getLogs(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Database error' })).to.be.true;
    });
  });

  describe('getLog', () => {
    it('should return log for valid ID', async () => {
      req.params = { id: mockLog._id.toString() };
      sinon.stub(Log, 'findById').resolves(mockLog);

      await getLog(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(mockLog)).to.be.true;
    });

    it('should return 404 for non-existent log', async () => {
      req.params = { id: '507f1f77bcf86cd799439999' };
      sinon.stub(Log, 'findById').resolves(null);

      await getLog(req as Request, res as Response);

      expect(statusStub.calledWith(404)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Log not found' })).to.be.true;
    });

    it('should return 400 for invalid ObjectId', async () => {
      req.params = { id: 'invalid-id' };
      sinon.stub(Log, 'findById').rejects(new Error('Cast to ObjectId failed'));

      await getLog(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Cast to ObjectId failed' })).to.be.true;
    });
  });

  describe('getLogByChatId', () => {
    it('should return logs for valid chat ID', async () => {
      req.query = { chatId: 'test-chat-id-123' };
      const chatLogs = [mockLog];
      sinon.stub(Log, 'find').resolves(chatLogs);

      await getLogByChatId(req as any, res as any);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(chatLogs)).to.be.true;
    });

    it('should return empty array for no logs', async () => {
      req.query = { chatId: 'non-existent-chat' };
      sinon.stub(Log, 'find').resolves([]);

      await getLogByChatId(req as any, res as any);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith([])).to.be.true;
    });

    it('should validate chat ID format', async () => {
      req.query = { chatId: 123 as any }; // Invalid type

      await getLogByChatId(req as any, res as any);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ error: 'chatId is required and must be a string' })).to.be.true;
    });

    it('should handle query parameter validation', async () => {
      req.query = {}; // Missing chatId

      await getLogByChatId(req as any, res as any);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ error: 'chatId is required and must be a string' })).to.be.true;
    });

    it('should handle database errors', async () => {
      req.query = { chatId: 'test-chat-id' };
      sinon.stub(Log, 'find').rejects(new Error('Database connection failed'));

      await getLogByChatId(req as any, res as any);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ error: 'Internal server error' })).to.be.true;
    });
  });

  describe('updateLog', () => {
    it('should update existing log', async () => {
      req.params = { id: mockLog._id.toString() };
      req.body = { status: 'updated' };
      const updatedLog = { ...mockLog, status: 'updated' };
      sinon.stub(Log, 'findByIdAndUpdate').resolves(updatedLog);

      await updateLog(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith(updatedLog)).to.be.true;
    });

    it('should return 404 for non-existent log', async () => {
      req.params = { id: '507f1f77bcf86cd799439999' };
      req.body = { status: 'updated' };
      sinon.stub(Log, 'findByIdAndUpdate').resolves(null);

      await updateLog(req as Request, res as Response);

      expect(statusStub.calledWith(404)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Log not found' })).to.be.true;
    });

    it('should validate update data', async () => {
      req.params = { id: mockLog._id.toString() };
      req.body = { invalidField: 'invalid' };
      sinon.stub(Log, 'findByIdAndUpdate').rejects(new Error('Validation failed'));

      await updateLog(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Validation failed' })).to.be.true;
    });
  });

  describe('deleteLog', () => {
    it('should delete existing log', async () => {
      req.params = { id: mockLog._id.toString() };
      sinon.stub(Log, 'findByIdAndDelete').resolves(mockLog);

      await deleteLog(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Log deleted successfully' })).to.be.true;
    });

    it('should return 404 for non-existent log', async () => {
      req.params = { id: '507f1f77bcf86cd799439999' };
      sinon.stub(Log, 'findByIdAndDelete').resolves(null);

      await deleteLog(req as Request, res as Response);

      expect(statusStub.calledWith(404)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Log not found' })).to.be.true;
    });

    it('should return success message', async () => {
      req.params = { id: mockLog._id.toString() };
      sinon.stub(Log, 'findByIdAndDelete').resolves(mockLog);

      await deleteLog(req as Request, res as Response);

      expect(jsonStub.calledWith({ message: 'Log deleted successfully' })).to.be.true;
    });

    it('should handle database errors', async () => {
      req.params = { id: mockLog._id.toString() };
      sinon.stub(Log, 'findByIdAndDelete').rejects(new Error('Database error'));

      await deleteLog(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Database error' })).to.be.true;
    });
  });
});
