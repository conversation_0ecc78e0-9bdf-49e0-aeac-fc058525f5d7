import { expect } from 'chai';
import sinon from 'sinon';
import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { generateAuthToken, refreshToken, logout } from '../../../controller/authController';
import User from '../../../databaseModels/user';

describe('Auth Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let statusStub: sinon.SinonStub;
  let jsonStub: sinon.SinonStub;
  let cookieStub: sinon.SinonStub;
  let clearCookieStub: sinon.SinonStub;

  beforeEach(() => {
    req = {
      body: {},
      cookies: {}
    };
    jsonStub = sinon.stub();
    statusStub = sinon.stub().returns({ json: jsonStub });
    cookieStub = sinon.stub();
    clearCookieStub = sinon.stub();
    res = {
      status: statusStub,
      cookie: cookieStub,
      clearCookie: clearCookieStub
    };

    // Set up environment variable
    process.env.JWT_SECRET = 'test-secret';
  });

  afterEach(() => {
    sinon.restore();
    delete process.env.JWT_SECRET;
  });

  describe('generateAuthToken', () => {
    it('should generate token for existing user', async () => {
      const mockUser = {
        walletAddress: 'test-wallet',
        lastLoginAt: new Date(),
        save: sinon.stub().resolves()
      };

      req.body = { walletAddress: 'test-wallet' };
      sinon.stub(User, 'findOne').resolves(mockUser);
      sinon.stub(jwt, 'sign').returns('mock-token' as any);
      sinon.stub(jwt, 'verify').returns({ walletAddress: 'test-wallet' } as any);

      await generateAuthToken(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith({
        token: 'mock-token',
        expiresIn: 3600
      })).to.be.true;
      expect(cookieStub.calledOnce).to.be.true;
      expect(mockUser.save.calledOnce).to.be.true;
    });

    it('should create new user if wallet address does not exist', async () => {
      const mockUser = {
        walletAddress: 'new-wallet',
        createdAt: sinon.match.date,
        lastLoginAt: sinon.match.date,
        save: sinon.stub().resolves()
      };

      req.body = { walletAddress: 'new-wallet' };
      sinon.stub(User, 'findOne').resolves(null);
      const userConstructorStub = sinon.stub(User.prototype, 'save').resolves();
      sinon.stub(jwt, 'sign').returns('mock-token' as any);
      sinon.stub(jwt, 'verify').returns({ walletAddress: 'new-wallet' } as any);

      await generateAuthToken(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith({
        token: 'mock-token',
        expiresIn: 3600
      })).to.be.true;
      expect(userConstructorStub.calledOnce).to.be.true;
    });

    it('should return 400 if wallet address is missing', async () => {
      req.body = {};

      await generateAuthToken(req as Request, res as Response);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Wallet address is required' })).to.be.true;
    });

    it('should update lastLoginAt for existing user', async () => {
      const mockUser = {
        walletAddress: 'test-wallet',
        lastLoginAt: new Date('2023-01-01'),
        save: sinon.stub().resolves()
      };

      req.body = { walletAddress: 'test-wallet' };
      sinon.stub(User, 'findOne').resolves(mockUser);
      sinon.stub(jwt, 'sign').returns('mock-token' as any);
      sinon.stub(jwt, 'verify').returns({ walletAddress: 'test-wallet' } as any);

      await generateAuthToken(req as Request, res as Response);

      expect(mockUser.lastLoginAt).to.not.equal(new Date('2023-01-01'));
      expect(mockUser.save.calledOnce).to.be.true;
    });

    it('should set httpOnly refresh token cookie', async () => {
      const mockUser = {
        walletAddress: 'test-wallet',
        lastLoginAt: new Date(),
        save: sinon.stub().resolves()
      };

      req.body = { walletAddress: 'test-wallet' };
      sinon.stub(User, 'findOne').resolves(mockUser);
      sinon.stub(jwt, 'sign').returns('mock-token' as any);
      sinon.stub(jwt, 'verify').returns({ walletAddress: 'test-wallet' } as any);

      await generateAuthToken(req as Request, res as Response);

      expect(cookieStub.calledWith('refresh_token', 'mock-token', {
        httpOnly: true,
        secure: false, // NODE_ENV is not production in tests
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000,
        path: '/'
      })).to.be.true;
    });

    it('should return 500 if JWT_SECRET is not defined', async () => {
      delete process.env.JWT_SECRET;
      req.body = { walletAddress: 'test-wallet' };

      await generateAuthToken(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith(sinon.match.has('message'))).to.be.true;
    });

    it('should handle database errors gracefully', async () => {
      req.body = { walletAddress: 'test-wallet' };
      sinon.stub(User, 'findOne').rejects(new Error('Database error'));

      await generateAuthToken(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Database error' })).to.be.true;
    });
  });

  describe('refreshToken', () => {
    it('should generate new access token with valid refresh token', async () => {
      req.cookies = { refresh_token: 'valid-refresh-token' };
      sinon.stub(jwt, 'verify').returns({ walletAddress: 'test-wallet', type: 'refresh' } as any);
      sinon.stub(jwt, 'sign').returns('new-access-token' as any);

      await refreshToken(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith({
        token: 'new-access-token',
        expiresIn: 3600
      })).to.be.true;
    });

    it('should return 401 if refresh token is missing', async () => {
      req.cookies = {};

      await refreshToken(req as Request, res as Response);

      expect(statusStub.calledWith(401)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Refresh token not found' })).to.be.true;
    });

    it('should return 401 if token type is not refresh', async () => {
      req.cookies = { refresh_token: 'access-token' };
      sinon.stub(jwt, 'verify').returns({ walletAddress: 'test-wallet', type: 'access' } as any);

      await refreshToken(req as Request, res as Response);

      expect(statusStub.calledWith(401)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Invalid token type' })).to.be.true;
    });

    it('should return 500 if JWT_SECRET is not defined', async () => {
      delete process.env.JWT_SECRET;
      req.cookies = { refresh_token: 'valid-refresh-token' };

      await refreshToken(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith(sinon.match.has('message'))).to.be.true;
    });

    it('should handle invalid JWT tokens', async () => {
      req.cookies = { refresh_token: 'invalid-token' };
      const jwtError = new Error('Invalid token');
      jwtError.name = 'JsonWebTokenError';
      sinon.stub(jwt, 'verify').throws(jwtError);

      await refreshToken(req as Request, res as Response);

      expect(statusStub.calledWith(401)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Invalid refresh token' })).to.be.true;
    });

    it('should return 401 if token is expired', async () => {
      req.cookies = { refresh_token: 'expired-token' };
      const expiredError = new Error('Token expired');
      expiredError.name = 'TokenExpiredError';
      sinon.stub(jwt, 'verify').throws(expiredError);

      await refreshToken(req as Request, res as Response);

      expect(statusStub.calledWith(401)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Refresh token expired' })).to.be.true;
    });
  });

  describe('logout', () => {
    it('should clear refresh token cookie', async () => {
      req.cookies = { refresh_token: 'some-token' };

      await logout(req as Request, res as Response);

      expect(clearCookieStub.calledWith('refresh_token', {
        httpOnly: true,
        secure: false,
        sameSite: 'strict',
        path: '/'
      })).to.be.true;
    });

    it('should return success response', async () => {
      req.cookies = { refresh_token: 'some-token' };

      await logout(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith({ success: true })).to.be.true;
    });

    it('should handle missing refresh token gracefully', async () => {
      req.cookies = {};

      await logout(req as Request, res as Response);

      expect(statusStub.calledWith(200)).to.be.true;
      expect(jsonStub.calledWith({ success: true })).to.be.true;
    });

    it('should handle errors during logout', async () => {
      req.cookies = { refresh_token: 'some-token' };
      clearCookieStub.throws(new Error('Cookie error'));

      await logout(req as Request, res as Response);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ message: 'Cookie error' })).to.be.true;
    });
  });
});
