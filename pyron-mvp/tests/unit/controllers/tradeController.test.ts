import { expect } from 'chai';
import sinon from 'sinon';
import { Request, Response } from 'express';
import { getPosition, getAccountActivity, getAccountAssets, getAccountPositions } from '../../../controller/tradeController';
import Agent from '../../../databaseModels/agent';
import * as getClientModule from '../../../trade/drift/getClient';
import * as getPriceModule from '../../../trade/drift/getPerpPrice';
import * as getMarketIdModule from '../../../utils/getMarketId';
import * as createKeypairModule from '../../../utils/createKeypairFromSecretKey';
import { Connection, PublicKey, Keypair } from '@solana/web3.js';
import { BN } from 'bn.js';

describe('Trade Controller', () => {
  let req: any;
  let res: any;
  let statusStub: sinon.SinonStub;
  let jsonStub: sinon.SinonStub;
  let mockDriftClient: any;
  let mockUser: any;
  let mockAgent: any;

  beforeEach(() => {
    // Setup request and response mocks with valid Solana public key
    req = {
      query: {},
      user: { walletAddress: '********************************' }
    };
    jsonStub = sinon.stub();
    statusStub = sinon.stub().returns({ json: jsonStub });
    res = {
      status: statusStub,
      json: jsonStub
    };

    // Mock agent data with valid Solana public key
    mockAgent = {
      _id: 'agent-id',
      botId: 'test-bot-id',
      pubkey: '********************************',
      assetPair: 'SOL',
      number: 0,
      agentName: 'Test Agent',
      deposit: 1000
    };

    // Mock Drift user
    mockUser = {
      getPerpPosition: sinon.stub(),
      fetchAccounts: sinon.stub().resolves(),
      getTotalAssetValue: sinon.stub().returns(new BN(**********)), // 1000 USDC
      getTotalAllTimePnl: sinon.stub().returns(new BN(********)), // 50 USDC
      userAccountPublicKey: 'mock-user-key'
    };

    // Mock Drift client
    mockDriftClient = {
      getUser: sinon.stub().returns(mockUser),
      subscribe: sinon.stub().resolves(true),
      unsubscribe: sinon.stub().resolves(true)
    };

    // Setup stubs
    sinon.stub(Agent, 'findOne');
    sinon.stub(Agent, 'find');
    sinon.stub(getClientModule, 'createClient').resolves(mockDriftClient);
    sinon.stub(getPriceModule, 'getCurrentPrice').resolves(100);
    sinon.stub(getMarketIdModule, 'getMarketId').returns(0);
    sinon.stub(createKeypairModule, 'createKeypairFromSecretKey').returns(
      Keypair.generate()
    );
    sinon.stub(global, 'fetch').resolves({
      json: sinon.stub().resolves([])
    } as any);

    // Mock environment variables with valid Solana secret key (64 bytes in base58)
    process.env.ADMIN_KEY = '4Z7cXSyeFR8wNGMVXUE1TwtKn5D5Vu7FzEv69dokLv7KrQk7h6pu4LF8ZRR9yQBhc7uSM9PiLrVZca7s5MQanG5Z';
    process.env.RPC_URL = 'https://api.mainnet-beta.solana.com';
  });

  afterEach(() => {
    sinon.restore();
    delete process.env.ADMIN_KEY;
    delete process.env.RPC_URL;
  });

  describe('getPosition', () => {
    it('should return 400 for missing agentId', async () => {
      req.query = {};

      await getPosition(req, res);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({
        error: 'agentId is required and must be a string'
      })).to.be.true;
    });

    it('should return 400 for non-string agentId', async () => {
      req.query = { agentId: 123 as any };

      await getPosition(req, res);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({
        error: 'agentId is required and must be a string'
      })).to.be.true;
    });

    it('should return 400 for non-existent agent', async () => {
      req.query = { agentId: 'non-existent-id' };
      (Agent.findOne as sinon.SinonStub).resolves(null);

      await getPosition(req, res);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ error: 'Agent not found' })).to.be.true;
    });

    it('should return 403 for unauthorized user access', async () => {
      req.query = { agentId: 'test-bot-id' };
      req.user = { walletAddress: '22222222222222222222222222222223' };
      (Agent.findOne as sinon.SinonStub).resolves(mockAgent);

      await getPosition(req, res);

      expect(statusStub.calledWith(403)).to.be.true;
      expect(jsonStub.calledWith({
        error: 'Forbidden: You do not have permission to access this agent data'
      })).to.be.true;
    });

    it('should handle missing environment variables', async () => {
      delete process.env.ADMIN_KEY;
      req.query = { agentId: 'test-bot-id' };
      (Agent.findOne as sinon.SinonStub).resolves(mockAgent);

      await getPosition(req, res);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ error: 'ADMIN_KEY and RPC_URL must be set in the environment variables' })).to.be.true;
    });

    it('should handle drift client creation errors', async () => {
      req.query = { agentId: 'test-bot-id' };
      (Agent.findOne as sinon.SinonStub).resolves(mockAgent);
      (getClientModule.createClient as sinon.SinonStub).rejects(new Error('Connection failed'));

      await getPosition(req, res);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ error: 'Connection failed' })).to.be.true;
    });
  });

  describe('getAccountActivity', () => {
    it('should return 400 for missing pubkey parameter', async () => {
      req.query = {};

      await getAccountActivity(req, res);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ error: 'pubkey is required and must be a string' })).to.be.true;
    });

    it('should return 403 for unauthorized user access', async () => {
      req.query = { pubkey: '********************************' };
      req.user = { walletAddress: '22222222222222222222222222222223' };

      await getAccountActivity(req, res);

      expect(statusStub.calledWith(403)).to.be.true;
      expect(jsonStub.calledWith({ error: 'Forbidden: You do not have permission to access this user data' })).to.be.true;
    });

    it('should handle drift client creation errors', async () => {
      req.query = { pubkey: '********************************' };
      (getClientModule.createClient as sinon.SinonStub).rejects(new Error('Connection failed'));

      await getAccountActivity(req, res);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ error: 'Connection failed' })).to.be.true;
    });
  });

  describe('getAccountAssets', () => {
    it('should return 400 for missing pubkey parameter', async () => {
      req.query = {};

      await getAccountAssets(req, res);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ error: 'pubkey is required and must be a string' })).to.be.true;
    });

    it('should return 403 for unauthorized user access', async () => {
      req.query = { pubkey: '********************************' };
      req.user = { walletAddress: '22222222222222222222222222222223' };

      await getAccountAssets(req, res);

      expect(statusStub.calledWith(403)).to.be.true;
      expect(jsonStub.calledWith({ error: 'Forbidden: You do not have permission to access this user data' })).to.be.true;
    });

    it('should handle drift client creation errors', async () => {
      req.query = { pubkey: '********************************' };
      (getClientModule.createClient as sinon.SinonStub).rejects(new Error('Connection failed'));

      await getAccountAssets(req, res);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ error: 'Connection failed' })).to.be.true;
    });
  });

  describe('getAccountPositions', () => {
    it('should return 400 for missing pubkey parameter', async () => {
      req.query = {};

      await getAccountPositions(req, res);

      expect(statusStub.calledWith(400)).to.be.true;
      expect(jsonStub.calledWith({ error: 'pubkey is required and must be a string' })).to.be.true;
    });

    it('should return 403 for unauthorized user access', async () => {
      req.query = { pubkey: '********************************' };
      req.user = { walletAddress: '22222222222222222222222222222223' };

      await getAccountPositions(req, res);

      expect(statusStub.calledWith(403)).to.be.true;
      expect(jsonStub.calledWith({ error: 'Forbidden: You do not have permission to access this user data' })).to.be.true;
    });

    it('should handle drift client creation errors', async () => {
      req.query = { pubkey: '********************************' };
      (getClientModule.createClient as sinon.SinonStub).rejects(new Error('Connection failed'));

      await getAccountPositions(req, res);

      expect(statusStub.calledWith(500)).to.be.true;
      expect(jsonStub.calledWith({ error: 'Connection failed' })).to.be.true;
    });
  });
});
