import { expect } from 'chai';
import sinon from 'sinon';
import mongoose from 'mongoose';
import ChatMessage from '../../../databaseModels/chatMessage';
import { validChatMessageData, invalidChatMessageData } from '../../fixtures/mockData';

describe('ChatMessage Model', () => {
  beforeEach(() => {
    // Mock mongoose connection for unit tests
    sinon.stub(mongoose, 'connect').resolves();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Schema Validation', () => {
    it('should create chat message with valid data', () => {
      const chatMessage = new ChatMessage(validChatMessageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.be.undefined;
      expect(chatMessage.chatId.toString()).to.equal(validChatMessageData.chatId);
      expect(chatMessage.type).to.equal(validChatMessageData.type);
      expect(chatMessage.content).to.equal(validChatMessageData.content);
      expect(chatMessage.metadata).to.deep.equal(validChatMessageData.metadata);
    });

    it('should require chatId field', () => {
      const messageData: any = { ...validChatMessageData };
      delete messageData.chatId;

      const chatMessage = new ChatMessage(messageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.chatId).to.exist;
    });

    it('should require type field', () => {
      const messageData: any = { ...validChatMessageData };
      delete messageData.type;

      const chatMessage = new ChatMessage(messageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.type).to.exist;
    });

    it('should require content field', () => {
      const messageData: any = { ...validChatMessageData };
      delete messageData.content;

      const chatMessage = new ChatMessage(messageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.content).to.exist;
    });

    it('should reject empty content', () => {
      const chatMessage = new ChatMessage(invalidChatMessageData.emptyContent);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.content).to.exist;
    });
  });

  describe('Default Values', () => {
    it('should set default timestamp', () => {
      const chatMessage = new ChatMessage(validChatMessageData);

      expect(chatMessage.timestamp).to.be.a('date');
      expect(chatMessage.timestamp.getTime()).to.be.closeTo(Date.now(), 1000);
    });

    it('should allow metadata to be undefined', () => {
      const messageData: any = { ...validChatMessageData };
      delete messageData.metadata;

      const chatMessage = new ChatMessage(messageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.be.undefined;
      expect(chatMessage.metadata).to.be.undefined;
    });
  });

  describe('Data Type Validation', () => {
    it('should validate ObjectId for chatId', () => {
      const validObjectId = new mongoose.Types.ObjectId();
      const messageData = { ...validChatMessageData, chatId: validObjectId };

      const chatMessage = new ChatMessage(messageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.be.undefined;
      expect(chatMessage.chatId).to.be.instanceOf(mongoose.Types.ObjectId);
      expect(chatMessage.chatId.toString()).to.equal(validObjectId.toString());
    });

    it('should validate string fields', () => {
      const chatMessage = new ChatMessage(validChatMessageData);

      expect(chatMessage.type).to.be.a('string');
      expect(chatMessage.content).to.be.a('string');
    });

    it('should validate date field', () => {
      const chatMessage = new ChatMessage(validChatMessageData);

      expect(chatMessage.timestamp).to.be.a('date');
    });

    it('should accept mixed metadata type', () => {
      const messageData = {
        ...validChatMessageData,
        metadata: {
          sender: 'user',
          messageId: 123,
          isEdited: false,
          attachments: ['file1.pdf', 'image.jpg'],
          reactions: { like: 5, dislike: 1 }
        }
      };

      const chatMessage = new ChatMessage(messageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.be.undefined;
      expect(chatMessage.metadata).to.deep.equal(messageData.metadata);
    });
  });

  describe('Message Type Validation', () => {
    it('should accept common message types', () => {
      const validTypes = ['user', 'bot', 'system', 'agent', 'notification'];

      validTypes.forEach(type => {
        const messageData = { ...validChatMessageData, type };
        const chatMessage = new ChatMessage(messageData);
        const validationError = chatMessage.validateSync();

        expect(validationError).to.be.undefined;
        expect(chatMessage.type).to.equal(type);
      });
    });

    it('should accept custom message types', () => {
      const messageData = { ...validChatMessageData, type: 'custom-type' };
      const chatMessage = new ChatMessage(messageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.be.undefined;
      expect(chatMessage.type).to.equal('custom-type');
    });
  });

  describe('Chat Reference', () => {
    it('should reference Chat model', () => {
      const chatMessage = new ChatMessage(validChatMessageData);

      // Check that the schema has the correct reference
      const chatIdPath = ChatMessage.schema.paths.chatId as any;
      expect(chatIdPath.options.ref).to.equal('Chat');
      expect(chatIdPath.instance).to.equal('ObjectId');
    });
  });

  describe('Minimal Valid Message', () => {
    it('should create message with only required fields', () => {
      const minimalData = {
        chatId: new mongoose.Types.ObjectId(),
        type: 'user',
        content: 'Test message'
      };

      const chatMessage = new ChatMessage(minimalData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.be.undefined;
      expect(chatMessage.chatId).to.equal(minimalData.chatId);
      expect(chatMessage.type).to.equal(minimalData.type);
      expect(chatMessage.content).to.equal(minimalData.content);
      expect(chatMessage.metadata).to.be.undefined;
      expect(chatMessage.timestamp).to.be.a('date');
    });
  });

  describe('Content Validation', () => {
    it('should accept long content', () => {
      const longContent = 'A'.repeat(10000); // 10KB of text
      const messageData = { ...validChatMessageData, content: longContent };

      const chatMessage = new ChatMessage(messageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.be.undefined;
      expect(chatMessage.content).to.equal(longContent);
    });

    it('should accept content with special characters', () => {
      const specialContent = 'Test message with émojis 🚀💰 and special chars: @#$%^&*()';
      const messageData = { ...validChatMessageData, content: specialContent };

      const chatMessage = new ChatMessage(messageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.be.undefined;
      expect(chatMessage.content).to.equal(specialContent);
    });

    it('should accept JSON-like content', () => {
      const jsonContent = JSON.stringify({
        action: 'trade',
        symbol: 'SOL-PERP',
        amount: 100,
        side: 'buy'
      });
      const messageData = { ...validChatMessageData, content: jsonContent };

      const chatMessage = new ChatMessage(messageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.be.undefined;
      expect(chatMessage.content).to.equal(jsonContent);
    });
  });

  describe('Metadata Flexibility', () => {
    it('should handle complex metadata structures', () => {
      const complexMetadata = {
        messageContext: {
          threadId: 'thread-123',
          replyToId: 'msg-456',
          isThreadStart: false
        },
        formatting: {
          bold: [0, 5],
          italic: [10, 15],
          links: [{ start: 20, end: 30, url: 'https://example.com' }]
        },
        analytics: {
          readTime: 1500,
          sentiment: 'positive',
          keywords: ['trading', 'profit']
        }
      };

      const messageData = { ...validChatMessageData, metadata: complexMetadata };
      const chatMessage = new ChatMessage(messageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.be.undefined;
      expect(chatMessage.metadata).to.deep.equal(complexMetadata);
    });

    it('should handle null metadata', () => {
      const messageData = { ...validChatMessageData, metadata: null };
      const chatMessage = new ChatMessage(messageData);
      const validationError = chatMessage.validateSync();

      expect(validationError).to.be.undefined;
      expect(chatMessage.metadata).to.be.null;
    });
  });
});
