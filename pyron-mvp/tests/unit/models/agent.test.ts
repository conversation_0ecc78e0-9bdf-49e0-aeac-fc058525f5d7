import { expect } from 'chai';
import sinon from 'sinon';
import mongoose from 'mongoose';
import Agent, { IAgent } from '../../../databaseModels/agent';
import { validAgentData, invalidAgentData } from '../../fixtures/mockData';

describe('Agent Model', () => {
  beforeEach(() => {
    // Mock mongoose connection for unit tests
    sinon.stub(mongoose, 'connect').resolves();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Schema Validation', () => {
    it('should create agent with valid data', () => {
      const agent = new Agent(validAgentData);
      const validationError = agent.validateSync();

      expect(validationError).to.be.undefined;
      expect(agent.agentName).to.equal(validAgentData.agentName);
      expect(agent.assetPair).to.equal(validAgentData.assetPair);
      expect(agent.pubkey).to.equal(validAgentData.pubkey);
      expect(agent.botId).to.equal(validAgentData.botId);
      expect(agent.deposit).to.equal(validAgentData.deposit);
    });

    it('should require agentName field', () => {
      const agentData: any = { ...validAgentData };
      delete agentData.agentName;

      const agent = new Agent(agentData);
      const validationError = agent.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.agentName).to.exist;
    });

    it('should require assetPair field', () => {
      const agentData: any = { ...validAgentData };
      delete agentData.assetPair;

      const agent = new Agent(agentData);
      const validationError = agent.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.assetPair).to.exist;
    });

    it('should require pubkey field', () => {
      const agentData: any = { ...validAgentData };
      delete agentData.pubkey;

      const agent = new Agent(agentData);
      const validationError = agent.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.pubkey).to.exist;
    });

    it('should require botId field', () => {
      const agentData: any = { ...validAgentData };
      delete agentData.botId;

      const agent = new Agent(agentData);
      const validationError = agent.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.botId).to.exist;
    });

    it('should require deposit field', () => {
      const agentData: any = { ...validAgentData };
      delete agentData.deposit;

      const agent = new Agent(agentData);
      const validationError = agent.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.deposit).to.exist;
    });
  });

  describe('Default Values', () => {
    it('should set default hypothesisStatus to "off"', () => {
      const agentData: any = { ...validAgentData };
      delete agentData.hypothesisStatus;

      const agent = new Agent(agentData);

      expect(agent.hypothesisStatus).to.equal('off');
    });

    it('should set default signals object', () => {
      const agentData: any = { ...validAgentData };
      delete agentData.signals;

      const agent = new Agent(agentData);

      expect(agent.signals).to.be.an('object');
      expect(agent.signals.sell).to.equal(0);
      expect(agent.signals.buy).to.equal(0);
      expect(agent.signals.sellOpenBar).to.equal(0);
      expect(agent.signals.buyOpenBar).to.equal(0);
    });

    it('should allow optional fields to be undefined', () => {
      const minimalData = {
        agentName: 'Test Agent',
        assetPair: 'SOL-PERP',
        pubkey: 'test-pubkey',
        botId: 'test-bot-id',
        deposit: 1000
      };

      const agent = new Agent(minimalData);
      const validationError = agent.validateSync();

      expect(validationError).to.be.undefined;
      expect(agent.buySignalMessage).to.be.undefined;
      expect(agent.sellSignalMessage).to.be.undefined;
      expect(agent.timestamps).to.be.undefined;
      expect(agent.tradingStatus).to.be.undefined;
      expect(agent.number).to.be.undefined;
      expect(agent.chatId).to.be.undefined;
    });
  });

  describe('Data Type Validation', () => {
    it('should validate string fields', () => {
      const agent = new Agent(validAgentData);

      expect(agent.agentName).to.be.a('string');
      expect(agent.assetPair).to.be.a('string');
      expect(agent.pubkey).to.be.a('string');
      expect(agent.botId).to.be.a('string');
    });

    it('should validate number fields', () => {
      const agent = new Agent(validAgentData);

      expect(agent.deposit).to.be.a('number');
      if (agent.number !== undefined) {
        expect(agent.number).to.be.a('number');
      }
    });

    it('should validate signals structure', () => {
      const agent = new Agent(validAgentData);

      expect(agent.signals).to.be.an('object');
      expect(agent.signals.sell).to.be.a('number');
      expect(agent.signals.buy).to.be.a('number');
      expect(agent.signals.sellOpenBar).to.be.a('number');
      expect(agent.signals.buyOpenBar).to.be.a('number');
    });

    it('should handle invalid deposit type', () => {
      const agentData = { ...validAgentData, deposit: 'invalid-number' as any };

      const agent = new Agent(agentData);
      const validationError = agent.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.deposit).to.exist;
    });
  });

  describe('Signal Structure Validation', () => {
    it('should accept valid signals object', () => {
      const agentData = {
        ...validAgentData,
        signals: {
          sell: 5,
          buy: 3,
          sellOpenBar: 2,
          buyOpenBar: 1
        }
      };

      const agent = new Agent(agentData);
      const validationError = agent.validateSync();

      expect(validationError).to.be.undefined;
      expect(agent.signals.sell).to.equal(5);
      expect(agent.signals.buy).to.equal(3);
      expect(agent.signals.sellOpenBar).to.equal(2);
      expect(agent.signals.buyOpenBar).to.equal(1);
    });

    it('should handle partial signals object', () => {
      const agentData = {
        ...validAgentData,
        signals: {
          sell: 2,
          buy: 1
          // Missing sellOpenBar and buyOpenBar
        }
      };

      const agent = new Agent(agentData);
      const validationError = agent.validateSync();

      expect(validationError).to.be.undefined;
      expect(agent.signals.sell).to.equal(2);
      expect(agent.signals.buy).to.equal(1);
    });

    it('should not have _id in signals subdocument', () => {
      const agent = new Agent(validAgentData);

      expect((agent.signals as any)._id).to.be.undefined;
    });
  });

  describe('Confirmation Fields', () => {
    it('should accept all confirmation fields', () => {
      const agentData = {
        ...validAgentData,
        requiredBuyConfirmationsOpen: 2,
        requiredSellConfirmationsOpen: 2,
        requiredBuyConfirmationsOverride: 1,
        requiredSellConfirmationsOverride: 1,
        requiredBuyConfirmationsClose: 1,
        requiredSellConfirmationsClose: 1,
        requiredBuyConfirmationsResetCounter: 3,
        requiredSellConfirmationsResetCounter: 3,
        requiredBuyConfirmationsOpenBar: 1,
        requiredSellConfirmationsOpenBar: 1
      };

      const agent = new Agent(agentData);
      const validationError = agent.validateSync();

      expect(validationError).to.be.undefined;
      expect(agent.requiredBuyConfirmationsOpen).to.equal(2);
      expect(agent.requiredSellConfirmationsOpen).to.equal(2);
      expect(agent.requiredBuyConfirmationsOverride).to.equal(1);
      expect(agent.requiredSellConfirmationsOverride).to.equal(1);
    });

    it('should allow confirmation fields to be optional', () => {
      const minimalData = {
        agentName: 'Test Agent',
        assetPair: 'SOL-PERP',
        pubkey: 'test-pubkey',
        botId: 'test-bot-id',
        deposit: 1000
      };

      const agent = new Agent(minimalData);
      const validationError = agent.validateSync();

      expect(validationError).to.be.undefined;
      expect(agent.requiredBuyConfirmationsOpen).to.be.undefined;
      expect(agent.requiredSellConfirmationsOpen).to.be.undefined;
    });
  });

  describe('Prompt Fields', () => {
    it('should accept buy and sell report prompts', () => {
      const agentData = {
        ...validAgentData,
        buyReportPrompt: 'Buy report prompt text',
        sellReportPrompt: 'Sell report prompt text'
      };

      const agent = new Agent(agentData);
      const validationError = agent.validateSync();

      expect(validationError).to.be.undefined;
      expect(agent.buyReportPrompt).to.equal('Buy report prompt text');
      expect(agent.sellReportPrompt).to.equal('Sell report prompt text');
    });

    it('should allow prompt fields to be optional', () => {
      const agent = new Agent(validAgentData);
      const validationError = agent.validateSync();

      expect(validationError).to.be.undefined;
      expect(agent.buyReportPrompt).to.be.undefined;
      expect(agent.sellReportPrompt).to.be.undefined;
    });
  });
});
