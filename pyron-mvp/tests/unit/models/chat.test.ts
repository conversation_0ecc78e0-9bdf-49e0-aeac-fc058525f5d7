import { expect } from 'chai';
import sinon from 'sinon';
import mongoose from 'mongoose';
import Chat from '../../../databaseModels/chat';
import { validChatData, invalidChatData } from '../../fixtures/mockData';

describe('Chat Model', () => {
  beforeEach(() => {
    // Mock mongoose connection for unit tests
    sinon.stub(mongoose, 'connect').resolves();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Schema Validation', () => {
    it('should create chat with valid data', () => {
      const chat = new Chat(validChatData);
      const validationError = chat.validateSync();

      expect(validationError).to.be.undefined;
      expect(chat.walletAddress).to.equal(validChatData.walletAddress);
      expect(chat.title).to.equal(validChatData.title);
      expect(chat.chatType).to.equal(validChatData.chatType);
      expect(chat.metadata).to.deep.equal(validChatData.metadata);
    });

    it('should require walletAddress field', () => {
      const chatData: any = { ...validChatData };
      delete chatData.walletAddress;

      const chat = new Chat(chatData);
      const validationError = chat.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.walletAddress).to.exist;
    });

    it('should require title field', () => {
      const chatData: any = { ...validChatData };
      delete chatData.title;

      const chat = new Chat(chatData);
      const validationError = chat.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.title).to.exist;
    });

    it('should reject empty walletAddress', () => {
      const chat = new Chat(invalidChatData.emptyWalletAddress);
      const validationError = chat.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.walletAddress).to.exist;
    });

    it('should reject empty title', () => {
      const chat = new Chat(invalidChatData.emptyTitle);
      const validationError = chat.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.title).to.exist;
    });
  });

  describe('Default Values', () => {
    it('should set default chatType to "general"', () => {
      const chatData: any = { ...validChatData };
      delete chatData.chatType;

      const chat = new Chat(chatData);

      expect(chat.chatType).to.equal('general');
    });

    it('should set createdAt and updatedAt timestamps', () => {
      const chat = new Chat(validChatData);

      expect(chat.createdAt).to.be.a('date');
      expect(chat.updatedAt).to.be.a('date');
    });

    it('should allow metadata to be undefined', () => {
      const chatData: any = { ...validChatData };
      delete chatData.metadata;

      const chat = new Chat(chatData);
      const validationError = chat.validateSync();

      expect(validationError).to.be.undefined;
      expect(chat.metadata).to.be.undefined;
    });
  });

  describe('Data Type Validation', () => {
    it('should validate string fields', () => {
      const chat = new Chat(validChatData);

      expect(chat.walletAddress).to.be.a('string');
      expect(chat.title).to.be.a('string');
      expect(chat.chatType).to.be.a('string');
    });

    it('should validate date fields', () => {
      const chat = new Chat(validChatData);

      expect(chat.createdAt).to.be.a('date');
      expect(chat.updatedAt).to.be.a('date');
    });

    it('should accept mixed metadata type', () => {
      const chatData = {
        ...validChatData,
        metadata: {
          stringField: 'test',
          numberField: 123,
          booleanField: true,
          arrayField: [1, 2, 3],
          objectField: { nested: 'value' }
        }
      };

      const chat = new Chat(chatData);
      const validationError = chat.validateSync();

      expect(validationError).to.be.undefined;
      expect(chat.metadata).to.deep.equal(chatData.metadata);
    });
  });

  describe('Chat Type Validation', () => {
    it('should accept valid chat types', () => {
      const validTypes = ['general', 'trading', 'support', 'agent'];

      validTypes.forEach(type => {
        const chatData = { ...validChatData, chatType: type };
        const chat = new Chat(chatData);
        const validationError = chat.validateSync();

        expect(validationError).to.be.undefined;
        expect(chat.chatType).to.equal(type);
      });
    });

    it('should accept custom chat types', () => {
      const chatData = { ...validChatData, chatType: 'custom-type' };
      const chat = new Chat(chatData);
      const validationError = chat.validateSync();

      expect(validationError).to.be.undefined;
      expect(chat.chatType).to.equal('custom-type');
    });
  });

  describe('Minimal Valid Chat', () => {
    it('should create chat with only required fields', () => {
      const minimalData = {
        walletAddress: 'test-wallet-address',
        title: 'Test Chat'
      };

      const chat = new Chat(minimalData);
      const validationError = chat.validateSync();

      expect(validationError).to.be.undefined;
      expect(chat.walletAddress).to.equal(minimalData.walletAddress);
      expect(chat.title).to.equal(minimalData.title);
      expect(chat.chatType).to.equal('general'); // default value
      expect(chat.metadata).to.be.undefined;
      expect(chat.createdAt).to.be.a('date');
      expect(chat.updatedAt).to.be.a('date');
    });
  });

  describe('Metadata Flexibility', () => {
    it('should handle complex metadata structures', () => {
      const complexMetadata = {
        agentConfig: {
          tradingPairs: ['SOL-PERP', 'BTC-PERP'],
          riskLevel: 'medium',
          autoTrade: true
        },
        userPreferences: {
          notifications: true,
          theme: 'dark'
        },
        sessionData: {
          lastActivity: new Date(),
          messageCount: 42
        }
      };

      const chatData = { ...validChatData, metadata: complexMetadata };
      const chat = new Chat(chatData);
      const validationError = chat.validateSync();

      expect(validationError).to.be.undefined;
      expect(chat.metadata).to.deep.equal(complexMetadata);
    });

    it('should handle null metadata', () => {
      const chatData = { ...validChatData, metadata: null };
      const chat = new Chat(chatData);
      const validationError = chat.validateSync();

      expect(validationError).to.be.undefined;
      expect(chat.metadata).to.be.null;
    });
  });
});
