import { expect } from 'chai';
import sinon from 'sinon';
import mongoose from 'mongoose';
import Log, { ILog } from '../../../databaseModels/log';
import { validLogData, invalidLogData } from '../../fixtures/mockData';

describe('Log Model', () => {
  beforeEach(() => {
    // Mock mongoose connection for unit tests
    sinon.stub(mongoose, 'connect').resolves();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Schema Validation', () => {
    it('should create log with valid data', () => {
      const log = new Log(validLogData);
      const validationError = log.validateSync();

      expect(validationError).to.be.undefined;
      expect(log.chatId).to.equal(validLogData.chatId);
      expect(log.type).to.equal(validLogData.type);
      expect(log.side).to.equal(validLogData.side);
      expect(log.size).to.equal(validLogData.size);
      expect(log.total).to.equal(validLogData.total);
      expect(log.percentage).to.equal(validLogData.percentage);
      expect(log.market).to.equal(validLogData.market);
      expect(log.status).to.equal(validLogData.status);
      expect(log.shown).to.equal(validLogData.shown);
      expect(log.timestamps).to.equal(validLogData.timestamps);
      expect(log.webhookSignal).to.equal(validLogData.webhookSignal);
    });

    it('should require chatId field', () => {
      const logData: any = { ...validLogData };
      delete logData.chatId;

      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.chatId).to.exist;
    });

    it('should require type field', () => {
      const logData: any = { ...validLogData };
      delete logData.type;

      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.type).to.exist;
    });

    it('should require side field', () => {
      const logData: any = { ...validLogData };
      delete logData.side;

      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.side).to.exist;
    });

    it('should require size field', () => {
      const logData: any = { ...validLogData };
      delete logData.size;

      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.size).to.exist;
    });

    it('should require market field', () => {
      const logData: any = { ...validLogData };
      delete logData.market;

      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.market).to.exist;
    });

    it('should require status field', () => {
      const logData: any = { ...validLogData };
      delete logData.status;

      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.status).to.exist;
    });

    it('should require shown field', () => {
      const logData: any = { ...validLogData };
      delete logData.shown;

      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.shown).to.exist;
    });

    it('should require timestamps field', () => {
      const logData: any = { ...validLogData };
      delete logData.timestamps;

      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.timestamps).to.exist;
    });
  });

  describe('Optional Fields', () => {
    it('should allow total field to be optional', () => {
      const logData: any = { ...validLogData };
      delete logData.total;

      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.be.undefined;
      expect(log.total).to.be.undefined;
    });

    it('should allow percentage field to be optional', () => {
      const logData: any = { ...validLogData };
      delete logData.percentage;

      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.be.undefined;
      expect(log.percentage).to.be.undefined;
    });

    it('should allow webhookSignal field to be optional', () => {
      const logData: any = { ...validLogData };
      delete logData.webhookSignal;

      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.be.undefined;
      expect(log.webhookSignal).to.be.undefined;
    });
  });

  describe('Data Type Validation', () => {
    it('should validate string fields', () => {
      const log = new Log(validLogData);

      expect(log.chatId).to.be.a('string');
      expect(log.type).to.be.a('string');
      expect(log.side).to.be.a('string');
      expect(log.size).to.be.a('string');
      expect(log.market).to.be.a('string');
      expect(log.status).to.be.a('string');
      expect(log.timestamps).to.be.a('string');
    });

    it('should validate number fields', () => {
      const log = new Log(validLogData);

      expect(log.shown).to.be.a('number');
    });

    it('should validate optional string fields when present', () => {
      const log = new Log(validLogData);

      if (log.total !== undefined) {
        expect(log.total).to.be.a('string');
      }
      if (log.percentage !== undefined) {
        expect(log.percentage).to.be.a('string');
      }
      if (log.webhookSignal !== undefined) {
        expect(log.webhookSignal).to.be.a('string');
      }
    });
  });

  describe('Log Type Validation', () => {
    it('should accept common log types', () => {
      const validTypes = ['trade', 'position', 'order', 'signal', 'error', 'info'];

      validTypes.forEach(type => {
        const logData = { ...validLogData, type };
        const log = new Log(logData);
        const validationError = log.validateSync();

        expect(validationError).to.be.undefined;
        expect(log.type).to.equal(type);
      });
    });

    it('should accept custom log types', () => {
      const logData = { ...validLogData, type: 'custom-log-type' };
      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.be.undefined;
      expect(log.type).to.equal('custom-log-type');
    });
  });

  describe('Side Validation', () => {
    it('should accept buy and sell sides', () => {
      const validSides = ['buy', 'sell', 'long', 'short'];

      validSides.forEach(side => {
        const logData = { ...validLogData, side };
        const log = new Log(logData);
        const validationError = log.validateSync();

        expect(validationError).to.be.undefined;
        expect(log.side).to.equal(side);
      });
    });

    it('should accept custom side values', () => {
      const logData = { ...validLogData, side: 'neutral' };
      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.be.undefined;
      expect(log.side).to.equal('neutral');
    });
  });

  describe('Status Validation', () => {
    it('should accept common status values', () => {
      const validStatuses = ['pending', 'completed', 'failed', 'cancelled', 'processing'];

      validStatuses.forEach(status => {
        const logData = { ...validLogData, status };
        const log = new Log(logData);
        const validationError = log.validateSync();

        expect(validationError).to.be.undefined;
        expect(log.status).to.equal(status);
      });
    });

    it('should accept custom status values', () => {
      const logData = { ...validLogData, status: 'custom-status' };
      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.be.undefined;
      expect(log.status).to.equal('custom-status');
    });
  });

  describe('Market Validation', () => {
    it('should accept common market formats', () => {
      const validMarkets = ['SOL-PERP', 'BTC-PERP', 'ETH-PERP', 'USDC-SPOT', 'SOL-USDC'];

      validMarkets.forEach(market => {
        const logData = { ...validLogData, market };
        const log = new Log(logData);
        const validationError = log.validateSync();

        expect(validationError).to.be.undefined;
        expect(log.market).to.equal(market);
      });
    });

    it('should accept custom market formats', () => {
      const logData = { ...validLogData, market: 'CUSTOM-PAIR' };
      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.be.undefined;
      expect(log.market).to.equal('CUSTOM-PAIR');
    });
  });

  describe('Shown Field Validation', () => {
    it('should accept 0 and 1 for shown field', () => {
      [0, 1].forEach(shown => {
        const logData = { ...validLogData, shown };
        const log = new Log(logData);
        const validationError = log.validateSync();

        expect(validationError).to.be.undefined;
        expect(log.shown).to.equal(shown);
      });
    });

    it('should accept other number values for shown field', () => {
      const logData = { ...validLogData, shown: 2 };
      const log = new Log(logData);
      const validationError = log.validateSync();

      expect(validationError).to.be.undefined;
      expect(log.shown).to.equal(2);
    });
  });

  describe('Minimal Valid Log', () => {
    it('should create log with only required fields', () => {
      const minimalData = {
        chatId: 'test-chat-id',
        type: 'trade',
        side: 'buy',
        size: '10.0',
        market: 'SOL-PERP',
        status: 'completed',
        shown: 1,
        timestamps: Date.now().toString()
      };

      const log = new Log(minimalData);
      const validationError = log.validateSync();

      expect(validationError).to.be.undefined;
      expect(log.chatId).to.equal(minimalData.chatId);
      expect(log.type).to.equal(minimalData.type);
      expect(log.side).to.equal(minimalData.side);
      expect(log.size).to.equal(minimalData.size);
      expect(log.market).to.equal(minimalData.market);
      expect(log.status).to.equal(minimalData.status);
      expect(log.shown).to.equal(minimalData.shown);
      expect(log.timestamps).to.equal(minimalData.timestamps);
      expect(log.total).to.be.undefined;
      expect(log.percentage).to.be.undefined;
      expect(log.webhookSignal).to.be.undefined;
    });
  });
});
