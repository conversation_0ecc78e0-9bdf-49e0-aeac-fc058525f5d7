import { expect } from 'chai';
import sinon from 'sinon';
import mongoose from 'mongoose';
import User from '../../../databaseModels/user';
import { validUserData, invalidUserData } from '../../fixtures/mockData';

describe('User Model', () => {
  beforeEach(() => {
    // Mock mongoose connection for unit tests
    sinon.stub(mongoose, 'connect').resolves();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Schema Validation', () => {
    it('should create user with valid data', () => {
      const user = new User(validUserData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.walletAddress).to.equal(validUserData.walletAddress);
      expect(user.displayName).to.equal(validUserData.displayName);
      expect(user.settings).to.deep.equal(validUserData.settings);
      expect(user.apiKeys).to.deep.equal(validUserData.apiKeys);
    });

    it('should require walletAddress field', () => {
      const userData: any = { ...validUserData };
      delete userData.walletAddress;

      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.walletAddress).to.exist;
    });

    it('should reject empty walletAddress', () => {
      const user = new User(invalidUserData.emptyWalletAddress);
      const validationError = user.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.walletAddress).to.exist;
    });

    it('should enforce unique walletAddress constraint', () => {
      const user = new User(validUserData);

      // Check that the schema has unique constraint
      const walletAddressPath = User.schema.paths.walletAddress as any;
      expect(walletAddressPath.options.unique).to.be.true;
    });
  });

  describe('Default Values', () => {
    it('should set default createdAt timestamp', () => {
      const user = new User(validUserData);

      expect(user.createdAt).to.be.a('date');
      expect(user.createdAt.getTime()).to.be.closeTo(Date.now(), 1000);
    });

    it('should set default lastLoginAt timestamp', () => {
      const user = new User(validUserData);

      expect(user.lastLoginAt).to.be.a('date');
      expect(user.lastLoginAt.getTime()).to.be.closeTo(Date.now(), 1000);
    });

    it('should allow displayName to be undefined', () => {
      const userData: any = { ...validUserData };
      delete userData.displayName;

      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.displayName).to.be.undefined;
    });

    it('should allow settings to be undefined', () => {
      const userData: any = { ...validUserData };
      delete userData.settings;

      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.settings).to.be.undefined;
    });

    it('should allow apiKeys to be undefined', () => {
      const userData: any = { ...validUserData };
      delete userData.apiKeys;

      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.apiKeys).to.be.undefined;
    });
  });

  describe('Data Type Validation', () => {
    it('should validate string fields', () => {
      const user = new User(validUserData);

      expect(user.walletAddress).to.be.a('string');
      if (user.displayName !== undefined) {
        expect(user.displayName).to.be.a('string');
      }
    });

    it('should validate date fields', () => {
      const user = new User(validUserData);

      expect(user.createdAt).to.be.a('date');
      expect(user.lastLoginAt).to.be.a('date');
    });

    it('should accept mixed settings type', () => {
      const userData = {
        ...validUserData,
        settings: {
          theme: 'dark',
          notifications: true,
          tradingPreferences: {
            riskLevel: 'medium',
            autoTrade: false,
            maxPositionSize: 1000
          },
          uiPreferences: {
            language: 'en',
            timezone: 'UTC',
            chartType: 'candlestick'
          }
        }
      };

      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.settings).to.deep.equal(userData.settings);
    });

    it('should accept mixed apiKeys type', () => {
      const userData = {
        ...validUserData,
        apiKeys: {
          driftApi: 'drift-api-key-123',
          solanaRpc: 'https://api.mainnet-beta.solana.com',
          webhookSecret: 'webhook-secret-456',
          encryptedKeys: {
            privateKey: 'encrypted-private-key',
            mnemonic: 'encrypted-mnemonic'
          }
        }
      };

      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.apiKeys).to.deep.equal(userData.apiKeys);
    });
  });

  describe('Wallet Address Validation', () => {
    it('should accept valid Solana wallet addresses', () => {
      const validWalletAddresses = [
        'DjVE6JNiYqPL2QXyCUUh8rNjHrbz9hXHNYt99MQ59qw1',
        '********************************',
        'So111111111********************************',
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
      ];

      validWalletAddresses.forEach(walletAddress => {
        const userData = { ...validUserData, walletAddress };
        const user = new User(userData);
        const validationError = user.validateSync();

        expect(validationError).to.be.undefined;
        expect(user.walletAddress).to.equal(walletAddress);
      });
    });

    it('should accept custom wallet address formats', () => {
      const customWalletAddress = 'custom-wallet-format-123';
      const userData = { ...validUserData, walletAddress: customWalletAddress };

      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.walletAddress).to.equal(customWalletAddress);
    });
  });

  describe('Display Name Validation', () => {
    it('should accept various display name formats', () => {
      const validDisplayNames = [
        'John Doe',
        'trader123',
        'Crypto_Trader_2024',
        '<EMAIL>',
        '🚀 Moon Trader 🚀',
        'Alice (Pro Trader)'
      ];

      validDisplayNames.forEach(displayName => {
        const userData = { ...validUserData, displayName };
        const user = new User(userData);
        const validationError = user.validateSync();

        expect(validationError).to.be.undefined;
        expect(user.displayName).to.equal(displayName);
      });
    });

    it('should accept empty string for displayName', () => {
      const userData = { ...validUserData, displayName: '' };
      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.displayName).to.equal('');
    });
  });

  describe('Minimal Valid User', () => {
    it('should create user with only required fields', () => {
      const minimalData = {
        walletAddress: 'test-wallet-address-minimal'
      };

      const user = new User(minimalData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.walletAddress).to.equal(minimalData.walletAddress);
      expect(user.displayName).to.be.undefined;
      expect(user.settings).to.be.undefined;
      expect(user.apiKeys).to.be.undefined;
      expect(user.createdAt).to.be.a('date');
      expect(user.lastLoginAt).to.be.a('date');
    });
  });

  describe('Settings Flexibility', () => {
    it('should handle complex settings structures', () => {
      const complexSettings = {
        ui: {
          theme: 'dark',
          language: 'en',
          timezone: 'America/New_York',
          notifications: {
            email: true,
            push: false,
            sms: true,
            types: ['trades', 'alerts', 'news']
          }
        },
        trading: {
          defaultRiskLevel: 'medium',
          autoTrade: false,
          maxPositionSize: 10000,
          stopLossPercentage: 5,
          takeProfitPercentage: 15,
          preferredMarkets: ['SOL-PERP', 'BTC-PERP', 'ETH-PERP']
        },
        security: {
          twoFactorEnabled: true,
          sessionTimeout: 3600,
          ipWhitelist: ['***********', '********']
        }
      };

      const userData = { ...validUserData, settings: complexSettings };
      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.settings).to.deep.equal(complexSettings);
    });

    it('should handle null settings', () => {
      const userData = { ...validUserData, settings: null };
      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.settings).to.be.null;
    });
  });

  describe('API Keys Flexibility', () => {
    it('should handle complex API key structures', () => {
      const complexApiKeys = {
        exchanges: {
          drift: {
            apiKey: 'drift-api-key',
            secretKey: 'encrypted-secret',
            environment: 'mainnet'
          },
          jupiter: {
            apiKey: 'jupiter-api-key',
            rateLimit: 100
          }
        },
        blockchain: {
          solana: {
            rpcUrl: 'https://api.mainnet-beta.solana.com',
            wsUrl: 'wss://api.mainnet-beta.solana.com',
            commitment: 'confirmed'
          }
        },
        external: {
          telegram: {
            botToken: 'encrypted-bot-token',
            chatId: 'encrypted-chat-id'
          },
          discord: {
            webhookUrl: 'encrypted-webhook-url'
          }
        }
      };

      const userData = { ...validUserData, apiKeys: complexApiKeys };
      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.apiKeys).to.deep.equal(complexApiKeys);
    });

    it('should handle null apiKeys', () => {
      const userData = { ...validUserData, apiKeys: null };
      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.apiKeys).to.be.null;
    });
  });

  describe('Timestamp Handling', () => {
    it('should allow custom createdAt timestamp', () => {
      const customCreatedAt = new Date('2023-01-01T00:00:00.000Z');
      const userData = { ...validUserData, createdAt: customCreatedAt };

      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.createdAt).to.deep.equal(customCreatedAt);
    });

    it('should allow custom lastLoginAt timestamp', () => {
      const customLastLoginAt = new Date('2023-12-01T12:00:00.000Z');
      const userData = { ...validUserData, lastLoginAt: customLastLoginAt };

      const user = new User(userData);
      const validationError = user.validateSync();

      expect(validationError).to.be.undefined;
      expect(user.lastLoginAt).to.deep.equal(customLastLoginAt);
    });
  });
});
