import { expect } from 'chai';
import sinon from 'sinon';
import mongoose from 'mongoose';
import Hypothesis, { IHypothesis } from '../../../databaseModels/hypothesis';
import { validHypothesisData, invalidHypothesisData } from '../../fixtures/mockData';

describe('Hypothesis Model', () => {
  beforeEach(() => {
    // Mock mongoose connection for unit tests
    sinon.stub(mongoose, 'connect').resolves();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Schema Validation', () => {
    it('should create hypothesis with valid data', () => {
      const hypothesis = new Hypothesis(validHypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.be.undefined;
      expect(hypothesis.logId.toString()).to.equal(validHypothesisData.logId);
      expect(hypothesis.hypothesis).to.equal(validHypothesisData.hypothesis);
      expect(hypothesis.date).to.be.a('date');
    });

    it('should require logId field', () => {
      const hypothesisData: any = { ...validHypothesisData };
      delete hypothesisData.logId;

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.logId).to.exist;
    });

    it('should require hypothesis field', () => {
      const hypothesisData: any = { ...validHypothesisData };
      delete hypothesisData.hypothesis;

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.hypothesis).to.exist;
    });

    it('should reject empty hypothesis text', () => {
      const hypothesis = new Hypothesis(invalidHypothesisData.emptyHypothesis);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.not.be.undefined;
      expect(validationError?.errors.hypothesis).to.exist;
    });
  });

  describe('Default Values', () => {
    it('should set default date to current time', () => {
      const hypothesis = new Hypothesis(validHypothesisData);

      expect(hypothesis.date).to.be.a('date');
      expect(hypothesis.date.getTime()).to.be.closeTo(Date.now(), 1000);
    });

    it('should allow custom date to be set', () => {
      const customDate = new Date('2023-01-01T00:00:00.000Z');
      const hypothesisData = { ...validHypothesisData, date: customDate };

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.be.undefined;
      expect(hypothesis.date).to.deep.equal(customDate);
    });
  });

  describe('Data Type Validation', () => {
    it('should validate ObjectId for logId', () => {
      const validObjectId = new mongoose.Types.ObjectId();
      const hypothesisData = { ...validHypothesisData, logId: validObjectId };

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.be.undefined;
      expect(hypothesis.logId).to.be.instanceOf(mongoose.Types.ObjectId);
      expect(hypothesis.logId.toString()).to.equal(validObjectId.toString());
    });

    it('should validate string for hypothesis', () => {
      const hypothesis = new Hypothesis(validHypothesisData);

      expect(hypothesis.hypothesis).to.be.a('string');
    });

    it('should validate date field', () => {
      const hypothesis = new Hypothesis(validHypothesisData);

      expect(hypothesis.date).to.be.a('date');
    });

    it('should convert string ObjectId to ObjectId', () => {
      const stringObjectId = '507f1f77bcf86cd799439011';
      const hypothesisData = { ...validHypothesisData, logId: stringObjectId };

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.be.undefined;
      expect(hypothesis.logId).to.be.instanceOf(mongoose.Types.ObjectId);
      expect(hypothesis.logId.toString()).to.equal(stringObjectId);
    });
  });

  describe('Log Reference', () => {
    it('should reference Log model', () => {
      const hypothesis = new Hypothesis(validHypothesisData);

      // Check that the schema has the correct reference
      const logIdPath = Hypothesis.schema.paths.logId as any;
      expect(logIdPath.options.ref).to.equal('Log');
      expect(logIdPath.instance).to.equal('ObjectId');
    });
  });

  describe('Timestamps', () => {
    it('should have timestamps enabled', () => {
      const hypothesis = new Hypothesis(validHypothesisData);

      // The schema should have timestamps enabled
      expect((Hypothesis.schema as any).options.timestamps).to.be.true;
    });

    it('should set createdAt and updatedAt when saved', () => {
      const hypothesis = new Hypothesis(validHypothesisData);

      // These would be set when actually saving to database
      // For unit tests, we just verify the schema configuration
      expect((hypothesis.schema as any).paths.createdAt).to.exist;
      expect((hypothesis.schema as any).paths.updatedAt).to.exist;
    });
  });

  describe('Hypothesis Content Validation', () => {
    it('should accept long hypothesis text', () => {
      const longHypothesis = 'This is a very long hypothesis that contains detailed analysis of market conditions, trading patterns, and potential outcomes. '.repeat(10);
      const hypothesisData = { ...validHypothesisData, hypothesis: longHypothesis };

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.be.undefined;
      expect(hypothesis.hypothesis).to.equal(longHypothesis);
    });

    it('should accept hypothesis with special characters', () => {
      const specialHypothesis = 'Market analysis: SOL/USDC shows 📈 bullish pattern with 25% probability of reaching $150 by Q4 2024. Risk factors: 🔻 regulatory uncertainty & market volatility.';
      const hypothesisData = { ...validHypothesisData, hypothesis: specialHypothesis };

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.be.undefined;
      expect(hypothesis.hypothesis).to.equal(specialHypothesis);
    });

    it('should accept hypothesis with line breaks', () => {
      const multilineHypothesis = `Market Analysis:
      - Current trend: Bullish
      - Support level: $120
      - Resistance level: $150
      - Recommendation: Hold position`;
      const hypothesisData = { ...validHypothesisData, hypothesis: multilineHypothesis };

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.be.undefined;
      expect(hypothesis.hypothesis).to.equal(multilineHypothesis);
    });

    it('should accept hypothesis with JSON-like content', () => {
      const jsonHypothesis = JSON.stringify({
        prediction: 'bullish',
        confidence: 0.75,
        timeframe: '1 week',
        factors: ['technical analysis', 'market sentiment', 'volume analysis']
      });
      const hypothesisData = { ...validHypothesisData, hypothesis: jsonHypothesis };

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.be.undefined;
      expect(hypothesis.hypothesis).to.equal(jsonHypothesis);
    });
  });

  describe('Minimal Valid Hypothesis', () => {
    it('should create hypothesis with only required fields', () => {
      const minimalData = {
        logId: new mongoose.Types.ObjectId(),
        hypothesis: 'Minimal test hypothesis'
      };

      const hypothesis = new Hypothesis(minimalData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.be.undefined;
      expect(hypothesis.logId).to.equal(minimalData.logId);
      expect(hypothesis.hypothesis).to.equal(minimalData.hypothesis);
      expect(hypothesis.date).to.be.a('date');
    });
  });

  describe('Edge Cases', () => {
    it('should handle very short hypothesis', () => {
      const shortHypothesis = 'Buy';
      const hypothesisData = { ...validHypothesisData, hypothesis: shortHypothesis };

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.be.undefined;
      expect(hypothesis.hypothesis).to.equal(shortHypothesis);
    });

    it('should handle hypothesis with only whitespace as invalid', () => {
      const whitespaceHypothesis = '   \n\t   ';
      const hypothesisData = { ...validHypothesisData, hypothesis: whitespaceHypothesis };

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      // Mongoose doesn't automatically trim and validate non-empty after trim
      // This would depend on custom validation if needed
      expect(validationError).to.be.undefined; // Current schema allows this
      expect(hypothesis.hypothesis).to.equal(whitespaceHypothesis);
    });

    it('should handle future dates', () => {
      const futureDate = new Date(Date.now() + 86400000); // 1 day in future
      const hypothesisData = { ...validHypothesisData, date: futureDate };

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.be.undefined;
      expect(hypothesis.date).to.deep.equal(futureDate);
    });

    it('should handle past dates', () => {
      const pastDate = new Date('2020-01-01T00:00:00.000Z');
      const hypothesisData = { ...validHypothesisData, date: pastDate };

      const hypothesis = new Hypothesis(hypothesisData);
      const validationError = hypothesis.validateSync();

      expect(validationError).to.be.undefined;
      expect(hypothesis.date).to.deep.equal(pastDate);
    });
  });
});
