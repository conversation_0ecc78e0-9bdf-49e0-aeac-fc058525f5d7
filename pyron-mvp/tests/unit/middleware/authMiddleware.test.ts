import { expect } from 'chai';
import sinon from 'sinon';
import jwt from 'jsonwebtoken';
import { authMiddleware, walletOwnershipMiddleware } from '../../../middleware/authMiddleware';

describe('Auth Middleware', () => {
  let req: any;
  let res: any;
  let next: sinon.SinonStub;

  beforeEach(() => {
    req = {
      headers: {},
      params: {},
      user: undefined
    };
    res = {
      status: sinon.stub().returnsThis(),
      json: sinon.stub()
    };
    next = sinon.stub();

    // Set up environment variable
    process.env.JWT_SECRET = 'test-secret';
  });

  afterEach(() => {
    sinon.restore();
    delete process.env.JWT_SECRET;
  });

  describe('authMiddleware', () => {
    it('should authenticate valid token', async () => {
      const mockPayload = { walletAddress: 'test-wallet', type: 'access' };
      req.headers.authorization = 'Bearer valid-token';

      sinon.stub(jwt, 'verify').returns(mockPayload as any);

      authMiddleware(req, res, next);

      expect(req.user).to.deep.equal({ walletAddress: 'test-wallet' });
      expect(next.calledOnce).to.be.true;
    });

    it('should return 401 for missing token', async () => {
      authMiddleware(req, res, next);

      expect(res.status.calledWith(401)).to.be.true;
      expect(res.json.calledWith({ message: 'No token provided' })).to.be.true;
      expect(next.called).to.be.false;
    });

    it('should return 401 for invalid token format', async () => {
      req.headers.authorization = 'InvalidFormat token';

      authMiddleware(req, res, next);

      expect(res.status.calledWith(401)).to.be.true;
      expect(res.json.calledWith({ message: 'No token provided' })).to.be.true;
      expect(next.called).to.be.false;
    });

    it('should return 401 for expired token', async () => {
      req.headers.authorization = 'Bearer expired-token';
      const expiredError = new Error('Token expired');
      expiredError.name = 'TokenExpiredError';
      sinon.stub(jwt, 'verify').throws(expiredError);

      authMiddleware(req, res, next);

      expect(res.status.calledWith(401)).to.be.true;
      expect(res.json.calledWith({ message: 'Token expired' })).to.be.true;
      expect(next.called).to.be.false;
    });

    it('should return 401 for invalid JWT token', async () => {
      req.headers.authorization = 'Bearer invalid-token';
      const jwtError = new Error('Invalid token');
      jwtError.name = 'JsonWebTokenError';
      sinon.stub(jwt, 'verify').throws(jwtError);

      authMiddleware(req, res, next);

      expect(res.status.calledWith(401)).to.be.true;
      expect(res.json.calledWith({ message: 'Invalid token' })).to.be.true;
      expect(next.called).to.be.false;
    });

    it('should return 500 if JWT_SECRET is not defined', async () => {
      delete process.env.JWT_SECRET;
      req.headers.authorization = 'Bearer valid-token';

      authMiddleware(req, res, next);

      expect(res.status.calledWith(500)).to.be.true;
      expect(res.json.calledWith({ message: 'Server configuration error' })).to.be.true;
      expect(next.called).to.be.false;
    });

    it('should attach user info to request', async () => {
      const mockPayload = { walletAddress: 'test-wallet-123' };
      req.headers.authorization = 'Bearer valid-token';

      sinon.stub(jwt, 'verify').returns(mockPayload as any);

      authMiddleware(req, res, next);

      expect(req.user).to.deep.equal({ walletAddress: 'test-wallet-123' });
      expect(next.calledOnce).to.be.true;
    });

    it('should handle generic errors', async () => {
      req.headers.authorization = 'Bearer valid-token';
      sinon.stub(jwt, 'verify').throws(new Error('Generic error'));

      authMiddleware(req, res, next);

      expect(res.status.calledWith(401)).to.be.true;
      expect(res.json.calledWith({ message: 'Invalid token' })).to.be.true;
      expect(next.called).to.be.false;
    });
  });

  describe('walletOwnershipMiddleware', () => {
    it('should allow access for wallet owner', async () => {
      req.params = { walletAddress: 'test-wallet' };
      req.user = { walletAddress: 'test-wallet' };

      walletOwnershipMiddleware(req, res, next);

      expect(next.calledOnce).to.be.true;
      expect(res.status.called).to.be.false;
    });

    it('should deny access for non-owner', async () => {
      req.params = { walletAddress: 'other-wallet' };
      req.user = { walletAddress: 'test-wallet' };

      walletOwnershipMiddleware(req, res, next);

      expect(res.status.calledWith(403)).to.be.true;
      expect(res.json.calledWith({ message: 'Access denied' })).to.be.true;
      expect(next.called).to.be.false;
    });

    it('should handle missing wallet address in params', async () => {
      req.params = {};
      req.user = { walletAddress: 'test-wallet' };

      walletOwnershipMiddleware(req, res, next);

      expect(res.status.calledWith(403)).to.be.true;
      expect(res.json.calledWith({ message: 'Access denied' })).to.be.true;
      expect(next.called).to.be.false;
    });

    it('should handle missing user in request', async () => {
      req.params = { walletAddress: 'test-wallet' };
      req.user = undefined;

      walletOwnershipMiddleware(req, res, next);

      expect(res.status.calledWith(403)).to.be.true;
      expect(res.json.calledWith({ message: 'Access denied' })).to.be.true;
      expect(next.called).to.be.false;
    });

    it('should handle errors during verification', async () => {
      req.params = { walletAddress: 'test-wallet' };
      req.user = { walletAddress: 'test-wallet' };

      // Simulate an error by making params.walletAddress throw
      Object.defineProperty(req.params, 'walletAddress', {
        get: () => { throw new Error('Params error'); }
      });

      walletOwnershipMiddleware(req, res, next);

      expect(res.status.calledWith(403)).to.be.true;
      expect(res.json.calledWith({ message: 'Access denied' })).to.be.true;
      expect(next.called).to.be.false;
    });

    it('should handle null user object', async () => {
      req.params = { walletAddress: 'test-wallet' };
      req.user = null;

      walletOwnershipMiddleware(req, res, next);

      expect(res.status.calledWith(403)).to.be.true;
      expect(res.json.calledWith({ message: 'Access denied' })).to.be.true;
      expect(next.called).to.be.false;
    });
  });
});
