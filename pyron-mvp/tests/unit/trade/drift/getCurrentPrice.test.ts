import { expect } from 'chai';
import sinon from 'sinon';
import axios from 'axios';
import { getCurrentPrice } from '../../../../trade/drift/getPerpPrice';

describe('getCurrentPrice', () => {
  let axiosStub: sinon.SinonStub;

  beforeEach(() => {
    axiosStub = sinon.stub(axios, 'get');
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should fetch current price for valid market', async () => {
    const mockResponse = {
      data: {
        bids: [{ price: '100000000' }], // 100 USDC in micro units
        asks: [{ price: '101000000' }]  // 101 USDC in micro units
      }
    };
    axiosStub.resolves(mockResponse);

    const result = await getCurrentPrice('SOL');

    expect(result).to.equal(100.5); // (100 + 101) / 2 / 10^6
    expect(axiosStub.calledOnce).to.be.true;
    expect(axiosStub.calledWith('https://dlob.drift.trade/l2?marketName=SOL-PERP&depth=10&includeOracle=true&includeVamm=true')).to.be.true;
  });

  it('should calculate mean price from bid/ask spread', async () => {
    const mockResponse = {
      data: {
        bids: [{ price: '50000000' }], // 50 USDC
        asks: [{ price: '52000000' }]  // 52 USDC
      }
    };
    axiosStub.resolves(mockResponse);

    const result = await getCurrentPrice('BTC');

    expect(result).to.equal(51); // (50 + 52) / 2 / 10^6
  });

  it('should handle API errors gracefully', async () => {
    axiosStub.rejects(new Error('Network error'));

    try {
      await getCurrentPrice('SOL');
      expect.fail('Should have thrown an error');
    } catch (error: any) {
      expect(error.message).to.equal('Failed to fetch market price');
    }
  });

  it('should validate market name format', async () => {
    const mockResponse = {
      data: {
        bids: [{ price: '100000000' }],
        asks: [{ price: '101000000' }]
      }
    };
    axiosStub.resolves(mockResponse);

    await getCurrentPrice('ETH');

    expect(axiosStub.calledWith('https://dlob.drift.trade/l2?marketName=ETH-PERP&depth=10&includeOracle=true&includeVamm=true')).to.be.true;
  });

  it('should handle malformed API responses', async () => {
    const mockResponse = {
      data: {
        bids: [],
        asks: []
      }
    };
    axiosStub.resolves(mockResponse);

    try {
      await getCurrentPrice('SOL');
      expect.fail('Should have thrown an error');
    } catch (error: any) {
      expect(error.message).to.equal('Failed to fetch market price');
    }
  });

  it('should handle missing bids in response', async () => {
    const mockResponse = {
      data: {
        asks: [{ price: '101000000' }]
      }
    };
    axiosStub.resolves(mockResponse);

    try {
      await getCurrentPrice('SOL');
      expect.fail('Should have thrown an error');
    } catch (error: any) {
      expect(error.message).to.equal('Failed to fetch market price');
    }
  });

  it('should handle missing asks in response', async () => {
    const mockResponse = {
      data: {
        bids: [{ price: '100000000' }]
      }
    };
    axiosStub.resolves(mockResponse);

    try {
      await getCurrentPrice('SOL');
      expect.fail('Should have thrown an error');
    } catch (error: any) {
      expect(error.message).to.equal('Failed to fetch market price');
    }
  });

  it('should handle invalid price format', async () => {
    const mockResponse = {
      data: {
        bids: [{ price: 'invalid' }],
        asks: [{ price: '101000000' }]
      }
    };
    axiosStub.resolves(mockResponse);

    const result = await getCurrentPrice('SOL');

    // parseFloat('invalid') returns NaN, so the result should be NaN
    expect(result).to.be.NaN;
  });

  it('should handle empty market name', async () => {
    const mockResponse = {
      data: {
        bids: [{ price: '100000000' }],
        asks: [{ price: '101000000' }]
      }
    };
    axiosStub.resolves(mockResponse);

    await getCurrentPrice('');

    expect(axiosStub.calledWith('https://dlob.drift.trade/l2?marketName=-PERP&depth=10&includeOracle=true&includeVamm=true')).to.be.true;
  });

  it('should handle axios timeout error', async () => {
    axiosStub.rejects({ code: 'ECONNABORTED', message: 'timeout' });

    try {
      await getCurrentPrice('SOL');
      expect.fail('Should have thrown an error');
    } catch (error: any) {
      expect(error.message).to.equal('Failed to fetch market price');
    }
  });

  it('should handle HTTP error responses', async () => {
    axiosStub.rejects({ response: { status: 404, statusText: 'Not Found' } });

    try {
      await getCurrentPrice('INVALID');
      expect.fail('Should have thrown an error');
    } catch (error: any) {
      expect(error.message).to.equal('Failed to fetch market price');
    }
  });
});
