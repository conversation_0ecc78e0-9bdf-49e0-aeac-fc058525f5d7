import request from 'supertest';
import app from '../main'; // Import your Express app
import { expect } from 'chai'; // Import Chai

// @ts-ignore
describe('Chat API', function(this: Mocha.Suite) {
  this.timeout(10000); // Set timeout to 10 seconds for this suite

  let validChatId: string;
  let authToken: string;

  before(async () => {
    // Get auth token first
    const authResponse = await request(app)
      .post('/api/auth/token')
      .send({ walletAddress: 'testWallet' });
    
    authToken = authResponse.body.token;
    
    // Create a chat to use its ID in tests
    const response = await request(app)
      .post('/api/chats')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        walletAddress: 'testWallet',
        title: 'Test Chat',
        chatType: 'general'
      });
    validChatId = response.body._id;
  });

  it('should create a new chat', async () => {
    const response = await request(app)
      .post('/api/chats')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        walletAddress: 'testWallet',
        title: 'Test Chat',
        chatType: 'general'
      });

    expect(response.status).to.equal(201);
    expect(response.body).to.have.property('title', 'Test Chat');
  });

  it('should get all chats for a wallet', async () => {
    // First ensure there's at least one chat for this wallet
    await request(app)
      .post('/api/chats')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        walletAddress: 'testWallet',
        title: 'Another Test Chat',
        chatType: 'general'
      });

    // Try the endpoint with the wallet address in the path
    const response = await request(app)
      .get('/api/chats/wallet/testWallet')
      .set('Authorization', `Bearer ${authToken}`);
  
    expect(response.status).to.equal(200);
    expect(response.body.length).to.be.greaterThan(0);
  });

  it('should get a specific chat', async () => {
    const response = await request(app)
      .get(`/api/chats/${validChatId}`)
      .set('Authorization', `Bearer ${authToken}`);
    expect(response.status).to.equal(200);
  });

  it('should update a chat', async () => {
    const response = await request(app)
      .patch(`/api/chats/${validChatId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({ title: 'Updated Chat Title' });

    expect(response.status).to.equal(200);
    expect(response.body).to.have.property('title', 'Updated Chat Title');
  });

  it('should delete a chat', async () => {
    const response = await request(app)
      .delete(`/api/chats/${validChatId}`)
      .set('Authorization', `Bearer ${authToken}`);
    expect(response.status).to.equal(200);
  });

  it('should add a message to a chat', async () => {
    const response = await request(app)
      .post(`/api/chats/${validChatId}/messages`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({ type: 'user', content: 'Hello, world!' });

    expect(response.status).to.equal(201);
    expect(response.body).to.have.property('content', 'Hello, world!');
  });

  it('should get messages for a chat', async () => {
    const response = await request(app)
      .get(`/api/chats/${validChatId}/messages`)
      .set('Authorization', `Bearer ${authToken}`);
    expect(response.status).to.equal(200);
    expect(response.body.length).to.be.greaterThan(0);
  });
});
