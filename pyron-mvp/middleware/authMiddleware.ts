import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { TokenPayload } from '../types/auth';

// Extend Express Request interface to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        walletAddress: string;
      };
    }
  }
}

/**
 * Middleware to verify JWT token
 * Extracts token from Authorization header and verifies it
 * If valid, adds user info to request object and calls next()
 * If invalid, returns 401 Unauthorized
 */
export const authMiddleware = (req: Request, res: Response, next: NextFunction):any => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'No token provided' });
    }

    const token = authHeader.split(' ')[1];

    // Verify JWT token
    const JWT_SECRET = process.env.JWT_SECRET;
    if (!JWT_SECRET) {
      console.error('JWT_SECRET is not defined in environment variables');
      return res.status(500).json({ message: 'Server configuration error' });
    }

    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET) as TokenPayload;

    // Add user info to request
    req.user = {
      walletAddress: decoded.walletAddress
    };

    next();
  } catch (error: any) {
    console.error('Token verification error:', error);

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expired' });
    }

    if (error.name === 'JsonWebTokenError') {
      // This will catch malformed tokens and other JWT-specific errors
      return res.status(401).json({ message: error.message });
    }

    return res.status(401).json({ message: 'Invalid token' });
  }
};

/**
 * Middleware to check if the requested wallet matches the authenticated wallet
 * Use this for routes that access wallet-specific data
 */
export const walletOwnershipMiddleware = (req: Request, res: Response, next: NextFunction):any => {
  try {
    const { walletAddress } = req.params;

    // Verify the requested wallet matches the authenticated wallet
    if (!req.user || walletAddress !== req.user.walletAddress) {
      return res.status(403).json({ message: 'Access denied' });
    }

    next();
  } catch (error) {
    console.error('Wallet ownership verification error:', error);
    return res.status(403).json({ message: 'Access denied' });
  }
};
