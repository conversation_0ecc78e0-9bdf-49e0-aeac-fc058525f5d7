import { Request, Response } from 'express';
import Agent from '../databaseModels/agent';
import { ObjectId } from 'mongodb';
import mongoose from 'mongoose';
import Chat from '../databaseModels/chat';

/**
 * Create a new Agent
 */
export const createAgent = async (req: Request, res: Response) => {
  try {
    const {
      agentName,
      assetPair,
      pubkey,
      buySignalMessage,
      sellSignalMessage,
      tradingStatus,
      number,
      botId,
      chatId,
      signals,
      deposit,
      requiredConfirmationsOpen
      
    } = req.body;

    const newAgent = new Agent({
      agentName: agentName as string,
      assetPair: assetPair as string,
      pubkey: pubkey as string,
      buySignalMessage: buySignalMessage as string,
      sellSignalMessage: sellSignalMessage as string,
      timestamps: Date.now().toString(),
      tradingStatus: tradingStatus as string,
      number: number as number,
      botId: botId as string,
      chatId: chatId as string,
      signals: signals,
      deposit: deposit as number,
      requiredConfirmationsOpen: requiredConfirmationsOpen as number
    });

    const savedAgent = await newAgent.save();
    res.status(201).json(savedAgent);
  } catch (error) {
    res.status(400).json({ message: (error as Error).message });
  }
};

/**
 * Get an Agent by ID
 */
export const getAgentById = async (req: any, res: any) => {
  try {
    const agent = await Agent.findById(req.params.id);

    if (!agent) {
      return res.status(404).json({ message: 'Agent not found' });
    }

    res.json(agent);
  } catch (error) {
    res.status(500).json({ message: (error as Error).message });
  }
};

export const getAgentByPubkeyAndAssetPair = async (req: any, res: any) => {
  try {
    const agent = await Agent.findOne({ pubkey: req.params.pubkey, assetPair: req.params.assetPair });
    console.log(agent);
    if (!agent) {
      return res.status(200).json(null);
    }
    res.json(agent);
  } catch (error) {
    res.status(500).json({ message: (error as Error).message });
  }
};

export const getAgentBybotId = async (req: any, res: any) => {
  try {
    const agent = await Agent.findOne({ botId: req.params.botId });
    console.log(agent);
    if (!agent) {
      return res.status(200).json(null);
    }
    res.json(agent);
  } catch (error) {
    res.status(500).json({ message: (error as Error).message });
  }
};

export const getAgentBychatId = async (req: any, res: any) => {
  try {
    console.log(req.params.chatId);
    const agent = await Agent.findOne({ chatId: req.params.chatId });
    console.log(agent);
    if (!agent) {
      return res.status(200).json(null);
    }
    res.json(agent);
  } catch (error) {
    res.status(500).json({ message: (error as Error).message });
  }
};

export const deleteAgent = async (req: any, res: any) => {
  try {
    const agent = await Agent.findByIdAndDelete(req.params.id);

    if (!agent) {
      return res.status(404).json({ message: 'Agent not found' });
    }

    res.json({ message: 'Agent deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: (error as Error).message });
  }
};

export const updateTradingStatus = async (req: any, res: any) => {
  try {
    const { botId } = req.params;
    const { tradingStatus } = req.body;

    // 1. Validate tradingStatus is either "on" or "off"
    if (tradingStatus !== 'on' && tradingStatus !== 'off') {
      return res.status(400).json({
        message: 'Invalid trading status. Must be either "on" or "off".',
      });
    }

    // 2. Find the agent by id
    const agent = await Agent.findOne({ botId: botId });
    if (!agent) {
      return res.status(403).json({ message: 'Agent not found.' });
    }

    // 3. Update trading status
    agent.tradingStatus = tradingStatus;

    // 4. Save the updated agent
    const updatedAgent = await agent.save();

    // 5. Return the updated agent
    return res.status(200).json(updatedAgent);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ message: 'Server error updating trading status.' });
  }
};

export const updateHypothesisStatus = async (req: any, res: any) => {
  try {
    const { botId } = req.params;
    const { hypothesisStatus } = req.body;

    // 1. Validate tradingStatus is either "on" or "off"
    if (hypothesisStatus !== 'on' && hypothesisStatus !== 'off') {
      return res.status(400).json({
        message: 'Invalid trading status. Must be either "on" or "off".',
      });
    }

    // 2. Find the agent by id
    const agent = await Agent.findOne({ botId: botId });
    if (!agent) {
      return res.status(403).json({ message: 'Agent not found.' });
    }

    // 3. Update trading status
    agent.hypothesisStatus = hypothesisStatus;

    // 4. Save the updated agent
    const updatedAgent = await agent.save();

    // 5. Return the updated agent
    return res.status(200).json(updatedAgent);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ message: 'Server error updating hypothesis status.' });
  }
};

export async function getAgentByPubkeyController(pubkey:string): Promise<any> {
  try {

    const user = await Agent.findOne({ pubkey });

    if (!user) {
      console.log({ message: `User with pubkey ${pubkey} not found` });
      return;
    }

    return user;
  } catch (error: any) {
    console.error('Error fetching user by public key:', error.message);
  }
}

export const saveOrUpdateAgent = async (req: any, res: any) => {
  try {
    const { botId } = req.params;
    const { agentName, assetPair, number, tradingStatus, deposit, hypothesisStatus,requiredBuyConfirmationsOpen,chatId,pubkey,requiredSellConfirmationsOpen,requiredBuyConfirmationsClose,requiredSellConfirmationsClose, requiredSellConfirmationsResetCounter, requiredBuyConfirmationsResetCounter, requiredBuyConfirmationsOverride, requiredSellConfirmationsOverride } = req.body.agent; 
    const updateData = { agentName, assetPair, number, tradingStatus, deposit, hypothesisStatus,requiredBuyConfirmationsOpen,chatId,pubkey,requiredSellConfirmationsOpen,requiredBuyConfirmationsClose,requiredSellConfirmationsClose, requiredSellConfirmationsResetCounter, requiredBuyConfirmationsResetCounter, requiredBuyConfirmationsOverride, requiredSellConfirmationsOverride };

    console.log('Updating data:', updateData);
    console.log('Bot ID:', botId);

    const options = { new: true, upsert: true };
    const updatedAgent = await Agent.findOneAndUpdate({ botId: botId }, updateData, options);

    console.log('Updated Agent:', updatedAgent);

    if (updatedAgent) {
      console.log('Agent updated or created successfully:', updatedAgent);
      return res.status(200).json(updatedAgent);
    } else {
      console.log('No document matches the provided query.');
      return res.status(404).send('Agent not found');
    }
  } catch (error: any) {
    console.error('Error saving or updating agent:', error);
    return res.status(500).send(error.message);
  }
};

export const getAllAgents = async (req: any, res: any) => {
  try {
    const agents = await Agent.find();
    res.json(agents);
  } catch (error: any) {
    console.error('Error fetching all agents:', error);
    return res.status(500).send(error.message);
  }
};

export const addPromptsToAgent = async (req: any, res: any) => {
  try {
    const { botId } = req.params;
    const { buyReportPrompt, sellReportPrompt } = req.body.agent; // Corrected destructuring path
    const updateData = { buyReportPrompt, sellReportPrompt };

    console.log('Updating data:', updateData);
    console.log('Bot ID:', botId);

    const options = { new: true, upsert: true };
    const updatedAgent = await Agent.findOneAndUpdate({ botId: botId }, updateData, options);

    console.log('Updated Agent:', updatedAgent);

    if (updatedAgent) {
      console.log('Agent updated or created successfully:', updatedAgent);
      return res.status(200).json(updatedAgent);
    } else {
      console.log('No document matches the provided query.');
      return res.status(404).send('Agent not found');
    }
  } catch (error: any) {
    console.error('Error saving or updating agent:', error);
    return res.status(500).send(error.message);
  }
};

export const updateAgentName = async (req: any, res: any) => {
  try {
    const { id } = req.params; // This is the chat ID
    const { name } = req.body;
    console.log("id", id);
    
    // Validate if name is provided
    if (!name) {
      return res.status(400).json({ message: 'Agent name is required' });
    }

    // Update the chat title first
    const updatedChat = await Chat.findByIdAndUpdate(
      id,
      { title: name },
      { new: true }
    );
    
    if (!updatedChat) {
      return res.status(404).json({ message: 'Chat not found' });
    }
    
    // Find if there's an agent associated with this chat
    const agent = await Agent.findOne({ chatId: id });
    let updatedAgent = null;
    
    if (agent) {
      // If agent exists, update it too
      updatedAgent = await Agent.findByIdAndUpdate(
        agent._id,
        { agentName: name },
        { new: true }
      );
    }

    return res.status(200).json({ 
      agent: updatedAgent,
      chat: updatedChat
    });
  } catch (error) {
    console.error('Error updating agent name:', error);
    return res.status(500).json({ message: (error as Error).message });
  }
};
