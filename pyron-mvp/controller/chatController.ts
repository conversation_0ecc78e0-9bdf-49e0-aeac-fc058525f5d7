import { Request, Response } from 'express';
import Chat from '../databaseModels/chat';
import ChatMessage from '../databaseModels/chatMessage';
import Agent from '../databaseModels/agent';

/**
 * Add a new AI Chat
 */
export const addChat = async (req: Request, res: Response) => {
  try {
    const { walletAddress, message } = req.body;

    if (!walletAddress || !message) {
      return res.status(400).json({ message: 'Wallet address and message are required' });
    }

    const newChat = new Chat({ walletAddress, message, timestamp: new Date() });
    await newChat.save();

    res.status(201).json(newChat);
  } catch (error: any) {
    console.error('Error adding chat:', error.message);
    res.status(500).json({ error: error.message });
  }
};

/**
 * Get Chats by Wallet Address
 */
export const getChatsByWalletAddress = async (req: Request, res: Response) => {
  try {
    const { walletAddress } = req.params;
    const chats = await Chat.find({ walletAddress });
    res.status(200).json(chats);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

export const getChatsByWalletAddressWithAgents = async (req: Request, res: Response) => {
  try {
    const { walletAddress } = req.params;
    
    // Find chats by wallet address
    const chats = await Chat.find({ walletAddress }).lean();
    
    // Get the corresponding agents for each chat
    const chatsWithAgents = await Promise.all(
      chats.map(async (chat) => {
        // Find the agent where agent.chatId equals chat._id
        const agent = await Agent.findOne({ chatId: chat._id }).lean();
        
        // Return chat with its associated agent as a plain object
        return {
          ...chat,
          agent: agent || null
        };
      })
    );
    
    res.status(200).json(chatsWithAgents);
  } catch (error: any) {
    console.error('Error getting chats with agents:', error.message);
    res.status(500).json({ error: error.message });
  }
};

/**
 * Get a specific chat
 */
export const getChatById = async (req: Request, res: Response) => {
  try {
    const { chatId } = req.params;
    const chat = await Chat.findById(chatId);

    if (!chat) {
       res.status(404).json({ message: 'Chat not found' });
       return;
    }

    res.status(200).json(chat);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

/**
 * Update a chat
 */
export const updateChat = async (req: Request, res: Response) => {
  try {
    const { chatId } = req.params;
    const updateData = req.body;

    const chat = await Chat.findByIdAndUpdate(chatId, updateData, { new: true });

    if (!chat) {
      res.status(404).json({ message: 'Chat not found' });
      return;
    }

    res.status(200).json(chat);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

/**
 * Delete a chat
 */
export const deleteChat = async (req: Request, res: Response) => {
  try {
    const { chatId } = req.params;
    const chat = await Chat.findByIdAndDelete(chatId);

    if (!chat) {
      res.status(404).json({ message: 'Chat not found' });
      return;
    }

    res.status(200).json({ message: 'Chat deleted successfully' });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

/**
 * Get messages for a chat
 */
export const getChatMessages = async (req: Request, res: Response) => {
  try {
    const { chatId } = req.params;
    console.log('getChatMessages', chatId);
    const messages = await ChatMessage.find({ chatId });
    console.log('messages', messages);
    res.status(200).json(messages);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

/**
 * Add message to a chat
 */
export const addChatMessage = async (req: Request, res: Response) => {
  try {
    const { chatId } = req.params;
    const { type, content, metadata } = req.body;
    console.log('chatId', chatId);
    console.log('addChatMessage', req.body);
    const newMessage = new ChatMessage({ chatId, type, content, metadata });
    console.log('newMessage', newMessage);
    await newMessage.save();

    res.status(201).json(newMessage);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

/**
 * Create a new chat
 */
export const createChat = async (req: Request, res: Response) => {
  try {
    const { walletAddress, title, chatType, metadata } = req.body;

    const newChat = new Chat({ walletAddress, title, chatType, metadata });
    await newChat.save();

    res.status(201).json(newChat);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
}; 