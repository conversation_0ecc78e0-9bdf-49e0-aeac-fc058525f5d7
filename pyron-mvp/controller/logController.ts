import { Request, Response } from 'express';
import Log from '../databaseModels/log'; // Make sure the path is correct

// Get all logs
export async function getLogs(req: Request, res: Response) {
  try {
    const logs = await Log.find();
    res.status(200).json(logs);
  } catch (error:any) {
    res.status(500).json({ message: error.message });
  }
}

// Get a single log by ID
export async function getLog(req: Request, res: Response) {
  try {
    const log = await Log.findById(req.params.id);
    if (log) {
      res.status(200).json(log);
    } else {
      res.status(404).json({ message: 'Log not found' });
    }
  } catch (error:any) {
    res.status(500).json({ message: error.message });
  }
}

// Create a new log
export async function createLog(req: Request, res: Response) {
  const log = new Log(req.body);
  try {
    const savedLog = await log.save();
    res.status(201).json(savedLog);
  } catch (error:any) {
    res.status(400).json({ message: error.message });
  }
}

// Update a log by ID
export async function updateLog(req: Request, res: Response) {
  try {
    const updatedLog = await Log.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (updatedLog) {
      res.status(200).json(updatedLog);
    } else {
      res.status(404).json({ message: 'Log not found' });
    }
  } catch (error:any) {
    res.status(400).json({ message: error.message });
  }
}

// Delete a log by ID
export async function deleteLog(req: Request, res: Response) {
  try {
    const log = await Log.findByIdAndDelete(req.params.id);
    if (log) {
      res.status(200).json({ message: 'Log deleted successfully' });
    } else {
      res.status(404).json({ message: 'Log not found' });
    }
  } catch (error:any) {
    res.status(500).json({ message: error.message });
  }
}

// Controller to fetch logs by chatId
export const getLogByChatId = async (req: any, res: any) => {
  try {
    console.log("get logs",req.query)
    const { chatId } = req.query;

    if (!chatId || typeof chatId !== 'string') {
      return res.status(400).json({ error: 'chatId is required and must be a string' });
    }

    const logs = await Log.find({ chatId });

    

    return res.status(200).json(logs);
  } catch (error) {
    console.error('Error fetching logs:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};






