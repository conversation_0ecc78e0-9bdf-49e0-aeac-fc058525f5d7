import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import User from '../databaseModels/user';
import { TokenPayload, AuthResponse, AccessTokenPayload, RefreshTokenPayload } from '../types/auth';

// Store for revoked tokens (in a production environment, use Redis or a database)
const revokedTokens = new Set<string>();

/**
 * Generate JWT token for a wallet address
 * @param walletAddress The wallet address to generate token for
 * @param type The type of token (access or refresh)
 * @returns JWT token
 */
const generateToken = (walletAddress: string, type: 'access' | 'refresh'): string => {
  const JWT_SECRET = process.env.JWT_SECRET;
  if (!JWT_SECRET) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  const payload: AccessTokenPayload | RefreshTokenPayload = {
    walletAddress,
    type
  };

  const expiresIn = type === 'access' ? '1h' : '7d';

  try {
    const token = jwt.sign(
      payload,
      JWT_SECRET,
      { expiresIn }
    );

    // Verify the token is valid before returning it
    jwt.verify(token, JWT_SECRET);

    return token;
  } catch (error: any) {
    console.error('Error generating token:', error);
    throw new Error(`Failed to generate valid token: ${error.message}`);
  }
};

/**
 * Extract token from Authorization header
 * @param authHeader The Authorization header value
 * @returns The extracted token or null if not found
 */
const extractTokenFromHeader = (authHeader?: string): string | null => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  return authHeader.split(' ')[1];
};

/**
 * Generate a new JWT token for a wallet address
 * If the wallet doesn't exist in the database, create a new user
 */
export const generateAuthToken = async (req: Request, res: Response): Promise<void> => {
  try {
    const { walletAddress } = req.body;

    if (!walletAddress) {
      res.status(400).json({ message: 'Wallet address is required' });
      return;
    }

    // Check if wallet exists in the database
    let user = await User.findOne({ walletAddress });

    if (!user) {
      // Create new user if not exists
      user = new User({
        walletAddress,
        createdAt: new Date(),
        lastLoginAt: new Date()
      });
      await user.save();
    } else {
      // Update last login time
      user.lastLoginAt = new Date();
      await user.save();
    }

    // Generate access token (short-lived)
    const accessToken = generateToken(walletAddress, 'access');

    // Generate refresh token (long-lived)
    const refreshToken = generateToken(walletAddress, 'refresh');

    // Set refresh token as httpOnly cookie
    res.cookie('refresh_token', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production', // Only send over HTTPS in production
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
      path: '/'
    });

    const response: AuthResponse = {
      token: accessToken,
      expiresIn: 3600 // 1 hour in seconds
    };

    res.status(200).json(response);
  } catch (error: any) {
    console.error('Token generation error:', error);
    res.status(500).json({ message: error.message || 'Server error' });
  }
};

/**
 * Refresh an existing JWT token
 * Uses the refresh token from cookies to generate a new access token
 */
export const refreshToken = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get refresh token from cookie
    const refreshToken = req.cookies.refresh_token;

    if (!refreshToken) {
      res.status(401).json({ message: 'Refresh token not found' });
      return;
    }

    // Check if token is revoked
    if (revokedTokens.has(refreshToken)) {
      res.status(401).json({ message: 'Refresh token has been revoked' });
      return;
    }

    // Verify refresh token
    const JWT_SECRET = process.env.JWT_SECRET;
    if (!JWT_SECRET) {
      throw new Error('JWT_SECRET is not defined in environment variables');
    }

    const decoded = jwt.verify(refreshToken, JWT_SECRET) as RefreshTokenPayload;

    // Verify it's a refresh token
    if (decoded.type !== 'refresh') {
      res.status(401).json({ message: 'Invalid token type' });
      return;
    }

    // Generate new access token
    const newAccessToken = generateToken(decoded.walletAddress, 'access');

    const response: AuthResponse = {
      token: newAccessToken,
      expiresIn: 3600 // 1 hour in seconds
    };

    res.status(200).json(response);
  } catch (error: any) {
    console.error('Token refresh error:', error);

    if (error.name === 'TokenExpiredError') {
      res.status(401).json({ message: 'Refresh token expired' });
      return;
    }

    if (error.name === 'JsonWebTokenError') {
      res.status(401).json({ message: 'Invalid refresh token' });
      return;
    }

    res.status(500).json({ message: error.message || 'Server error' });
  }
};

/**
 * Logout a user by revoking their refresh token and clearing the cookie
 */
export const logout = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get refresh token from cookie
    const refreshToken = req.cookies.refresh_token;

    if (refreshToken) {
      // Add refresh token to revoked tokens set
      revokedTokens.add(refreshToken);

      // In a production environment, you would store this in Redis or a database
      // with an expiration time matching the token's expiration
    }

    // Clear the refresh token cookie
    res.clearCookie('refresh_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/'
    });

    res.status(200).json({ success: true });
  } catch (error: any) {
    console.error('Logout error:', error);
    res.status(500).json({ message: error.message || 'Server error' });
  }
};
