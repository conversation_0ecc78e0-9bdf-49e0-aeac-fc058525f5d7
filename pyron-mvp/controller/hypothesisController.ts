import { Request, Response } from 'express';
import Hypothesis from '../databaseModels/hypothesis';
import Log from '../databaseModels/log';

// Create a new hypothesis
export async function createHypothesis(req: Request, res: Response) :Promise<any> {
  try {
    const { logId, hypothesis } = req.body;

    if (!logId || !hypothesis) {
      return res.status(400).json({ message: 'LogId and hypothesis are required' });
    }

    const newHypothesis = new Hypothesis({
      logId,
      hypothesis,
      date: new Date()
    });

    const savedHypothesis = await newHypothesis.save();
    res.status(201).json(savedHypothesis);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
}

// Get hypotheses by logId
export async function getHypothesesByLogId(req: Request, res: Response):Promise<any> {
  try {
    const { logId } = req.params;

    if (!logId) {
      return res.status(400).json({ message: 'LogId is required' });
    }

    const hypotheses = await Hypothesis.find({ logId }).sort({ date: -1 });
    res.status(200).json(hypotheses);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
}

// Get all hypotheses
export async function getAllHypotheses(req: Request, res: Response):Promise<any> {
  try {
    const hypotheses = await Hypothesis.find().sort({ date: -1 });
    res.status(200).json(hypotheses);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
}

// Get hypotheses by chatId
export async function getHypothesesByChatId(req: Request, res: Response):Promise<any> {
  try {
    const { chatId } = req.params;

    if (!chatId) {
      return res.status(400).json({ message: 'ChatId is required' });
    }

    // First, get all logs for this chatId
    const logs = await Log.find({ chatId });

    if (!logs || logs.length === 0) {
      return res.status(200).json([]); // Return empty array if no logs found
    }

    // Get all log IDs
    const logIds = logs.map(log => log._id);

    // Find all hypotheses for these logs
    const hypotheses = await Hypothesis.find({ logId: { $in: logIds } }).sort({ date: -1 });

    res.status(200).json(hypotheses);
  } catch (error: any) {
    console.error('Error fetching hypotheses by chatId:', error.message);
    res.status(500).json({ message: error.message });
  }
}
