import { Request, Response } from 'express';
import { createClient } from '../trade/drift/getClient';
import { SolanaAgentKit } from 'solana-agent-kit';
import { getCurrentPrice } from '../trade/drift/getPerpPrice';
import { getAgentBybotId } from './agentController';
import Agent from '../databaseModels/agent';
import { getMarketId } from '../utils/getMarketId';
import { createKeypairFromSecretKey } from '../utils/createKeypairFromSecretKey';
import { PublicKey } from '@solana/web3.js';
import { Connection } from '@solana/web3.js';
import { AMM_RESERVE_PRECISION_EXP, QUOTE_PRECISION } from '@drift-labs/sdk';



export async function getPosition(req: any, res: any): Promise<void> {
  try {
    const { agentId } = req.query;
    if (!agentId || typeof agentId !== 'string') {
      res.status(400).json({ error: 'agentId is required and must be a string' });
      return;
    }
    if (!process.env.ADMIN_KEY || !process.env.RPC_URL) {
      throw new Error("ADMIN_KEY and RPC_URL must be set in the environment variables");
    }

    const agent = await Agent.findOne({ botId: agentId.toString() });
    if (!agent) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }

    // Check if the authenticated user owns this agent
    if (!req.user || req.user.walletAddress !== agent.pubkey) {
      res.status(403).json({ error: 'Forbidden: You do not have permission to access this agent data' });
      return;
    }
    console.log("agent", agent);
    const wallet = createKeypairFromSecretKey(process.env.ADMIN_KEY);
    console.log("admin wallet", wallet.publicKey.toString());
    const connection = new Connection(process.env.RPC_URL);
    const authority = new PublicKey(agent.pubkey);
    console.log("authority", authority);
    let driftClient = await createClient(connection, wallet, authority);
    if (!driftClient) {
      throw new Error("Drift client not created");
    }
    const subscribe = await driftClient.subscribe().catch((error: any) => {
      console.log("error");
    });
    console.log("subscribe", subscribe);
    let ticker = agent.assetPair;
    console.log("ticker", ticker);
    let marketId = getMarketId(ticker);
    if (marketId === undefined) {
      res.status(400).json({ error: 'Market ID not found' });
      return;
    }
    console.log("marketId", marketId);
    console.log("agent.number", agent.number);
    const user = driftClient.getUser(agent.number);
    let position = user.getPerpPosition(marketId);
    await user.fetchAccounts();
    //console.log("accounts", accounts);
    let portfolioValue = user.getTotalAssetValue().toNumber() / 10 ** 6;
    let pnl = user.getTotalAllTimePnl().toNumber() / 10 ** 6;
    console.log("portfolioValue", portfolioValue);
    let deposit = agent.deposit || 0;
    console.log("deposit", deposit);
    console.log("position", position);

    // Convert to USD values with proper precision
    const pnlUsd = user.getTotalAllTimePnl().toNumber() / Number(QUOTE_PRECISION);
    const equityUsd = user.getTotalAssetValue().toNumber() / Number(QUOTE_PRECISION);
    const initialEq = equityUsd - pnlUsd; // equity before pnl

    // Calculate portfolio-level PnL percentage
    const pnlPctPortfolio = initialEq !== 0 ? (pnlUsd / initialEq) * 100 : 0;

    if (position) {
      const currentPrice = await getCurrentPrice(ticker);
      const positionSize = (position.baseAssetAmount.toNumber() || 0) / 10 ** AMM_RESERVE_PRECISION_EXP.toNumber();
      const currentVal = Math.abs(positionSize) * currentPrice;

      // Calculate position-level PnL percentage
      const entryCost = position.quoteEntryAmount.abs().toNumber() / Number(QUOTE_PRECISION);
      const pnlPctPosition = entryCost !== 0 ? ((currentVal - entryCost) / entryCost) * 100 : 0;

      let response = {
        position: positionSize.toFixed(5),
        positionValue: currentVal.toFixed(5),
        pnlUsd: pnlUsd.toFixed(2),
        pnl: pnlPctPortfolio.toFixed(2),
        pnlPctPosition: pnlPctPosition.toFixed(2),
        portfolioValue: equityUsd.toFixed(2)
      };

      res.status(200).json({ response });
    } else {
      res.status(200).json({
        response: {
          position: 0,
          positionValue: 0,
          pnlUsd: pnlUsd.toFixed(2),
          pnl: pnlPctPortfolio.toFixed(2),
          pnlPctPosition: 0,
          portfolioValue: equityUsd.toFixed(2)
        }
      });
    }

  } catch (error: any) {
    console.error('Error fetching position by agent id:', error.message);
    res.status(500).json({ error: error.message });
  }
}

export async function getAccountActivity(req: any, res: any): Promise<void> {
  try {
    const { pubkey } = req.query;
    if (!pubkey || typeof pubkey !== 'string') {
      res.status(400).json({ error: 'pubkey is required and must be a string' });
      return;
    }

    // Check if the authenticated user is requesting their own data
    if (!req.user || req.user.walletAddress !== pubkey) {
      res.status(403).json({ error: 'Forbidden: You do not have permission to access this user data' });
      return;
    }

    if (!process.env.ADMIN_KEY || !process.env.RPC_URL) {
      throw new Error("ADMIN_KEY and RPC_URL must be set in the environment variables");
    }
    const wallet = createKeypairFromSecretKey(process.env.ADMIN_KEY);
    const connection = new Connection(process.env.RPC_URL);
    const authority = new PublicKey(pubkey);
    let driftClient = await createClient(connection, wallet, authority);
    if (!driftClient) {
      throw new Error("Drift client not created");
    }
    const subscribe = await driftClient.subscribe().catch((error: any) => {
      console.log("error");
    });
    const agents = await Agent.find({ pubkey: pubkey.toString() });
    if (agents.length === 0) {
      res.status(200).json({ orders: [] });
      return;
    }
    let orders: any = {};

    for (let agent of agents) {
      try {
        const user = driftClient.getUser(agent.number);
        let userKey = user.userAccountPublicKey;
        let trades = await fetch(`https://data.api.drift.trade/user/${userKey}/trades`);
        let newOrders = await trades.json();
        orders[agent.botId] = { orders: newOrders, agentName: agent.agentName };
      } catch (error: any) {
        console.log("error");
      }
    }
    console.log("orders", orders);
    res.status(200).json(orders);
  } catch (error: any) {
    console.error('Error fetching account activity:', error.message);
    res.status(500).json({ error: error.message });
  }
}

export async function getAccountAssets(req: any, res: any): Promise<void> {
  try {
    const { pubkey } = req.query;
    if (!pubkey || typeof pubkey !== 'string') {
      res.status(400).json({ error: 'pubkey is required and must be a string' });
      return;
    }

    // Check if the authenticated user is requesting their own data
    if (!req.user || req.user.walletAddress !== pubkey) {
      res.status(403).json({ error: 'Forbidden: You do not have permission to access this user data' });
      return;
    }

    if (!process.env.ADMIN_KEY || !process.env.RPC_URL) {
      throw new Error("ADMIN_KEY and RPC_URL must be set in the environment variables");
    }
    const wallet = createKeypairFromSecretKey(process.env.ADMIN_KEY);
    const connection = new Connection(process.env.RPC_URL);
    const authority = new PublicKey(pubkey);
    let driftClient = await createClient(connection, wallet, authority);
    if (!driftClient) {
      throw new Error("Drift client not created");
    }
    const subscribe = await driftClient.subscribe()
    console.log("subscribe", subscribe);
    const agents = await Agent.find({ pubkey: pubkey.toString() });
    if (agents.length === 0) {
      res.status(200).json({ assets: 0 });
      return;
    }
    let assets: any = 0;
    for (let agent of agents) {
      try {
        let user = driftClient.getUser(agent.number);
        let asset = await user.getTotalAssetValue();
        assets += asset.toNumber() / 10 ** 6;
      } catch (error: any) {
        console.log("error");
      }
    }
    res.status(200).json({ assets });

  } catch (error: any) {
    console.error('Error fetching account assets:', error.message);
    res.status(500).json({ error: error.message });
  }
}

export async function getAccountPositions(req: any, res: any): Promise<void> {
  try {
    const { pubkey } = req.query;
    if (!pubkey || typeof pubkey !== 'string' || pubkey === "") {
      res.status(400).json({ error: 'pubkey is required and must be a string' });
      return;
    }

    // Check if the authenticated user is requesting their own data
    if (!req.user || req.user.walletAddress !== pubkey) {
      res.status(403).json({ error: 'Forbidden: You do not have permission to access this user data' });
      return;
    }

    if (!process.env.ADMIN_KEY || !process.env.RPC_URL) {
      throw new Error("ADMIN_KEY and RPC_URL must be set in the environment variables");
    }
    const wallet = createKeypairFromSecretKey(process.env.ADMIN_KEY);
    console.log("wallet", wallet.publicKey.toString());
    if (!wallet || !wallet.publicKey) {
      throw new Error("Wallet not created");
    }
    const connection = new Connection(process.env.RPC_URL);
    const authority = new PublicKey(pubkey);
    let driftClient = await createClient(connection, wallet, authority);
    if (!driftClient) {
      throw new Error("Drift client not created");
    }
    const subscribe = await driftClient.subscribe()
    console.log("subscribe", subscribe);
    const agents = await Agent.find({ pubkey: pubkey.toString() });
    if (agents.length === 0) {
      res.status(200).json({ positions: [] });
      return;
    }
    let positions: any = [];
    for (let agent of agents) {
      try {
        if (!agent.number || agent.number < 0) {
          console.log("Agent number not found");
          continue;
        }
        let user = driftClient.getUser(agent.number);
        console.log("agent", agent);

        // Ensure assetPair is uppercase for market lookup
        const assetPair = agent.assetPair.toUpperCase();
        let marketId = getMarketId(assetPair);

        console.log("Looking up market ID for:", assetPair, "Result:", marketId);

        if (marketId === undefined) {
          console.log("Market ID not found for", assetPair);
          continue;
        }

        let position = user.getPerpPosition(marketId);
        if (!position) {
          console.log("Position not found");
          continue;
        }
        positions.push({
          market: agent.assetPair,
          position: position.baseAssetAmount.toNumber() / 10 ** AMM_RESERVE_PRECISION_EXP.toNumber(),
        });
      } catch (error: any) {
        console.log("error", error.message);
      }
    }
    res.status(200).json({ positions });

  } catch (error: any) {
    console.error('Error fetching account positions:', error.message);
    res.status(500).json({ error: error.message });
  }
}
