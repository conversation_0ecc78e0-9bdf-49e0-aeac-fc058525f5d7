import { Request, Response } from 'express';
import mongoose from 'mongoose';
import User from '../databaseModels/user';

/**
 * Add a new User
 */
export async function addUserController(req: Request, res: Response): Promise<void> {
  try {
    const { walletAddress, sellReportPrompt, buyReportPrompt } = req.body;

    if (!walletAddress) {
      res.status(400).json({ message: 'Wallet address is required' });
      return;
    }

    const newUser = new User({ walletAddress, sellReportPrompt, buyReportPrompt });
    await newUser.save();

    res.status(201).json(newUser);
  } catch (error: any) {
    console.error('Error adding user:', error.message);
    res.status(500).json({ error: error.message });
  }
}

/**
 * Get a User by Mongo ObjectId
 */
export async function getUserByIdController(req: Request, res: Response): Promise<any> {
  try {
    const { id } = req.params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: `Invalid ObjectId: ${id}` });
    }

    const user = await User.findById(id);

    if (!user) {
      res.status(404).json({ message: `User with id ${id} not found` });
      return;
    }

    res.status(200).json(user);
  } catch (error: any) {
    console.error('Error fetching user by ID:', error.message);
    res.status(500).json({ error: error.message });
  }
}

// Get user profile
export const getUserProfile = async (req: Request, res: Response): Promise<any> => {
  try {
    const { walletAddress } = req.params;
    const user = await User.findOne({ walletAddress });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json(user);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

// Update user profile
export const updateUserProfile = async (req: Request, res: Response): Promise<any> => {
  try {
    const { walletAddress } = req.params;
    const updateData = req.body;

    const user = await User.findOneAndUpdate({ walletAddress }, updateData, { new: true });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json(user);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

// Get user settings
export const getUserSettings = async (req: Request, res: Response): Promise<any> => {
  try {
    const { walletAddress } = req.params;
    const user = await User.findOne({ walletAddress }, 'settings');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json(user.settings);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

// Update user settings
export const updateUserSettings = async (req: Request, res: Response): Promise<any> => {
  try {
    const { walletAddress } = req.params;
    const { settings } = req.body;

    const user = await User.findOneAndUpdate({ walletAddress }, { settings }, { new: true });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json(user.settings);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

