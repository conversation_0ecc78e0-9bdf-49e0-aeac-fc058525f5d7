import { SolanaAgentKit } from "solana-agent-kit";
import { 
  PublicKey, 
  SystemProgram, 
  Transaction, 
  VersionedTransaction
} from "@solana/web3.js";
import { LAMPORTS_PER_SOL } from "@solana/web3.js";

import { ixsToVtx } from "../trade/jito/prepareVersionedTransaction";
import { getAssociatedTokenAddress } from "@solana/spl-token";
import { getMint } from "@solana/spl-token";
import { createTransferInstruction } from "@solana/spl-token";

/**
 * Transfer SOL or SPL tokens to a recipient
 * @param agent SolanaAgentKit instance
 * @param to Recipient's public key
 * @param amount Amount to transfer
 * @param mint Optional mint address for SPL tokens
 * @returns Transaction signature
 */
export async function transfer(
  agent: SolanaAgentKit,
  from: PublicKey,
  to: PublicKey,
  amount: number,
  mint?: PublicKey
): Promise<VersionedTransaction> {
  try {
    let transaction: Transaction;
    if (!mint) {
      // Transfer native SOL
      transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: from,
          toPubkey: to,
          lamports: amount * LAMPORTS_PER_SOL
        })
      );
    } else {
      // Transfer SPL token
      const fromAta = await getAssociatedTokenAddress(mint, from);
      const toAta = await getAssociatedTokenAddress(mint, to);
      
      // Get mint info to determine decimals
      const mintInfo = await getMint(agent.connection, mint);
      const adjustedAmount = amount * Math.pow(10, mintInfo.decimals);

      transaction = new Transaction().add(
        createTransferInstruction(
          fromAta,
          toAta,
          agent.wallet_address,
          adjustedAmount
        )
      );
    }
    console.log("agent.wallet.publicKey",agent.wallet.publicKey.toString())
    let vTx = await ixsToVtx(transaction.instructions,agent.wallet.publicKey,[agent.wallet],agent.connection)
    return vTx;
  } catch (error: any) {
    throw new Error(`Transfer failed: ${error.message}`);
  }
}