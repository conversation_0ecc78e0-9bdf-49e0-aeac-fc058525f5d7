import { Keypair } from "@solana/web3.js";
import bs58 from "bs58";
export function createKeypairFromSecretKey(base58String: string) {
    // Decode the base58 secret key to a Uint8Array
    const secretKeyUint8Array = bs58.decode(base58String);

    // Check if the decoded secret key is 64 bytes
    if (secretKeyUint8Array.length !== 64) {
        throw new Error('Invalid secret key length. The key must be 64 bytes.');
    }

    // Create the keypair
    const keypair = Keypair.fromSecretKey(secretKeyUint8Array);

    return keypair;
}