import { ComputeBudgetProgram, LAMPORTS_PER_SOL, TransactionInstruction } from "@solana/web3.js";

export function makePriorityFeeIx(priorityFeeUi?: number): TransactionInstruction[] {
    const priorityFeeIx: TransactionInstruction[] = [];
    const limitCU = 500_000;

    let microLamports: number = 1;

    if (priorityFeeUi) {
      const priorityFeeMicroLamports = priorityFeeUi * LAMPORTS_PER_SOL * 500_000;
      microLamports = Math.round(priorityFeeMicroLamports / limitCU);
    }
    const cuRequestIxs = ComputeBudgetProgram.setComputeUnitLimit({ units: 500_000 });
    priorityFeeIx.push(cuRequestIxs);
    priorityFeeIx.push(
      ComputeBudgetProgram.setComputeUnitPrice({
        microLamports,
      })
    );

    return priorityFeeIx;
  }