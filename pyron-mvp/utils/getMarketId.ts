import { PERP_MARKETS } from "../constant";

export function getMarketId(ticker: string) {
  // Normalize ticker to uppercase
  const normalizedTicker = ticker.toUpperCase();
  
  // Check if the market exists in PERP_MARKETS
  if (!(normalizedTicker in PERP_MARKETS)) {
    console.log(`Market not found for ticker: ${ticker} (normalized: ${normalizedTicker})`);
    console.log("Available markets:", Object.keys(PERP_MARKETS));
    return undefined;
  }
  
  const market = PERP_MARKETS[normalizedTicker as keyof typeof PERP_MARKETS];
  const marketId = market ? market.marketId : undefined;
  
  return marketId;
}
