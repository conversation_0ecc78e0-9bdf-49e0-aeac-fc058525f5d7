import { Transaction } from "@solana/web3.js";
import { Connection } from "@solana/web3.js";
import base58 from "bs58";

export async function getPriorityFee(transaction:Transaction) {
    const heliusURL = process.env.RPC_URL;
    console.log(heliusURL);
    if (!heliusURL) {
        throw new Error("RPC_URL is not set");
    }
    const response = await fetch(heliusURL, {
        method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      jsonrpc: "2.0",
      id: "1",
      method: "getPriorityFeeEstimate",
      params: [
        {
          transaction: base58.encode(transaction.serialize()), // Pass the serialized transaction in Base58
          options: { 
           // "recommended": true ,
            "priorityLevel": "High"
        },
        },
      ],
    }),
  });
 const result = await response.json();
 console.log(result);
 return result.result.priorityFeeEstimate;
}
