import { VersionedT<PERSON>saction, PublicKey, LAMPORTS_PER_SOL } from "@solana/web3.js";
import { SolanaAgentKit } from "solana-agent-kit";
import { TOKENS, DEFAULT_OPTIONS, JUP_API } from "../constant";

/**
 * Swap tokens using Jupiter Exchange
 * @param agent SolanaAgentKit instance
 * @param outputMint Target token mint address
 * @param inputAmount Amount to swap (in token decimals)
 * @param inputMint Source token mint address (defaults to USDC)
 * @param slippageBps Slippage tolerance in basis points (default: 300 = 3%)
 * @returns Transaction signature
 */
export async function trade(
  agent: SolanaAgentKit,
  outputMint: PublicKey,
  inputAmount: number,
  inputMint: PublicKey = new PublicKey(TOKENS.USDC.address),
  slippageBps: number = DEFAULT_OPTIONS.SLIPPAGE_BPS,
  decimal: number = TOKENS.USDC.decimals,
): Promise<any> {
  try {
    // Get quote for the swap
    console.log(inputMint.toString(), outputMint.toString(), inputAmount, slippageBps);
    const quoteResponse = await (
      await fetch(
        `${JUP_API}/quote?` +
          `inputMint=${inputMint.toString()}` +
          `&outputMint=${outputMint.toString()}` +
          `&amount=${inputAmount * 10}` +
          `&slippageBps=${slippageBps}` +
          `&onlyDirectRoutes=true` +
          `&maxAccounts=20`,
      )
    ).json();
    console.log(quoteResponse);
    console.log(agent.wallet.publicKey.toString());
    // Get serialized transaction
    const response = await fetch("https://quote-api.jup.ag/v6/swap", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        quoteResponse,
        userPublicKey:agent.wallet_address,
        wrapAndUnwrapSol: true,
        dynamicComputeUnitLimit: true,
        prioritizationFeeLamports: "auto",
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch swap transaction: ${response.statusText}`);
    }

    const swapTransactionRes:any = await response.json();
    const swapTransaction = swapTransactionRes.swapTransaction;
    console.log(swapTransactionRes.swapTransaction);
    // Deserialize transaction
    const swapTransactionBuf = Buffer.from(swapTransaction, "base64");

    const transaction = VersionedTransaction.deserialize(swapTransactionBuf);
    // Sign and send transaction
    transaction.sign([agent.wallet]);
    console.log({transaction,outputAmount:quoteResponse.outAmount})
    return {transaction,outputAmount:quoteResponse.outAmount}
  } catch (error: any) {
    throw new Error(`Swap failed: ${error.message}`);
  }
}
