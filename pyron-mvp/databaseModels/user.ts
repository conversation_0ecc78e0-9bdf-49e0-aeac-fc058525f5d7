import mongoose, { Schema, Document } from 'mongoose';

interface IUser extends Document {
  walletAddress: string;
  displayName?: string;
  createdAt: Date;
  lastLoginAt: Date;
  settings?: Record<string, any>;
  apiKeys?: Record<string, any>;
}

const UserSchema: Schema = new Schema({
  walletAddress: { type: String, required: true, unique: true },
  displayName: { type: String },
  createdAt: { type: Date, default: Date.now },
  lastLoginAt: { type: Date, default: Date.now },
  settings: { type: Schema.Types.Mixed },
  apiKeys: { type: Schema.Types.Mixed }
});

export default mongoose.model<IUser>('User', UserSchema); 