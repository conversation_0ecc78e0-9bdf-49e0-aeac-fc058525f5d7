import mongoose, { Schema, Document } from 'mongoose';

interface IChatMessage extends Document {
  chatId: mongoose.Types.ObjectId;
  type: string;
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

const ChatMessageSchema: Schema = new Schema({
  chatId: { type: Schema.Types.ObjectId, ref: 'Chat', required: true },
  type: { type: String, required: true },
  content: { type: String, required: true },
  timestamp: { type: Date, default: Date.now },
  metadata: { type: Schema.Types.Mixed }
});

export default mongoose.model<IChatMessage>('ChatMessage', ChatMessageSchema); 