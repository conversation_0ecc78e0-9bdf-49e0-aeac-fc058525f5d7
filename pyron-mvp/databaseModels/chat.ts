import mongoose, { Schema, Document } from 'mongoose';

interface IChat extends Document {
  walletAddress: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  chatType: string;
  metadata?: Record<string, any>;
}

const ChatSchema: Schema = new Schema({
  walletAddress: { type: String, required: true },
  title: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  chatType: { type: String, default: 'general' },
  metadata: { type: Schema.Types.Mixed }
});

export default mongoose.model<IChat>('Chat', ChatSchema); 