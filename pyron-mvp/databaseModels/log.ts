import { Schema, model, Document } from 'mongoose';

export interface ILog extends Document {
  chatId:string;
  type: string;
  side: string;
  size: string;
  total: string;
  percentage: string;
  market:string;
  status:string;
  shown:number
  timestamps: string;
  webhookSignal: string;
}

const logSchema = new Schema<ILog>(
  {
    chatId: { type: String, required: true },
    type: { type: String, required: true },
    side: { type: String, required: true },
    size: { type: String, required: true },
    total: { type: String, required: false },
    percentage: { type: String, required: false },
    market: { type: String, required: true },
    status: { type: String, required: true },
    shown:{ type: Number, required: true },
    webhookSignal: { type: String, required: false },
    timestamps: { type: String, required: true }, // optional: adds createdAt and updatedAt fields
  }
);

const Log = model<ILog>('log', logSchema);

export default Log;
