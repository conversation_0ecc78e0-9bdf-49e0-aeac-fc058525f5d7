import { Schema, model, Document } from 'mongoose';
import { Types } from 'mongoose';

export interface IHypothesis extends Document {
  logId: Types.ObjectId;
  hypothesis: string;
  date: Date;
}

const hypothesisSchema = new Schema<IHypothesis>(
  {
    logId: { type: Schema.Types.ObjectId, ref: 'Log', required: true },
    hypothesis: { type: String, required: true },
    date: { type: Date, default: Date.now }
  },
  { timestamps: true }
);

const Hypothesis = model<IHypothesis>('Hypothesis', hypothesisSchema);

export default Hypothesis;