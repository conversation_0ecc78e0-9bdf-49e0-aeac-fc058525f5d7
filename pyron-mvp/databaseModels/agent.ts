import { Schema, model, Document } from 'mongoose';

export interface IAgent extends Document {
  agentName: string;
  assetPair: string;
  pubkey: string;
  buySignalMessage: string;
  sellSignalMessage: string;
  timestamps: string;
  tradingStatus: string;
  number: number;
  botId: string;
  chatId: string;
  // For opening a position:
  requiredSellConfirmationsOpen: number;   // e.g. 2
  requiredBuyConfirmationsOpen: number;
  requiredBuyConfirmationsOverride: number;
  requiredSellConfirmationsOverride: number;
  // For closing a position:
  requiredBuyConfirmationsClose: number;  // e.g. 1
  requiredSellConfirmationsClose: number;  // e.g. 1
  // For resetting the signal counter:
  requiredBuyConfirmationsResetCounter: number;
  requiredSellConfirmationsResetCounter: number;
  // For open bar signals:
  requiredBuyConfirmationsOpenBar: number;
  requiredSellConfirmationsOpenBar: number;
  // Track the signals state:
  signals: AgentSignals;
  deposit: number;
  sellReportPrompt: string;
  buyReportPrompt: string;
  hypothesisStatus: string;
}

interface AgentSignals {
  sell: number;
  buy: number;
  sellOpenBar: number;
  buyOpenBar: number;
}
const agentSchema = new Schema<IAgent>(
  {
    agentName: { type: String, required: true },
    assetPair: { type: String, required: true },
    pubkey: { type: String, required: true },
    buySignalMessage: { type: String, required: false },
    sellSignalMessage: { type: String, required: false },
    timestamps: { type: String, required: false }, // optional: adds createdAt and updatedAt fields
    tradingStatus: { type: String, required: false },
    number: { type: Number, required: false },
    botId: { type: String, required: true },
    chatId: { type: String, required: false },
    requiredBuyConfirmationsOpen: { type: Number, required: false },
    requiredSellConfirmationsOpen: { type: Number, required: false },
    requiredBuyConfirmationsOverride: { type: Number, required: false },
    requiredSellConfirmationsOverride: { type: Number, required: false },
    requiredBuyConfirmationsClose: { type: Number, required: false },
    requiredSellConfirmationsClose: { type: Number, required: false },
    requiredBuyConfirmationsResetCounter: { type: Number, required: false },
    requiredSellConfirmationsResetCounter: { type: Number, required: false },
    requiredBuyConfirmationsOpenBar: { type: Number, required: false },
    requiredSellConfirmationsOpenBar: { type: Number, required: false },
    signals: {
      type: new Schema(
        {
          sell: { type: Number, default: 0 },
          buy: { type: Number, default: 0 },
          sellOpenBar: { type: Number, default: 0 },
          buyOpenBar: { type: Number, default: 0 },
        },
        { _id: false } // Disable subdocument _id
      ),
      default: {}
    },
    deposit: { type: Number, required: true },
    sellReportPrompt: { type: String, required: false },
    buyReportPrompt: { type: String, required: false },
    hypothesisStatus: { type: String, required: false, default: 'off' },
  }
);

const Agent = model<IAgent>('Agent', agentSchema);

export default Agent;
