# Trading Bot System

This project is a Node.js-based REST API system for managing trading agents and related functionality. It provides a foundation for trading bot operations with user management, authentication, and chat features.

## Features

- **REST API**: Comprehensive endpoints for managing agents, users, trades, and chats
- **Authentication System**: Secure JWT-based authentication with cookie-based refresh tokens
- **Database Integration**: Stores agent configurations, users, chats, and logs in MongoDB
- **Trading Infrastructure**: Drift Protocol integration for Solana-based trading operations
- **Chat System**: Chat functionality with message management
- **Health Monitoring**: Health check endpoints for system monitoring
- **Agent Management**: CRUD operations for trading agent configurations

## Prerequisites

- **Node.js**: Version 14 or higher.
- **MongoDB**: For storing agent data, users, chats, and logs.
- **Solana Wallet**: With sufficient SOL for transaction fees (for trading operations).

### Test Environment Requirements

For running tests successfully, ensure the following are properly configured:

- **MongoDB**: Running instance accessible with credentials
- **Environment Variables**: All required variables set in `.env` file
- **Test Dependencies**: Mocha, Chai, Supertest, and ts-node installed
- **TypeScript**: Properly configured for test compilation

## Installation

1. **Clone the Repository**:
   ```bash
   git clone <repository-url>
   cd <project-directory>
   ```

2. **Install Dependencies**:
   ```bash
   npm install
   ```

3. **Environment Configuration**:
   - Create a `.env` file:
     ```plaintext
     ADMIN_KEY=         # Solana wallet private key
     RPC_URL=           # Solana RPC URL
     MONGO_HOST=        # MongoDB host
     MONGO_USER=        # MongoDB username
     MONGO_PASSWORD=    # MongoDB password
     MONGO_DB=          # MongoDB database name
     MONGO_PORT=        # MongoDB port
     PORT=              # API server port
     JWT_SECRET=        # Secret key for JWT token generation (required for tests)
     NODE_ENV=          # Environment (development/production)
     ```

## Testing

### Test Requirements

Before running tests, ensure the following requirements are met:

#### **1. Environment Variables**
All environment variables must be properly set in your `.env` file:

```plaintext
# Required for all tests
JWT_SECRET=your-secret-key-here
MONGO_HOST=localhost
MONGO_USER=your-mongo-username
MONGO_PASSWORD=your-mongo-password
MONGO_DB=your-test-database
MONGO_PORT=27017
PORT=3003

# Required for trading tests (optional for auth/health tests)
ADMIN_KEY=your-solana-private-key
RPC_URL=https://api.mainnet-beta.solana.com
```

#### **2. MongoDB Setup**
- MongoDB instance must be running and accessible
- Database user must have read/write permissions
- Test database should be separate from production data
- Connection string format: `********************************:port/database?authSource=admin`

#### **3. Dependencies**
Ensure all test dependencies are installed:
```bash
npm install --include=dev
```

Key test dependencies:
- `mocha`: Test runner
- `chai`: Assertion library
- `supertest`: HTTP testing
- `ts-node`: TypeScript execution
- `@types/mocha`, `@types/chai`, `@types/supertest`: TypeScript definitions

### Running Tests

#### **Run All Tests**
```bash
npm test
```

#### **Run Specific Test Suites**
```bash
# Auth Controller Tests
npx mocha --require ts-node/register tests/authController.test.ts

# Health Check Tests
npx mocha --require ts-node/register tests/healthCheck.test.ts

# Chat Controller Tests
npx mocha --require ts-node/register tests/chatController.test.ts
```

#### **Test Coverage**
Current test coverage includes:
- ✅ **Auth API** (6 tests): Token generation, refresh, logout, middleware
- ✅ **Chat API** (7 tests): CRUD operations, messaging
- ✅ **Health Check API** (1 test): Endpoint availability

### Test Results Expected

When all requirements are met, you should see:
```
  Auth API
    ✔ should generate a token for a valid wallet address
    ✔ should create a new user if wallet address does not exist
    ✔ should return 400 if wallet address is missing
    ✔ should refresh a token with a valid refresh token
    ✔ should logout a user by clearing the refresh token cookie
    ✔ should protect routes with the auth middleware

  Chat API
    ✔ should create a new chat
    ✔ should get all chats for a wallet
    ✔ should get a specific chat
    ✔ should update a chat
    ✔ should delete a chat
    ✔ should add a message to a chat
    ✔ should get messages for a chat

  Health Check API
    ✔ should return 200 OK for HEAD /api/health

  14 passing
```

### Troubleshooting Tests

#### **Common Issues:**

1. **MongoDB Connection Errors**
   ```
   Error: MONGO_USER, MONGO_PASSWORD, and MONGO_HOST must be set
   ```
   - Solution: Verify all MongoDB environment variables are set correctly

2. **JWT Secret Missing**
   ```
   Error: JWT_SECRET must be set in the environment variables
   ```
   - Solution: Add `JWT_SECRET=your-secret-key` to your `.env` file

3. **Port Already in Use**
   ```
   Error: listen EADDRINUSE: address already in use :::3003
   ```
   - Solution: Kill existing processes or change PORT in `.env`

4. **TypeScript Compilation Errors**
   ```
   Error: Cannot find module or type declarations
   ```
   - Solution: Run `npm install` to ensure all dependencies are installed

#### **Test Database Cleanup**
Tests automatically clean up test data, but you can manually clean the test database:
```bash
# Connect to MongoDB and drop test collections
mongo your-test-database --eval "db.users.deleteMany({walletAddress: /test/i})"
```

## Project Structure

- **/controller**: Contains API route controllers (auth, chat, agent, trade, user, log, hypothesis).
- **/routers**: Defines Express routes for all API endpoints.
- **/trade**: Contains trading logic including Drift Protocol integration and Jito bundling.
- **/utils**: Utility functions for keypair creation, market data, and Jupiter swaps.
- **/databaseModels**: MongoDB schemas (Agent, Chat, ChatMessage, Log, Hypothesis, User).
- **/middleware**: Authentication middleware for JWT token verification.
- **/types**: TypeScript type definitions.
- **/tests**: Unit tests for controllers and API endpoints.

## API Endpoints

### Authentication
- `POST /api/auth/token`: Generate access token and set refresh token cookie
- `POST /api/auth/refresh`: Refresh access token using cookie-based refresh token
- `POST /api/auth/logout`: Logout user and clear refresh token cookie
- `GET /api/auth/verify`: Verify current token validity

### Agents
- `POST /api/agents/add-agent`: Create a new trading agent
- `GET /api/agents/get-agentById/:id`: Retrieve an agent by ID
- `GET /api/agents/get-agentBybotId/:botId`: Retrieve an agent by bot ID
- `GET /api/agents/get-agentBychatId/:chatId`: Retrieve an agent by chat ID
- `GET /api/agents/get-agent/:pubkey/:assetPair`: Retrieve an agent by pubkey and asset pair
- `GET /api/agents/get-all-agents`: Retrieve all agents
- `DELETE /api/agents/delete-agent/:id`: Delete an agent
- `PUT /api/agents/update-trading-status/:botId`: Update the trading status of an agent
- `PUT /api/agents/update-hypothesis-status/:botId`: Update the hypothesis status of an agent
- `PUT /api/agents/save-or-update-agent/:botId`: Save or update an agent
- `PUT /api/agents/add-prompts-to-agent/:botId`: Add prompts to an agent
- `PUT /api/agents/update-agent-name/:id`: Update an agent's name

### Users
- `POST /api/users/add-user`: Add a new user
- `GET /api/users/:id`: Get a user by ObjectId
- `GET /api/users/wallet/:walletAddress`: Get user profile by wallet address
- `PATCH /api/users/wallet/:walletAddress`: Update user profile by wallet address
- `GET /api/users/wallet/:walletAddress/settings`: Get user settings by wallet address
- `PUT /api/users/wallet/:walletAddress/settings`: Update user settings by wallet address

### Chats
- `POST /api/chats`: Create a new chat
- `GET /api/chats/wallet/:walletAddress`: Get all chats for a wallet
- `GET /api/chats/:id`: Get a specific chat
- `PATCH /api/chats/:id`: Update a chat
- `DELETE /api/chats/:id`: Delete a chat
- `POST /api/chats/:id/messages`: Add a message to a chat
- `GET /api/chats/:id/messages`: Get messages for a chat

### Trading
- `GET /api/trade/get-drift-position`: Get the current trading position
- `GET /api/trade/get-user-activity`: Retrieve trading activity
- `GET /api/trade/get-user-assets`: Get account assets
- `GET /api/trade/get-user-positions`: Get account positions

### Logs
- `GET /api/logs/getAll`: Retrieve all trading logs
- `GET /api/logs/getLog/:id`: Get a single log by ID
- `POST /api/logs/add-log`: Create a new log
- `PUT /api/logs/:id`: Update a log by ID
- `DELETE /api/logs/:id`: Delete a log by ID
- `GET /api/logs/get-all-by-chat-id`: Retrieve logs by chat ID

### Hypothesis
- `POST /api/hypothesis/create`: Create a new hypothesis
- `GET /api/hypothesis/by-log-id/:logId`: Get hypotheses by log ID
- `GET /api/hypothesis/getAll`: Get all hypotheses
- `GET /api/hypothesis/by-chat-id/:chatId`: Get hypotheses by chat ID

### Health
- `HEAD /api/health`: Health check endpoint

## Authentication System

### Cookie-Based JWT Implementation

The system uses a secure, cookie-based JWT authentication system with the following features:

#### **Token Types:**
- **Access Token**: Short-lived (1 hour), sent in response body, used for API authorization
- **Refresh Token**: Long-lived (7 days), stored in httpOnly cookie, used for token renewal

#### **Security Features:**
- **HttpOnly Cookies**: Refresh tokens protected from XSS attacks
- **Secure Flag**: HTTPS-only transmission in production
- **SameSite Strict**: CSRF protection
- **Token Type Validation**: Prevents misuse of token types
- **Automatic Expiration**: Browser-managed cookie lifecycle

#### **Frontend Integration:**
To use the authentication system, frontend applications must include credentials in requests:

```javascript
// Login - generates access token and sets refresh token cookie
fetch('/api/auth/token', {
  method: 'POST',
  credentials: 'include', // Required for cookie handling
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ walletAddress: 'your-wallet-address' })
});

// API calls - use access token in Authorization header
fetch('/api/protected-endpoint', {
  credentials: 'include',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  }
});

// Token refresh - uses refresh token from cookie automatically
fetch('/api/auth/refresh', {
  method: 'POST',
  credentials: 'include'
});

// Logout - clears refresh token cookie
fetch('/api/auth/logout', {
  method: 'POST',
  credentials: 'include'
});
```

## Running the Application

1. **Start the Main Server**:
   ```bash
   npm start
   ```

The server will start on the port specified in your `.env` file (default: 3000).

## Available Scripts

- `npm start`: Start the main application server
- `npm test`: Run all unit tests

## Agent Configuration

- **Asset Pair Selection**: Configure the asset pairs for trading agents
- **Trading Status**: Enable or disable trading for specific agents
- **Signal Configuration**: Set up buy/sell signal messages and confirmation requirements

