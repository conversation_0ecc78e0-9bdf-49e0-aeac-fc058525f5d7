import { Router } from 'express';
import {
  getChatsByWalletAddress,
  createChat,
  getChatById,
  updateChat,
  deleteChat,
  getChatMessages,
  addChatMessage,
  getChatsByWalletAddressWithAgents
} from '../controller/chatController';
import { authMiddleware, walletOwnershipMiddleware } from '../middleware/authMiddleware';

const chatRouter = Router();

// Add a new chat
chatRouter.post('/add-chat', authMiddleware, createChat);

// Get chats by wallet address
chatRouter.get('/wallet/:walletAddress', authMiddleware, walletOwnershipMiddleware, getChatsByWalletAddress);
chatRouter.get('/wallet/:walletAddress/agents', authMiddleware, walletOwnershipMiddleware, getChatsByWalletAddressWithAgents);

// General chat routes - all protected by authentication
chatRouter.get('/', authMiddleware, getChatsByWalletAddress);
chatRouter.post('/', authMiddleware, createChat);
chatRouter.get('/:chatId', authMiddleware, getChatById);
chatRouter.patch('/:chatId', authMiddleware, updateChat);
chatRouter.delete('/:chatId', authMiddleware, deleteChat);
chatRouter.get('/:chatId/messages', authMiddleware, getChatMessages);
chatRouter.post('/:chatId/messages', authMiddleware, addChatMessage);

export default chatRouter;