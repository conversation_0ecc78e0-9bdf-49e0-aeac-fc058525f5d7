import express from 'express';
import {
  addUser<PERSON>ontroller,
  getUserByIdController,
  getUserProfile,
  updateUserProfile,
  getUserSettings,
  updateUserSettings,
} from '../controller/userController';
import { authMiddleware, walletOwnershipMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

// Add a new user - public route
router.post('/add-user', addUserController);

// Get a user by ObjectId - protected route
router.get('/:id', authMiddleware, getUserByIdController);

// Wallet-specific routes - protected and require wallet ownership
router.get('/wallet/:walletAddress', authMiddleware, walletOwnershipMiddleware, getUserProfile);
router.patch('/wallet/:walletAddress', authMiddleware, walletOwnershipMiddleware, updateUserProfile);
router.get('/wallet/:walletAddress/settings', authMiddleware, walletOwnershipMiddleware, getUserSettings);
router.put('/wallet/:walletAddress/settings', authMiddleware, walletOwnershipMiddleware, updateUserSettings);

export default router;
