import express from 'express';
import { getAccountActivity, getPosition, getAccountAssets, getAccountPositions } from '../controller/tradeController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

// Protected routes - require authentication
router.get('/get-drift-position', authMiddleware, getPosition);
router.get('/get-user-activity', authMiddleware, getAccountActivity);
router.get('/get-user-assets', authMiddleware, getAccountAssets);
router.get('/get-user-positions', authMiddleware, getAccountPositions);

export default router;
