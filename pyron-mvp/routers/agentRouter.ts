import express from 'express';
import {
  createAgent,
  getAgentById,
  deleteAgent,
  getAgentByPubkeyAndAssetPair,
  updateTradingStatus,
  getAgentBybotId,
  saveOrUpdateAgent,
  getAgentBychatId,
  getAllAgents,
  addPromptsToAgent,
  updateHypothesisStatus,
  updateAgentName
} from '../controller/agentController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

/**
 * Create an Agent
 * POST /agents
 */
router.post('/add-agent', authMiddleware, createAgent);

/**
 * Get an Agent by ID
 * GET /agents/:id
 */
router.get('/get-agentById/:id', authMiddleware, getAgentById);
router.get('/get-agentBybotId/:botId', authMiddleware, getAgentBybotId);
router.get('/get-agentBychatId/:chatId', authMiddleware, getAgentBychatId);
router.get('/get-agent/:pubkey/:assetPair', authMiddleware, getAgentByPubkeyAndAssetPair);
router.get('/get-all-agents', authMiddleware, getAllAgents);


/**
 * Delete an Agent
 * DELETE /agents/:id
 */
router.delete('/delete-agent/:id', authMiddleware, deleteAgent);

router.put('/update-trading-status/:botId', authMiddleware, updateTradingStatus);
router.put('/update-hypothesis-status/:botId', authMiddleware, updateHypothesisStatus);
router.put('/save-or-update-agent/:botId', authMiddleware, saveOrUpdateAgent);
router.put('/add-prompts-to-agent/:botId', authMiddleware, addPromptsToAgent);
router.put('/update-agent-name/:id', authMiddleware, updateAgentName);

export default router;
