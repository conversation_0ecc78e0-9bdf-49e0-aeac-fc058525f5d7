import { Router } from 'express';
import {
  createLog,
  getLog,
  getLogs,
  updateLog,
  deleteLog,
  getLogByChatId,
} from '../controller/logController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = Router();

// Route to get all logs
router.get('/getAll', authMiddleware, getLogs);

// Route to get a single log by ID
router.get('/getLog/:id', authMiddleware, getLog);

// Route to create a new log
router.post('/add-log', authMiddleware, createLog);

// Route to update a log by ID
router.put('/:id', authMiddleware, updateLog);

// Route to delete a log by ID
router.delete('/:id', authMiddleware, deleteLog);

// Route to get logs by agentId
router.get('/get-all-by-chat-id', authMiddleware, getLogByChatId);


export default router;
