import { Router } from 'express';
import {
  createHypothesis,
  getHypothesesByLogId,
  getAllHypotheses,
  getHypothesesByChatId
} from '../controller/hypothesisController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = Router();

// Route to create a new hypothesis
router.post('/create', authMiddleware, createHypothesis);

// Route to get hypotheses by logId
router.get('/by-log-id/:logId', authMiddleware, getHypothesesByLogId);

// Route to get all hypotheses
router.get('/getAll', authMiddleware, getAllHypotheses);

// Route to get hypotheses by chatId
router.get('/by-chat-id/:chatId', authMiddleware, getHypothesesByChatId);

export default router;