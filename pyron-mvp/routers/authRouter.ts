import { Router } from 'express';
import { generateAuthToken, refreshToken, logout } from '../controller/authController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = Router();

/**
 * @route POST /api/auth/token
 * @desc Generate a new JWT token for a wallet address
 * @access Public
 */
router.post('/token', generateAuthToken);

/**
 * @route POST /api/auth/refresh
 * @desc Refresh an existing JWT token
 * @access Public
 */
router.post('/refresh', refreshToken);

/**
 * @route POST /api/auth/logout
 * @desc Logout a user by revoking their refresh token
 * @access Public
 */
router.post('/logout', logout);

/**
 * @route GET /api/auth/verify
 * @desc Verify if the current token is valid
 * @access Protected
 */
router.get('/verify', authMiddleware, (req, res) => {
  // If we get here, the token is valid (authMiddleware passed)
  res.status(200).json({
    valid: true,
    user: { walletAddress: req.user?.walletAddress }
  });
});

export default router;
