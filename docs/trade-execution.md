                             **Trade Execution Architecture**

### **Frontend Workflow:**

1. **User Wallet Connection**: The user connects using their wallet.

2. **Agent Creation**: save agent data on db  
3. **Subaccount creation**: When submitting a trade, the frontend prepares the following:

   * **Create Drift Subaccount**: Initializes a subaccount for the user drift account.

   * **Deposit Instruction**: Deposits funds from the user’s wallet to the created subaccount.

4. **Subaccount Delegation**:

   * Prepares an instruction to delegate the subaccount to the admin wallet.

5. **Atomic Transaction Execution**:

   * All prepared instructions are sent in a single atomic transaction to ensure consistency.

### **Backend Workflow:**

1. **Webhook Handling**:

   * Upon receiving a webhook alert, the backend initializes a drift client with:

     * The admin wallet.

     * The user’s address as authority.

2. **Trade Execution**:

   * Executes the trade on the agent’s subaccount, which was delegated in the frontend.

