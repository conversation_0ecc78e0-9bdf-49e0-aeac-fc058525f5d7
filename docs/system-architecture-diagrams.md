# PyRon System Architecture Diagrams

This document contains comprehensive architecture diagrams for the PyRon trading system, illustrating the relationships and data flows between all components.

## 1. High-Level System Overview

```mermaid
graph TB
    subgraph "External Systems"
        TS[Trading Signals]
        SP[Signal Providers]
        SN[Solana Network]
        DP[Drift Protocol]
    end
    
    subgraph "PyRon System"
        subgraph "Frontend Layer"
            WA[PyRon WebApp<br/>React + TypeScript]
        end
        
        subgraph "Backend Layer"
            API[PyRon MVP API<br/>Express.js + MongoDB]
            WH[Webhook Service<br/>Express.js + Redis]
        end
        
        subgraph "Data Layer"
            DB[(MongoDB<br/>Database)]
            RQ[(Redis<br/>Queue)]
        end
    end
    
    subgraph "Users"
        U[Traders]
        W[Wallets]
    end
    
    %% User interactions
    U --> WA
    W --> WA
    
    %% Frontend to backend
    WA --> API
    
    %% External signals
    TS --> WH
    SP --> WH
    
    %% Backend interactions
    API --> DB
    WH --> RQ
    WH --> DB
    
    %% Blockchain interactions
    API --> SN
    WH --> DP
    DP --> SN
    
    %% Styling
    classDef frontend fill:#e1f5fe
    classDef backend fill:#f3e5f5
    classDef data fill:#e8f5e8
    classDef external fill:#fff3e0
    classDef user fill:#fce4ec
    
    class WA frontend
    class API,WH backend
    class DB,RQ data
    class TS,SP,SN,DP external
    class U,W user
```

## 2. PyRon WebApp Architecture

```mermaid
graph TB
    subgraph "Browser Environment"
        subgraph "React Application"
            subgraph "Pages"
                IP[Index Page]
                NF[Not Found]
            end
            
            subgraph "Components"
                CI[Chat Interface]
                SM[Sidebar Menu]
                HD[Header]
                MC[Main Chart]
                CS[Connection Status]
            end
            
            subgraph "Context & State"
                CP[Chat Provider]
                WS[Wallet Store]
                NS[Network Status]
            end
            
            subgraph "Services"
                AS[Auth Service]
                TS[Trading Service]
                CS2[Chat Service]
            end
            
            subgraph "Libraries"
                DC[Drift Client]
                SW[Solana Web3]
                LC[Lightweight Charts]
            end
        end
    end
    
    subgraph "External APIs"
        BA[Backend API]
        SRN[Solana RPC]
    end
    
    %% Component relationships
    IP --> CI
    IP --> SM
    IP --> HD
    CI --> MC
    
    %% State management
    WS --> CI
    WS --> HD
    CP --> CI
    NS --> CS
    
    %% Service interactions
    AS --> BA
    TS --> DC
    TS --> SW
    CS2 --> BA
    
    %% External connections
    DC --> SRN
    SW --> SRN
    
    %% Styling
    classDef page fill:#e3f2fd
    classDef component fill:#f1f8e9
    classDef state fill:#fff8e1
    classDef service fill:#fce4ec
    classDef library fill:#f3e5f5
    classDef external fill:#efebe9
    
    class IP,NF page
    class CI,SM,HD,MC,CS component
    class CP,WS,NS state
    class AS,TS,CS2 service
    class DC,SW,LC library
    class BA,SRN external
```

## 3. PyRon MVP Backend Architecture

```mermaid
graph TB
    subgraph "Express.js Application"
        subgraph "Routes"
            AR[Auth Router]
            UR[User Router]
            AGR[Agent Router]
            TR[Trade Router]
            CR[Chat Router]
            LR[Log Router]
            HR[Hypothesis Router]
        end
        
        subgraph "Controllers"
            AC[Auth Controller]
            UC[User Controller]
            AGC[Agent Controller]
            TC[Trade Controller]
            CC[Chat Controller]
            LC[Log Controller]
            HC[Hypothesis Controller]
        end
        
        subgraph "Middleware"
            AM[Auth Middleware]
            WM[Wallet Ownership]
            VM[Validation Middleware]
        end
        
        subgraph "Services"
            subgraph "Trading Services"
                DS[Drift Service]
                JS[Jito Service]
                JUS[Jupiter Service]
            end
            
            subgraph "Utility Services"
                KS[Keypair Service]
                MS[Market Service]
                LS[Logging Service]
            end
        end
        
        subgraph "Database Models"
            UM[User Model]
            AGM[Agent Model]
            CM[Chat Model]
            LM[Log Model]
            HM[Hypothesis Model]
        end
    end
    
    subgraph "External Systems"
        DB[(MongoDB)]
        SN[Solana Network]
        DP[Drift Protocol]
        JP[Jupiter Protocol]
        JB[Jito Bundler]
    end
    
    %% Route to controller mapping
    AR --> AC
    UR --> UC
    AGR --> AGC
    TR --> TC
    CR --> CC
    LR --> LC
    HR --> HC
    
    %% Middleware usage
    AM --> UC
    AM --> AGC
    AM --> TC
    WM --> UC
    
    %% Controller to service mapping
    TC --> DS
    TC --> JS
    TC --> JUS
    AGC --> KS
    TC --> MS
    
    %% Model interactions
    UC --> UM
    AGC --> AGM
    CC --> CM
    LC --> LM
    HC --> HM
    
    %% Database connections
    UM --> DB
    AGM --> DB
    CM --> DB
    LM --> DB
    HM --> DB
    
    %% External service connections
    DS --> DP
    JS --> JB
    JUS --> JP
    DS --> SN
    
    %% Styling
    classDef router fill:#e8f5e8
    classDef controller fill:#e1f5fe
    classDef middleware fill:#fff3e0
    classDef service fill:#f3e5f5
    classDef model fill:#fce4ec
    classDef external fill:#efebe9
    
    class AR,UR,AGR,TR,CR,LR,HR router
    class AC,UC,AGC,TC,CC,LC,HC controller
    class AM,WM,VM middleware
    class DS,JS,JUS,KS,MS,LS service
    class UM,AGM,CM,LM,HM model
    class DB,SN,DP,JP,JB external
```

## 4. PyRon Webhook Service Architecture

```mermaid
graph TB
    subgraph "Webhook Service"
        subgraph "API Layer"
            WR[Webhook Router]
            WC[Webhook Controller]
        end
        
        subgraph "Middleware Stack"
            IPF[IP Filter]
            WHF[Webhook Filter]
            VM[Validation Middleware]
        end
        
        subgraph "Queue System"
            AQ[Alert Queue<br/>BullMQ]
            WK[Worker Pool]
            DLQ[Dead Letter Queue]
        end
        
        subgraph "Processing Engine"
            PA[Process Alert]
            TL[Trading Logic]
            PM[Position Manager]
            OM[Order Manager]
        end
        
        subgraph "Database Models"
            AM[Agent Model]
            UM[User Model]
            LM[Log Model]
        end
    end
    
    subgraph "External Systems"
        TS[Trading Signals]
        RD[(Redis)]
        DB[(MongoDB)]
        DP[Drift Protocol]
        SN[Solana Network]
    end
    
    %% Request flow
    TS --> WR
    WR --> IPF
    IPF --> WHF
    WHF --> VM
    VM --> WC
    WC --> AQ
    
    %% Queue processing
    AQ --> WK
    WK --> PA
    PA --> TL
    TL --> PM
    PM --> OM
    
    %% Failed job handling
    WK --> DLQ
    
    %% Database interactions
    PA --> AM
    PA --> UM
    TL --> LM
    AM --> DB
    UM --> DB
    LM --> DB
    
    %% Queue storage
    AQ --> RD
    WK --> RD
    DLQ --> RD
    
    %% Trading execution
    OM --> DP
    DP --> SN
    
    %% Styling
    classDef api fill:#e8f5e8
    classDef middleware fill:#fff3e0
    classDef queue fill:#e1f5fe
    classDef processing fill:#f3e5f5
    classDef model fill:#fce4ec
    classDef external fill:#efebe9
    
    class WR,WC api
    class IPF,WHF,VM middleware
    class AQ,WK,DLQ queue
    class PA,TL,PM,OM processing
    class AM,UM,LM model
    class TS,RD,DB,DP,SN external
```

## 5. Data Flow Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant WA as WebApp
    participant API as MVP API
    participant WH as Webhook
    participant Q as Queue
    participant W as Worker
    participant DP as Drift Protocol
    participant SN as Solana Network
    
    %% User authentication flow
    U->>WA: Connect Wallet
    WA->>API: Request JWT Token
    API->>WA: Return Token
    WA->>U: Show Dashboard
    
    %% Agent creation flow
    U->>WA: Create Trading Agent
    WA->>API: POST /agents/add-agent
    API->>API: Store Agent Config
    API->>WA: Return Agent ID
    
    %% Webhook signal processing
    Note over WH: External Signal Received
    WH->>WH: IP & Signal Filtering
    WH->>Q: Add Job to Queue
    Q->>W: Process Job
    W->>W: Validate Agent & Signal
    W->>DP: Execute Trade
    DP->>SN: Submit Transaction
    SN->>DP: Confirm Transaction
    DP->>W: Return Result
    W->>API: Log Trade Result
    
    %% Real-time updates
    API->>WA: WebSocket Update (Future)
    WA->>U: Show Trade Result
```

## 6. Security Architecture

```mermaid
graph TB
    subgraph "Security Layers"
        subgraph "Network Security"
            CF[Cloudflare/CDN]
            LB[Load Balancer]
            FW[Firewall]
        end
        
        subgraph "Application Security"
            CORS[CORS Policy]
            RL[Rate Limiting]
            IPF[IP Filtering]
            JWT[JWT Authentication]
        end
        
        subgraph "Data Security"
            ENC[Encryption at Rest]
            TLS[TLS in Transit]
            VAL[Input Validation]
            SAN[Data Sanitization]
        end
        
        subgraph "Blockchain Security"
            WS[Wallet Security]
            TS[Transaction Signing]
            SC[Smart Contract]
            MS[Multi-sig (Future)]
        end
    end
    
    subgraph "External Threats"
        DDoS[DDoS Attacks]
        BOT[Bot Traffic]
        INJ[Injection Attacks]
        MITM[Man-in-Middle]
        MEV[MEV Attacks]
    end
    
    %% Security protections
    CF --> DDoS
    LB --> BOT
    FW --> INJ
    CORS --> MITM
    RL --> BOT
    IPF --> BOT
    JWT --> INJ
    ENC --> MITM
    TLS --> MITM
    VAL --> INJ
    SAN --> INJ
    WS --> MEV
    TS --> MEV
    SC --> MEV
    
    %% Styling
    classDef security fill:#ffebee
    classDef threat fill:#f3e5f5
    
    class CF,LB,FW,CORS,RL,IPF,JWT,ENC,TLS,VAL,SAN,WS,TS,SC,MS security
    class DDoS,BOT,INJ,MITM,MEV threat
```

## 7. Deployment Architecture

```mermaid
graph TB
    subgraph "Production Environment"
        subgraph "Frontend Deployment"
            CDN[CDN Distribution]
            WEB[Web Servers]
        end
        
        subgraph "Backend Deployment"
            API1[API Server 1]
            API2[API Server 2]
            WH1[Webhook Server 1]
            WH2[Webhook Server 2]
        end
        
        subgraph "Data Layer"
            DB1[(MongoDB Primary)]
            DB2[(MongoDB Secondary)]
            RD1[(Redis Primary)]
            RD2[(Redis Secondary)]
        end
        
        subgraph "Monitoring"
            LOG[Logging Service]
            MON[Monitoring]
            ALT[Alerting]
        end
    end
    
    subgraph "External Services"
        SOL[Solana RPC]
        DRIFT[Drift Protocol]
        JITO[Jito Bundler]
    end
    
    %% Load balancing
    CDN --> WEB
    WEB --> API1
    WEB --> API2
    WEB --> WH1
    WEB --> WH2
    
    %% Database replication
    DB1 --> DB2
    RD1 --> RD2
    
    %% Service connections
    API1 --> DB1
    API2 --> DB1
    WH1 --> RD1
    WH2 --> RD1
    WH1 --> DB1
    WH2 --> DB1
    
    %% External connections
    API1 --> SOL
    API2 --> SOL
    WH1 --> DRIFT
    WH2 --> DRIFT
    WH1 --> JITO
    WH2 --> JITO
    
    %% Monitoring connections
    API1 --> LOG
    API2 --> LOG
    WH1 --> LOG
    WH2 --> LOG
    LOG --> MON
    MON --> ALT
    
    %% Styling
    classDef frontend fill:#e3f2fd
    classDef backend fill:#f1f8e9
    classDef data fill:#fff8e1
    classDef monitoring fill:#fce4ec
    classDef external fill:#efebe9
    
    class CDN,WEB frontend
    class API1,API2,WH1,WH2 backend
    class DB1,DB2,RD1,RD2 data
    class LOG,MON,ALT monitoring
    class SOL,DRIFT,JITO external
```
