1. ssh git
2. monitoring?
3. where do the different pieces of the code run?
4. what are the entry points for admins, users, etc.
5. What are the different roles?
6. MitM clipbaord attack
7. Monitoring with 
    a. https://workos.com/radar

Implementation questions:

1. How to handle the different roles?
2. How to handle authentication?
3. How ot handle authorization?
4. How to handle the different workflows?
5. How to handle the different timeframes?
6. How to handle the different strategies?
7. How to handle many alerts from different users?
8. How to store admin wallet secrets securely ?
9. How to send error notifications when a trade fails (<PERSON><PERSON><PERSON>, Telegram, Email)?
10. How to prevent one misbehaving agent from blocking the system?
11. How to make sure the alerts ?
13. How to secure the source of api requests?
12. How to securely authenticate users via wallet signatures (e.g., Solana signMessage)?
13. How to verify ownership of Crossmint or Phantom wallets before assigning permissions?
14. How to store and manage user sessions (JWT, cookie, refresh tokens)?

Thor:➜ pyron-security/pyron-webhook git:(main) ✗  yarn audit
yarn audit v1.22.22
info No lockfile found.
warning @types/mongoose@5.11.97: Mongoose publishes its own types, so you do not need to install this package.
warning solana-agent-kit > rpc-websockets@10.0.0: deprecate 10.0.0
warning jest > jest-cli > jest-config > glob@7.2.3: Glob versions prior to v9 are no longer supported
warning jest > @jest/core > jest-runtime > glob@7.2.3: Glob versions prior to v9 are no longer supported
warning jest > @jest/core > @jest/reporters > glob@7.2.3: Glob versions prior to v9 are no longer supported
warning jest > jest-cli > jest-config > glob > inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
warning jest > @jest/core > @jest/transform > babel-plugin-istanbul > test-exclude > glob@7.2.3: Glob versions prior to v9 are no longer supported

| Severity | Description                                                                 |
| -------- | --------------------------------------------------------------------------- |
| high     | Handling untrusted input can result in a crash, leading to loss of availability / denial of service |
| Package  | @solana/web3.js                                                            |
| Patched in | >=1.73.5                                                                  |
| Dependency of | @drift-labs/sdk                                                        |
| Path     | @drift-labs/sdk > @solana/web3.js                                         |
| More info | https://www.npmjs.com/advisories/1097073                                  |

| Severity | Description                                                                 |
| -------- | --------------------------------------------------------------------------- |
| high     | bigint-buffer Vulnerable to Buffer Overflow via toBigIntLE() Function      |
| Package  | bigint-buffer                                                               |
| Patched in | No patch available                                                        |
| Dependency of | @crossmint/wallets-sdk                                                |
| Path     | @crossmint/wallets-sdk > @solana/web3.js > bigint-buffer                  |
| More info | https://www.npmjs.com/advisories/1103747                                  |

| Severity | Description                                                                 |
| -------- | --------------------------------------------------------------------------- |
| high     | bigint-buffer Vulnerable to Buffer Overflow via toBigIntLE() Function      |
| Package  | bigint-buffer                                                               |
| Patched in | No patch available                                                        |
| Dependency of | @drift-labs/sdk                                                        |
| Path     | @drift-labs/sdk > @solana/spl-token > @solana/buffer-layout-utils > bigint-buffer |
| More info | https://www.npmjs.com/advisories/1103747                                  |

| Severity | Description                                                                 |
| -------- | --------------------------------------------------------------------------- |
| high     | bigint-buffer Vulnerable to Buffer Overflow via toBigIntLE() Function      |
| Package  | bigint-buffer                                                               |
| Patched in | No patch available                                                        |
| Dependency of | @drift-labs/sdk                                                        |
| Path     | @drift-labs/sdk > @ellipsis-labs/phoenix-sdk > @solana/spl-token > @solana/buffer-layout-utils > bigint-buffer |
| More info | https://www.npmjs.com/advisories/1103747                                  |

4 vulnerabilities found - Packages audited: 817
Severity: 4 High