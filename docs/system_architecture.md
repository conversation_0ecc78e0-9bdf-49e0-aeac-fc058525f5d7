# System Architecture

## Overview

The NOVA project utilizes a microservices architecture to ensure scalability, maintainability, and resilience. This approach involves breaking down the application into smaller, independent services that communicate with each other over a network. Each microservice is responsible for a specific business function, allowing teams to develop, deploy, and scale them independently.

## Microservices

The following microservices are part of the NOVA project:

*  **Web Application:** This microservice handles the user interface and provides a platform for users to interact with the system. It is responsible for rendering the front end, handling user requests, and managing user authentication and authorization.
*  **Backend:** This microservice implements the core trade logic and future trade perspectives. It is responsible for processing trade-related data, generating insights, and managing the trade lifecycle.
*  **New Front End:** This microservice replaces the old front end and provides an improved user experience. It is responsible for displaying trade-related information, interacting with the backend, and providing real-time updates to the user.

## Architecture Diagram

```mermaid
graph LR
  subgraph User
    U[User]
  end

  subgraph Web Application
    WA[Web Application]
  end

  subgraph Backend
    B[Backend]
  end

  subgraph New Front End
    FE[New Front End]
  end

  subgraph Cloud Services
    DB[(Database)]
    MQ[(Message Queue)]
    Cache[(Cache)]
  end

  U --> WA
  WA -- "REST API" --> B
  WA -- "REST API" --> FE
  B -- "MongoDB" --> DB
  B --> MQ
  FE --> Cache
```

## Technologies

The following technologies are used in the NOVA project:

*  **Backend:**
  *  **Golang:** Used for the backend microservice due to its performance and concurrency capabilities.
  *  **@drift-labs/sdk:** For interacting with the Drift protocol.
  *  **@solana/web3.js:** For interacting with the Solana blockchain.
  *  **express:** A web framework for Node.js.
  *  **mongoose:** An Object Data Modeling (ODM) library for MongoDB.
  *  **ioredis:** A Redis client for Node.js.
  *  **bullmq:** A queue library for Node.js.
*  **Frontend:**
  *  **react:** A JavaScript library for building user interfaces.
  *  **react-dom:** Provides DOM-specific methods for React.
  *  **react-router-dom:** For routing in React applications.
  *  **vite:** A build tool that aims to provide a faster and leaner development experience for modern web projects.
  *  **tailwindcss:** A utility-first CSS framework.
  *  **@radix-ui/react-\*:** A set of React components for building accessible user interfaces.
  *  **zustand:** A small, fast and scalable bearbones state-management solution.
  *  **@supabase/supabase-js:** A JavaScript client library for interacting with Supabase.
*  **Cloud Hosting Platforms (AWS, Azure, or Google Cloud):** Used to provide unlimited scalability and managed services.

## Scalability

The microservices architecture allows for independent scaling of each service. This means that if the backend microservice experiences high traffic, it can be scaled independently of the web application and new front end.

## Security

Each microservice is designed with security in mind. Secure coding practices, well-maintained libraries and frameworks, and compliance with international security standards like ISO 27001 are followed.

## Communication

Microservices communicate with each other using APIs. This allows for loose coupling and independent development.

## Deployment

Each microservice is deployed independently using continuous deployment practices. This allows for rapid iteration and faster time to market.