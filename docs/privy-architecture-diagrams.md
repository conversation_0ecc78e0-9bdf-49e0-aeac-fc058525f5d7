# Privy.io Architecture Diagrams

This document contains comprehensive architecture diagrams for Privy.io's wallet infrastructure and user onboarding platform.

## 1. High-Level Privy.io System Architecture

```mermaid
graph TB
    subgraph "End Users"
        NU[New Users<br/>Web3 Beginners]
        EU[Existing Users<br/>Crypto Experienced]
    end
    
    subgraph "Developer Applications"
        DA[dApp Frontend]
        DB[dApp Backend]
    end
    
    subgraph "Privy Platform"
        subgraph "Client-Side Integration"
            SDK[Privy SDKs<br/>React, React Native, Swift]
            AUI[Authentication UI]
            WUI[Wallet UI]
            EUI[Embedded Wallet UI]
        end
        
        subgraph "Privy Backend Services"
            AG[API Gateway /<br/>Load Balancers]
            
            subgraph "Core Services"
                UAS[User Authentication<br/>Service]
                EWMS[Embedded Wallet<br/>Management Service]
                EWCS[External Wallet<br/>Connector Service]
                TOS[Transaction Orchestration<br/>& Signing Service]
            end
            
            subgraph "Supporting Services"
                PE[Policy Engine]
                SS[Session Signers /<br/>Server Wallet API]
                BIS[Blockchain Interaction<br/>Service]
                IS[Indexing Service<br/>& Webhooks]
                DC[Developer Console &<br/>Management API]
            end
        end
        
        subgraph "Security Infrastructure"
            subgraph "Secure Execution Environments"
                CSE[Client-Side SEE<br/>Browser iFrame]
                SSE[Server-Side SEE<br/>Trusted Execution Environments]
            end
            
            subgraph "Key Management"
                DS[Device Share<br/>Local Storage]
                AS[Auth Share<br/>Encrypted Backend]
                RS[Recovery Share<br/>Optional]
            end
        end
    end
    
    subgraph "External Systems"
        subgraph "Identity Providers"
            GOOGLE[Google]
            APPLE[Apple]
            TWITTER[X/Twitter]
            DISCORD[Discord]
            GITHUB[GitHub]
        end
        
        subgraph "Communication Services"
            SMS[SMS Gateway]
            EMAIL[Email Gateway]
        end
        
        subgraph "Blockchain Networks"
            ETH[Ethereum]
            POLY[Polygon]
            SOL[Solana]
            BASE[Base]
            ARB[Arbitrum]
            BTC[Bitcoin]
        end
        
        subgraph "External Wallets"
            MM[MetaMask]
            WC[WalletConnect]
            PHANTOM[Phantom]
            CB[Coinbase Wallet]
        end
        
        subgraph "Infrastructure"
            INFURA[Infura]
            ALCHEMY[Alchemy]
            TEE[Secure Hardware<br/>Intel SGX, AMD SEV]
        end
    end
    
    %% User interactions
    NU --> SDK
    EU --> SDK
    
    %% dApp integration
    DA --> SDK
    DB --> AG
    
    %% SDK components
    SDK --> AUI
    SDK --> WUI
    SDK --> EUI
    
    %% API Gateway routing
    AG --> UAS
    AG --> EWMS
    AG --> EWCS
    AG --> TOS
    AG --> PE
    AG --> SS
    AG --> BIS
    AG --> IS
    AG --> DC
    
    %% Authentication flows
    UAS --> GOOGLE
    UAS --> APPLE
    UAS --> TWITTER
    UAS --> DISCORD
    UAS --> GITHUB
    UAS --> SMS
    UAS --> EMAIL
    
    %% Wallet management
    EWMS --> CSE
    EWMS --> SSE
    EWMS --> DS
    EWMS --> AS
    EWMS --> RS
    
    %% External wallet connections
    EWCS --> MM
    EWCS --> WC
    EWCS --> PHANTOM
    EWCS --> CB
    
    %% Blockchain interactions
    BIS --> ETH
    BIS --> POLY
    BIS --> SOL
    BIS --> BASE
    BIS --> ARB
    BIS --> BTC
    BIS --> INFURA
    BIS --> ALCHEMY
    
    %% Secure execution
    SSE --> TEE
    
    %% Styling
    classDef user fill:#fce4ec
    classDef dapp fill:#e8f5e8
    classDef sdk fill:#e3f2fd
    classDef core fill:#fff3e0
    classDef support fill:#f1f8e9
    classDef security fill:#ffebee
    classDef external fill:#f3e5f5
    classDef blockchain fill:#e1f5fe
    
    class NU,EU user
    class DA,DB dapp
    class SDK,AUI,WUI,EUI sdk
    class UAS,EWMS,EWCS,TOS core
    class PE,SS,BIS,IS,DC support
    class CSE,SSE,DS,AS,RS security
    class GOOGLE,APPLE,TWITTER,DISCORD,GITHUB,SMS,EMAIL,MM,WC,PHANTOM,CB,INFURA,ALCHEMY,TEE external
    class ETH,POLY,SOL,BASE,ARB,BTC blockchain
```

## 2. Embedded Wallet Key Management Architecture

```mermaid
graph TB
    subgraph "User Device"
        subgraph "Browser Environment"
            DAPP[dApp Interface]
            subgraph "Sandboxed iFrame"
                KR[Key Reconstruction]
                TS[Transaction Signing]
                DS[Device Share<br/>Encrypted Local Storage]
            end
        end
    end
    
    subgraph "Privy Backend"
        subgraph "Wallet Management Service"
            WMS[Wallet Management<br/>Service]
            AS[Auth Share<br/>Encrypted Storage]
            PE[Policy Engine]
        end
        
        subgraph "Authentication Service"
            AUTH[User Authentication]
            JWT[JWT Token<br/>Management]
        end
        
        subgraph "Optional Recovery"
            RS[Recovery Share<br/>Encrypted Storage]
            REC[Recovery Service]
        end
    end
    
    subgraph "Secure Execution Environment"
        subgraph "Server-Side TEE"
            TEE[Trusted Execution<br/>Environment]
            SKR[Server Key<br/>Reconstruction]
            STS[Server Transaction<br/>Signing]
        end
    end
    
    subgraph "Key Generation Process"
        KG[Key Generation]
        SSS[Shamir's Secret<br/>Sharing]
        SHARD[Key Sharding]
    end
    
    %% Key generation flow
    WMS --> KG
    KG --> SSS
    SSS --> SHARD
    
    %% Share distribution
    SHARD --> DS
    SHARD --> AS
    SHARD --> RS
    
    %% Authentication flow
    DAPP --> AUTH
    AUTH --> JWT
    JWT --> WMS
    
    %% Client-side signing flow
    DAPP --> KR
    WMS --> AS
    AS --> KR
    DS --> KR
    KR --> TS
    TS --> DAPP
    
    %% Server-side signing flow (alternative)
    WMS --> TEE
    AS --> SKR
    SKR --> STS
    STS --> WMS
    
    %% Policy enforcement
    WMS --> PE
    PE --> KR
    PE --> SKR
    
    %% Recovery flow
    REC --> RS
    RS --> KR
    
    %% Security annotations
    DS -.->|"Never leaves device"| KR
    AS -.->|"Only after auth"| KR
    KR -.->|"Isolated environment"| TS
    
    %% Styling
    classDef device fill:#e3f2fd
    classDef backend fill:#fff3e0
    classDef security fill:#ffebee
    classDef process fill:#f1f8e9
    
    class DAPP,DS,KR,TS device
    class WMS,AS,PE,AUTH,JWT,RS,REC backend
    class TEE,SKR,STS security
    class KG,SSS,SHARD process
```

## 3. Authentication and User Onboarding Flow

```mermaid
sequenceDiagram
    participant U as User
    participant D as dApp
    participant SDK as Privy SDK
    participant AUTH as Auth Service
    participant IDP as Identity Provider
    participant WMS as Wallet Management
    participant SEE as Secure Execution Environment
    participant BC as Blockchain
    
    Note over U,BC: New User Onboarding with Embedded Wallet
    
    U->>D: Access dApp
    D->>SDK: Initialize Privy
    U->>SDK: Choose login method (email/social/etc.)
    
    alt Email Authentication
        SDK->>AUTH: Request email OTP
        AUTH->>U: Send OTP via email
        U->>SDK: Enter OTP
        SDK->>AUTH: Verify OTP
    else Social Authentication
        SDK->>IDP: OAuth request
        IDP->>U: Login prompt
        U->>IDP: Provide credentials
        IDP->>SDK: OAuth token
        SDK->>AUTH: Verify OAuth token
    end
    
    AUTH->>AUTH: Create Privy User Account
    AUTH->>SDK: Issue JWT token
    
    Note over SDK,SEE: Embedded Wallet Creation
    
    SDK->>WMS: Request wallet creation
    WMS->>SEE: Generate key pair
    SEE->>SEE: Apply Shamir's Secret Sharing
    SEE->>SDK: Device Share (encrypted)
    SEE->>WMS: Auth Share (encrypted)
    WMS->>WMS: Store Auth Share
    SDK->>SDK: Store Device Share locally
    
    WMS->>SDK: Return wallet address
    SDK->>D: Wallet ready
    D->>U: Show wallet address & balance
    
    Note over U,BC: Transaction Signing Flow
    
    U->>D: Initiate transaction
    D->>SDK: Request transaction signing
    SDK->>WMS: Transaction request + JWT
    WMS->>WMS: Validate user & policies
    WMS->>SDK: Request Device Share
    SDK->>SEE: Provide Device Share
    WMS->>SEE: Provide Auth Share
    SEE->>SEE: Reconstruct private key
    SEE->>SEE: Sign transaction
    SEE->>SDK: Signed transaction
    SDK->>WMS: Signed transaction
    WMS->>BC: Broadcast transaction
    BC->>WMS: Transaction confirmation
    WMS->>SDK: Transaction result
    SDK->>D: Update UI
    D->>U: Show transaction status
```

## 4. External Wallet Integration Architecture

```mermaid
graph TB
    subgraph "User Environment"
        subgraph "Browser/Mobile"
            DAPP[dApp with Privy SDK]
        end
        
        subgraph "External Wallets"
            MM[MetaMask<br/>Browser Extension]
            WC[WalletConnect<br/>Mobile Wallet]
            CB[Coinbase Wallet]
            PHANTOM[Phantom Wallet]
        end
    end
    
    subgraph "Privy Platform"
        subgraph "SDK Layer"
            SDK[Privy SDK]
            WCI[Wallet Connection<br/>Interface]
            TI[Transaction<br/>Interface]
        end
        
        subgraph "Backend Services"
            EWCS[External Wallet<br/>Connector Service]
            TOS[Transaction<br/>Orchestration Service]
            UMS[User Management<br/>Service]
        end
    end
    
    subgraph "Connection Protocols"
        WCP[WalletConnect<br/>Protocol]
        BEP[Browser Extension<br/>Provider]
        MWP[Mobile Wallet<br/>Protocol]
    end
    
    subgraph "Blockchain Networks"
        ETH[Ethereum]
        SOL[Solana]
        POLY[Polygon]
        OTHER[Other Chains]
    end
    
    %% Wallet connection flows
    DAPP --> SDK
    SDK --> WCI
    
    %% Connection methods
    WCI --> MM
    WCI --> WCP
    WCI --> BEP
    WCI --> MWP
    
    %% Protocol connections
    WCP --> WC
    WCP --> CB
    BEP --> MM
    MWP --> PHANTOM
    
    %% Backend registration
    WCI --> EWCS
    EWCS --> UMS
    
    %% Transaction flows
    SDK --> TI
    TI --> MM
    TI --> WC
    TI --> CB
    TI --> PHANTOM
    
    %% Transaction orchestration
    TI --> TOS
    TOS --> ETH
    TOS --> SOL
    TOS --> POLY
    TOS --> OTHER
    
    %% Metadata storage
    EWCS -.->|"Store wallet metadata"| UMS
    
    %% Styling
    classDef user fill:#e3f2fd
    classDef wallet fill:#fce4ec
    classDef privy fill:#fff3e0
    classDef protocol fill:#f1f8e9
    classDef blockchain fill:#e1f5fe
    
    class DAPP user
    class MM,WC,CB,PHANTOM wallet
    class SDK,WCI,TI,EWCS,TOS,UMS privy
    class WCP,BEP,MWP protocol
    class ETH,SOL,POLY,OTHER blockchain
```

## 5. Security Architecture and Key Sharding

```mermaid
graph TB
    subgraph "Security Layers"
        subgraph "Authentication Layer"
            MFA[Multi-Factor<br/>Authentication]
            JWT[JWT Token<br/>Management]
            OAUTH[OAuth Integration]
            PASSKEY[Passkey/WebAuthn]
        end
        
        subgraph "Key Management Layer"
            subgraph "Shamir's Secret Sharing"
                SSS[Secret Sharing<br/>Algorithm]
                THRESH[Threshold Scheme<br/>2-of-3 or 3-of-5]
            end
            
            subgraph "Key Shares"
                DS[Device Share<br/>Client Storage]
                AS[Auth Share<br/>Backend Storage]
                RS[Recovery Share<br/>Optional]
                BS[Backup Share<br/>Optional]
            end
        end
        
        subgraph "Execution Layer"
            subgraph "Client-Side Security"
                IFRAME[Sandboxed iFrame]
                CSP[Content Security Policy]
                CORS[CORS Protection]
            end
            
            subgraph "Server-Side Security"
                TEE[Trusted Execution<br/>Environment]
                HSM[Hardware Security<br/>Module]
                ENCLAVE[Secure Enclave]
            end
        end
        
        subgraph "Network Layer"
            TLS[TLS Encryption]
            E2E[End-to-End<br/>Encryption]
            RATE[Rate Limiting]
            DDoS[DDoS Protection]
        end
        
        subgraph "Compliance Layer"
            SOC2[SOC 2 Compliance]
            AUDIT[Security Audits]
            BOUNTY[Bug Bounty<br/>Program]
            MONITOR[24/7 Monitoring]
        end
    end
    
    subgraph "Threat Mitigation"
        subgraph "Key Compromise Protection"
            NOSPOF[No Single Point<br/>of Failure]
            DISTRIB[Distributed Key<br/>Storage]
            ROTATION[Key Rotation]
        end
        
        subgraph "Attack Prevention"
            PHISH[Phishing Protection]
            MITM[Man-in-Middle<br/>Prevention]
            REPLAY[Replay Attack<br/>Prevention]
            INJECTION[Injection Attack<br/>Prevention]
        end
    end
    
    %% Security relationships
    SSS --> DS
    SSS --> AS
    SSS --> RS
    SSS --> BS
    
    THRESH --> SSS
    
    DS --> IFRAME
    AS --> TEE
    RS --> HSM
    
    IFRAME --> CSP
    IFRAME --> CORS
    
    TEE --> ENCLAVE
    
    MFA --> JWT
    OAUTH --> JWT
    PASSKEY --> JWT
    
    TLS --> E2E
    RATE --> DDoS
    
    SOC2 --> AUDIT
    AUDIT --> BOUNTY
    BOUNTY --> MONITOR
    
    %% Threat mitigation
    DISTRIB --> NOSPOF
    SSS --> NOSPOF
    
    E2E --> MITM
    JWT --> REPLAY
    CSP --> INJECTION
    MFA --> PHISH
    
    %% Styling
    classDef auth fill:#e3f2fd
    classDef key fill:#fff3e0
    classDef exec fill:#f1f8e9
    classDef network fill:#e8f5e8
    classDef compliance fill:#fce4ec
    classDef threat fill:#ffebee
    
    class MFA,JWT,OAUTH,PASSKEY auth
    class SSS,THRESH,DS,AS,RS,BS key
    class IFRAME,CSP,CORS,TEE,HSM,ENCLAVE exec
    class TLS,E2E,RATE,DDoS network
    class SOC2,AUDIT,BOUNTY,MONITOR compliance
    class NOSPOF,DISTRIB,ROTATION,PHISH,MITM,REPLAY,INJECTION threat
```

## 6. Developer Integration Flow

```mermaid
sequenceDiagram
    participant DEV as Developer
    participant DC as Developer Console
    participant SDK as Privy SDK
    participant DAPP as dApp
    participant PRIVY as Privy Backend
    participant USER as End User
    participant WALLET as Wallet

    Note over DEV,WALLET: Developer Onboarding & Integration

    DEV->>DC: Create Privy account
    DC->>DEV: Provide API keys & app ID
    DEV->>DC: Configure app settings
    DC->>PRIVY: Store app configuration

    Note over DEV,DAPP: SDK Integration

    DEV->>SDK: Install Privy SDK
    DEV->>DAPP: Initialize SDK with API key
    DAPP->>SDK: Configure authentication methods
    SDK->>PRIVY: Validate API key & config

    Note over USER,WALLET: User Interaction Flow

    USER->>DAPP: Access application
    DAPP->>SDK: Trigger authentication
    SDK->>USER: Show login options

    alt New User - Embedded Wallet
        USER->>SDK: Choose email login
        SDK->>PRIVY: Authenticate user
        PRIVY->>SDK: Create embedded wallet
        SDK->>WALLET: Generate wallet
        WALLET->>USER: Wallet ready
    else Existing User - External Wallet
        USER->>SDK: Choose external wallet
        SDK->>WALLET: Connect to MetaMask/etc
        WALLET->>USER: Approve connection
        SDK->>PRIVY: Register connection
    end

    USER->>DAPP: Perform transaction
    DAPP->>SDK: Request transaction
    SDK->>WALLET: Sign transaction
    WALLET->>SDK: Return signed transaction
    SDK->>PRIVY: Submit transaction
    PRIVY->>SDK: Transaction result
    SDK->>DAPP: Update UI
    DAPP->>USER: Show confirmation
```

## 7. Policy Engine and Governance

```mermaid
graph TB
    subgraph "Policy Configuration"
        subgraph "Developer Console"
            DC[Developer Console]
            PC[Policy Configuration]
            RB[Rule Builder]
        end

        subgraph "Policy Types"
            SL[Spending Limits]
            WL[Whitelisted Addresses]
            CL[Contract Limits]
            TL[Time Limits]
            MFA[MFA Requirements]
            MS[Multi-Signature Rules]
        end
    end

    subgraph "Policy Engine"
        subgraph "Rule Evaluation"
            PE[Policy Engine]
            RE[Rule Evaluator]
            CE[Condition Engine]
        end

        subgraph "Enforcement"
            TE[Transaction Evaluator]
            AE[Action Enforcer]
            NE[Notification Engine]
        end

        subgraph "Storage"
            PS[Policy Storage]
            RL[Rule Library]
            AL[Audit Log]
        end
    end

    subgraph "Transaction Flow"
        subgraph "Request Processing"
            TR[Transaction Request]
            TV[Transaction Validation]
            TA[Transaction Approval]
        end

        subgraph "User Interaction"
            UP[User Prompt]
            MV[MFA Verification]
            MS2[Multi-Sig Approval]
        end
    end

    subgraph "Monitoring & Compliance"
        subgraph "Audit Trail"
            AT[Audit Trail]
            CR[Compliance Reports]
            AR[Activity Reports]
        end

        subgraph "Alerts"
            PA[Policy Alerts]
            VA[Violation Alerts]
            SA[Security Alerts]
        end
    end

    %% Configuration flow
    DC --> PC
    PC --> RB
    RB --> SL
    RB --> WL
    RB --> CL
    RB --> TL
    RB --> MFA
    RB --> MS

    %% Policy storage
    PC --> PS
    SL --> PS
    WL --> PS
    CL --> PS
    TL --> PS
    MFA --> PS
    MS --> PS

    %% Rule evaluation
    PS --> PE
    PE --> RE
    RE --> CE

    %% Transaction processing
    TR --> TE
    TE --> PE
    PE --> AE
    AE --> TA

    %% User interaction triggers
    AE --> UP
    UP --> MV
    UP --> MS2
    MV --> TA
    MS2 --> TA

    %% Notifications
    AE --> NE
    NE --> PA
    NE --> VA
    NE --> SA

    %% Audit and compliance
    TE --> AL
    AE --> AL
    AL --> AT
    AT --> CR
    AT --> AR

    %% Rule library
    RE --> RL
    RL --> CE

    %% Styling
    classDef config fill:#e3f2fd
    classDef policy fill:#fff3e0
    classDef transaction fill:#f1f8e9
    classDef monitoring fill:#fce4ec

    class DC,PC,RB,SL,WL,CL,TL,MFA,MS config
    class PE,RE,CE,TE,AE,NE,PS,RL,AL policy
    class TR,TV,TA,UP,MV,MS2 transaction
    class AT,CR,AR,PA,VA,SA monitoring
```

## 8. Multi-Chain Architecture

```mermaid
graph TB
    subgraph "Privy Platform"
        subgraph "Chain Abstraction Layer"
            CAL[Chain Abstraction Layer]
            CA[Chain Adapter]
            CM[Chain Manager]
        end

        subgraph "Wallet Management"
            UWM[Universal Wallet Manager]
            KM[Key Manager]
            AM[Address Manager]
        end

        subgraph "Transaction Processing"
            UTP[Universal Transaction Processor]
            CS[Chain Selector]
            TB[Transaction Builder]
        end
    end

    subgraph "Blockchain Networks"
        subgraph "EVM Compatible"
            ETH[Ethereum]
            POLY[Polygon]
            BSC[Binance Smart Chain]
            AVAX[Avalanche]
            ARB[Arbitrum]
            OP[Optimism]
        end

        subgraph "Non-EVM"
            SOL[Solana]
            BTC[Bitcoin]
            NEAR[NEAR Protocol]
            COSMOS[Cosmos]
            FLOW[Flow]
        end

        subgraph "Layer 2 Solutions"
            MATIC[Polygon PoS]
            STARKNET[StarkNet]
            ZKSYNC[zkSync]
            LOOPRING[Loopring]
        end
    end

    subgraph "Chain-Specific Services"
        subgraph "RPC Providers"
            INFURA[Infura]
            ALCHEMY[Alchemy]
            QUICKNODE[QuickNode]
            ANKR[Ankr]
        end

        subgraph "Indexing Services"
            GRAPH[The Graph]
            MORALIS[Moralis]
            COVALENT[Covalent]
        end

        subgraph "Bridge Protocols"
            WORMHOLE[Wormhole]
            LAYERZERO[LayerZero]
            MULTICHAIN[Multichain]
        end
    end

    %% Chain abstraction
    CAL --> CA
    CA --> CM
    CM --> UWM

    %% Wallet management
    UWM --> KM
    UWM --> AM
    KM --> AM

    %% Transaction processing
    UTP --> CS
    CS --> TB
    TB --> CA

    %% EVM connections
    CA --> ETH
    CA --> POLY
    CA --> BSC
    CA --> AVAX
    CA --> ARB
    CA --> OP

    %% Non-EVM connections
    CA --> SOL
    CA --> BTC
    CA --> NEAR
    CA --> COSMOS
    CA --> FLOW

    %% Layer 2 connections
    CA --> MATIC
    CA --> STARKNET
    CA --> ZKSYNC
    CA --> LOOPRING

    %% RPC provider connections
    ETH --> INFURA
    ETH --> ALCHEMY
    POLY --> QUICKNODE
    SOL --> ANKR

    %% Indexing connections
    ETH --> GRAPH
    POLY --> MORALIS
    BSC --> COVALENT

    %% Bridge connections
    ETH --> WORMHOLE
    POLY --> LAYERZERO
    BSC --> MULTICHAIN

    %% Styling
    classDef privy fill:#e3f2fd
    classDef evm fill:#fff3e0
    classDef nonevm fill:#f1f8e9
    classDef layer2 fill:#fce4ec
    classDef services fill:#e8f5e8

    class CAL,CA,CM,UWM,KM,AM,UTP,CS,TB privy
    class ETH,POLY,BSC,AVAX,ARB,OP evm
    class SOL,BTC,NEAR,COSMOS,FLOW nonevm
    class MATIC,STARKNET,ZKSYNC,LOOPRING layer2
    class INFURA,ALCHEMY,QUICKNODE,ANKR,GRAPH,MORALIS,COVALENT,WORMHOLE,LAYERZERO,MULTICHAIN services
```
